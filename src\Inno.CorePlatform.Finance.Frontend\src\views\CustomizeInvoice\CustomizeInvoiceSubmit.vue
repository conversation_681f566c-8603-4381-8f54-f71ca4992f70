<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>运营提交开票</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crudClassify" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crudClassify.query.searchKey" @search="searchCrudClassify" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crudClassify" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL>
          <inno-split-pane :default-percent="50" split="vertical">
            <template #paneL="{ full, onFull }">
              <inno-crud-operation :crud="crudClassify" border hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs v-model="crudClassify.query.status" class="demo-tabs" @tab-change="ClassifyTabhandleClick">
                    <el-tab-pane :label="`待提交(${classifyTabCount.waitSubmitCount})`" :name="0" lazy />
                    <el-tab-pane :label="`审批中(${classifyTabCount.auditingCount})`" :name="1" lazy />
                    <el-tab-pane :label="`已审批(${classifyTabCount.waitInvoiceCount})`" :name="2" />
                    <el-tab-pane :label="`全部(${classifyTabCount.allCount})`" :name="-1" lazy />
                    <el-tab-pane :label="`我的审批(${classifyTabCount.myAuditCount})`" :name="5000" lazy />
                  </el-tabs>
                </template>
                <template #default>
                  <el-button type="primary"
                             icon="Check"
                             :disabled="!crudClassify.selections.length || crudClassify.rowData.status !== 0"
                             :loading="submitClassfiyLoading"
                             @click="submitClassfiy">提交</el-button>
                  <inno-button-tooltip type="danger" icon="Plus" :disabled="!crudClassify.selections.length" @click="openAttachment_jsqd(crudClassify.selections)">上传结算清单附件</inno-button-tooltip>

                  <el-dropdown trigger="click">
                    <el-button type="primary">
                      更多操作
                      <el-icon class="el-icon--right">
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :disabled="!crudClassify.selections.length ||
        crudClassify.rowData.status !== 0
        " @click="delClassfiy">
                          <el-button type="text" icon="Delete" :disabled="!crudClassify.selections.length ||
        crudClassify.rowData.status !== 0
        ">删除</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="crudClassify.rowData.status !== 1" @click="recallFun">
                          <el-button type="text" icon="RefreshLeft" :disabled="crudClassify.rowData.status !== 1" :ms-disabled="crudClassify.rowDisabled">撤回</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <el-button v-if="crudClassify.rowData.oaRequestId !== null"
                                     icon="View"
                                     content="请选择一条数据"
                                     :disabled="!crudClassify.selections.length"
                                     type="text"
                                     @click="auditProcessClick(crudClassify.rowData)">查看审批过程</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <el-button icon="Download" :loading="batchDownLoading" type="text" @click="batchDownLoad()">批量下载附件</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <el-button icon="Upload" :loading="batchDownLoading" type="text" @click="exportInitCredit()">导入初始应收开票</el-button>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table ref="tableClassifyRef"
                        v-inno-loading="crudClassify.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        border
                        :data="crudClassify.data"
                        stripe
                        :row-class-name="crudClassify.tableRowClassName"
                        @sort-change="crudClassify.sortChange"
                        @selection-change="crudClassify.selectionChangeHandler"
                        @row-click="crudClassify.singleSelection">
                <el-table-column type="selection" fixed="left" width="55"></el-table-column>
                <el-table-column label="单号" min-width="200" property="billCode" fixed="left" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="关联单号" width="200" property="relationCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.relationCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="开票类型" width="90" property="classifyStr" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.classifyStr }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="公司" property="companyName" width="200" show-overflow-tooltip sortable>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.companyId" :crud="crudClassify" :column="column" isInput />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="客户" property="customerName" width="200" sortable show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.customerId" :crud="crudClassify" :column="column" isInput />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="发票数" width="90" property="count" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.count }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="总金额" width="100" property="totalAmount" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.totalAmount" format="0,0.000" />
                  </template>
                </el-table-column>
                <el-table-column label="状态" property="statusStr" width="80" show-overflow-tooltip>
                  <template #default="scope">
                    <el-tag :type="scope.row.status !== 2 ? 'error' : 'success'" disable-transitions>{{scope.row.statusStr }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="销售子系统" width="90" property="saleSystemName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="160" property="remark" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="创建人" width="90" property="createdByName" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.createdByName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" property="createdTime" width="100" show-overflow-tooltip sortable>
                  <template #default="scope">
                    {{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>
                <el-table-column label="结算清单附件" property="attachFileIds" width="100" show-overflow-tooltip fixed="right">
                  <template #default="scope">
                    <el-button type="primary"
                               style="font-size: 12px"
                               v-if="scope.row.attachFileIds"
                               @click.stop="
                        showAttachFile_jsqd(
                          scope.row.attachFileIds,
                          scope.row.id,
                          scope.row.status
                        )
                      ">查看附件</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crudClassify.selections.length }} 条； 金额合计：
                <inno-numeral :value="dataSum(crudClassify.data.map((d) => d.totalAmount))" format="0,0.00" />
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudClassify" />
              </div>
            </template>
            <template #paneR="{ full, onFull }">
              <inno-crud-operation :crud="crud" border hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs v-model="crud.query.status" class="demo-tabs" @tab-change="tabhandleClick">
                    <el-tab-pane :label="`全部(${tabCount.allCount})`" :name="-1" lazy />
                    <el-tab-pane :label="`待开票(${tabCount.waitInvoiceCount})`" name="2" lazy />
                    <el-tab-pane :label="`已开票(${tabCount.invoicedCount})`" name="99" lazy />
                    <el-tab-pane :label="`已作废(${tabCount.cancelCount})`" name="9" lazy />
                  </el-tabs>
                </template>
                <template #default>
                  <inno-button-tooltip v-if="!hasRedInvoiceRelationCode"
                                       type="primary"
                                       :disabled="crudClassify.rowData.status !== 0 && crud.rowData.status !== 0"
                                       :loading="anotherLoading"
                                       icon="Plus"
                                       @click="anotherFun">设置为另一个开票分类</inno-button-tooltip>
                  <inno-button-tooltip type="primary" icon="EditPen" :disabled="crud.rowData.status === 1" :ms-disabled="crud.rowDisabled" @click="editFun">编辑</inno-button-tooltip>
                  <inno-button-tooltip type="primary" icon="EditPen" :ms-disabled="crud.rowData.status" @click="editBacthFun">批量编辑</inno-button-tooltip>
                  <el-dropdown trigger="click">
                    <el-button type="primary">
                      更多操作
                      <el-icon class="el-icon--right">
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :disabled="99 !== crud.rowData.status||crud.rowData.invoiceTotalAmount<0"
                                          :ms-disabled="crud.rowDisabled"
                                          @click="createOffsetInvoice(crud.rowData)">
                          <el-button type="text" icon="Check" :disabled="99 !== crud.rowData.status||crud.rowData.invoiceTotalAmount<0" :ms-disabled="crud.rowDisabled">红冲发票（已有红字确认单号）</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="99 !== crud.rowData.status|| !isDigitalInvoice(crud.rowData.invoiceType) || crud.rowDisabled||crud.rowData.invoiceTotalAmount<0"
                                          :ms-disabled="crud.rowDisabled"
                                          @click="createOffsetInvoiceNonRedComfirmNo(crud.rowData)">
                          <el-button type="text"
                                     icon="Check"
                                     :disabled="99 !== crud.rowData.status || !isDigitalInvoice(crud.rowData.invoiceType) || crud.rowDisabled||crud.rowData.invoiceTotalAmount<0"
                                     :ms-disabled="crud.rowDisabled">红冲发票（暂无红字确认单号）</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="crud.selections.length <= 1" :ms-disabled="crud.rowDisabled" @click="merge(crud.rowData)">
                          <el-button type="text" icon="Position" :disabled="crud.selections.length <= 1" :ms-disabled="crud.rowDisabled">合并申开单</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="crud.rowDisabled" @click="openAttachment(crud.rowData)">
                          <el-button type="text" icon="Plus" :ms-disabled="crud.rowDisabled" :disabled="crud.rowDisabled">上传附件</el-button>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <el-button icon="Download" :loading="batchDownLoading" type="text" @click="exportClick()">导出数据</el-button>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table ref="tableRef"
                        v-inno-loading="crud.loading"
                        class="auto-layout-table"
                        highlight-current-row
                        border
                        :data="crud.data"
                        stripe
                        :row-class-name="crud.tableRowClassName"
                        @sort-change="crud.sortChange"
                        @selection-change="crud.selectionChangeHandler"
                        @row-click="crud.singleSelection">
                <el-table-column type="selection" fixed="left" width="35" />
                <el-table-column label="开票单号" width="200" property="code" fixed="left" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="开票总金额" property="invoiceTotalAmount" width="120" show-overflow-tooltip sortable>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.invoiceTotalAmount" format="0,0.000" />
                  </template>
                </el-table-column>
                <el-table-column label="开票类型" property="invoiceType" width="100" show-overflow-tooltip sortable>
                  <template #default="scope">
                    {{ InvoiceTypeEnum[scope.row.invoiceType - 1]?.name }}
                  </template>
                </el-table-column>
                <el-table-column label="开票状态" property="statusStr" width="80" show-overflow-tooltip>
                  <template #default="scope">
                    <el-tag :type="scope.row.status !== 99 ? 'error' : 'success'" disable-transitions>
                      {{
                      scope.row.statusStr
                      }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="红冲状态" property="changedStatusStr" width="85" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.changedStatusStr }}
                  </template>
                </el-table-column>
                <el-table-column label="红冲单号" property="relationCode" width="200" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.relationCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="申请方" property="redOffsetOptStr" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.redOffsetOptStr }}
                  </template>
                </el-table-column>
                <el-table-column label="红字确认单号" property="redOffsetCode" width="110" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.redOffsetCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="蓝字发票号" property="invoiceNo" width="80" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="蓝字发票代码" property="invoiceCode" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="蓝字红冲金额" property="blueRedInvoiceAmount" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    {{ scope.row.blueRedInvoiceAmount }}
                  </template>
                </el-table-column>
                <el-table-column label="冲红原因" property="redOffsetReasonStr" width="90">
                  <template #default="scope">
                    {{ scope.row.redOffsetReasonStr }}
                  </template>
                </el-table-column>

                <el-table-column label="发票备注" property="remark" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="审批备注" property="approveRemark" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.approveRemark }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="附件" property="attachFileIds" width="100" show-overflow-tooltip fixed="right">
                  <template #default="scope">
                    <el-link style="font-size: 13px" @click="showAttachFile(scope.row.attachFileIds, scope.row.id)">{{ scope.row.attachFileIds ? '查看附件' : '' }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crud.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crud" />
              </div>
            </template>
          </inno-split-pane>
        </template>

        <template #paneR="{ full, onFull }">
          <inno-crud-operation :crud="crud" border hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`开票明细信息`" />
              </el-tabs>
            </template>
            <template #default>
              <template v-if="crud.selections &&
                        crud.selections.length===1 &&
                        crud.selections[0].status <= 0
              ">
                <span style="font-size: 0.8em; padding-right: 10px; color: red">{{ '对开票明细操作后，需保存' }}</span>

                <inno-button-tooltip type="primary" icon="Check" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="Save">保存</inno-button-tooltip>
                <el-dropdown trigger="click" v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode">
                  <el-button type="primary">
                    拆分操作
                    <el-icon class="el-icon--right">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="Share" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="splitFun">按数量拆分</inno-button-tooltip>

                      </el-dropdown-item>  
                      <el-dropdown-item> 
                        <inno-button-tooltip type="text" icon="Share" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="splitFunCount">按份数拆分</inno-button-tooltip>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-dropdown trigger="click" v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode">
                  <el-button type="primary">
                    合并操作
                    <el-icon class="el-icon--right">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" :disabled="crud1.rowData.tag === '折扣行'" @click="MergeByDiscount" v-if="crudClassify.rowData.classify === 1" icon="CaretRight">折扣行合并</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="CaretRight" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="MargeByProductNamePrice">按开票名称、单价合并</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip
                          type="text"
                          icon="CaretRight"
                          :loading="isSubmit"
                          :disabled="crud1.rowData.tag === '折扣行'"
                          @click="MargeByProductNamePriceSpecification"
                        >按开票名称、单价、规格合并</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip
                          type="text"
                          icon="CaretRight"
                          :loading="isSubmit"
                          :disabled="crud1.rowData.tag === '折扣行'"
                          @click="MargeByProductNamePriceOriginPrice"
                        >按开票名称、单价、原价合并</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="CaretRight" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="MargeByProductNoPriceOriginPrice">按原始规格、单价、原价合并</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="CaretRight" :loading="isSubmit" :disabled="crud1.rowData.tag === '折扣行'" @click="MergeByInputQuantityPrice">输入数量、单价合并</inno-button-tooltip>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <inno-button-tooltip
                  type="primary"
                  icon="edit"
                  :loading="isSubmit"
                  :disabled="!crud1.selections.length"
                  @click="UpdateDetailQuantityPrice"
                  v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode"
                >编辑数量、单价</inno-button-tooltip>
                <inno-button-tooltip
                  type="primary"
                  icon="plus"
                  :loading="isSubmit"
                  :disabled="!crud1.selections.length"
                  @click="discount"
                  v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode"
                >新增折扣行</inno-button-tooltip>
                <inno-button-tooltip
                  icon="Setting"
                  type="primary"
                  :loading="isSubmit"
                  :disabled="crud1.rowData.tag === '折扣行'"
                  @click="SetAsAnotherInvoice"
                  v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode"
                >设置为另一个开票单</inno-button-tooltip>
                <inno-button-tooltip
                  icon="Setting"
                  type="primary"
                  :loading="isSubmit"
                  :disabled="crud1.rowData.tag === '折扣行'"
                  @click="BatchSetAsAnotherInvoice"
                  v-if="crudClassify.rowData.classify === 1&&!hasRedInvoiceRelationCode"
                >批量设置开票单</inno-button-tooltip>
                <el-dropdown trigger="click" v-if="crudClassify.rowData.classify === 1">
                  <el-button type="primary">
                    更多操作
                    <el-icon class="el-icon--right">
                      <ArrowDown />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>
                        <inno-button-tooltip
                          type="text"
                          icon="Download"
                          :loading="isSubmit"
                          :disabled="crud1.rowData.tag === '折扣行'"
                          @click="openImportModel"
                          v-if="crudClassify.rowData.classify === 1"
                        >排序导入</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="Close" :disabled="crud1.rowData.tag === '折扣行'" @click="deleteSpec">删除规格</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip type="text" icon="Close" :disabled="crud1.rowData.tag === '折扣行'" @click="deletePackUnit">删除计量单位</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip slot="reference" type="text" icon="Upload" @click="exportDetailClick">导出明细数据</inno-button-tooltip>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <inno-button-tooltip slot="reference" type="text" icon="Refresh" @click="updateSerialNumber">更新排序号</inno-button-tooltip>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <inno-button-tooltip
                  type="success"
                  v-if="crudClassify.rowData.classify !== 2&&crudClassify.rowData.classify !== 3"
                  :loading="syncTaxTypeNoLoading"
                  @click="syncTaxTypeNo"
                >同步税收分类编码</inno-button-tooltip>
              </template>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table id="tableDetail"
                    ref="tableRef1"
                    v-inno-loading="crud1.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    border
                    :key="tableKey"
                    :row-class-name="rowClassNameDetail"
                    :data="crud1.data"
                    @sort-change="handleSortChange"
                    @selection-change="crud1.selectionChangeHandler"
                    @row-click="crud1.singleSelection"
                    style="min-height: 120px;">
            <el-table-column v-if="isShowChecked" type="selection" fixed="left" width="35" />
            <el-table-column fixed="left" property="index" width="100" sortable>
              <template v-slot:header>
                排序号
                <el-popover placement="top-start" :width="200" trigger="hover" content="描述：针对开票明细的排序">
                  <template #reference>
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.sort" @blur="sortBlur(scope.row.sort)" maxlength="50"></el-input>
              </template>
            </el-table-column>
            <el-table-column fixed="left" property="index" width="100" sortable>
              <template v-slot:header>
                分组号
                <el-popover placement="top-start" :width="200" trigger="hover" content="描述：针对批量设置开票单">
                  <template #reference>
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.customizeInvoiceIndex" @blur="customizeInvoiceIndexBlur(scope.row.customizeInvoiceIndex)" maxlength="50"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="原始产品名称" property="originProductName" width="220" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originProductName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="货物或应税劳务服务名称(开票名称)" property="productName" width="280" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.productName" maxlength="200"></el-input>
                <span v-else>{{ scope.row.productName }}</span>
                <span style="display: none">{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原始单位" property="originPackUnit" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originPackUnit }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="计量单位" property="packUnit" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.packUnit" maxlength="50"></el-input>
                <span v-else>{{ scope.row.packUnit }}</span>
                <span style="display: none">{{ scope.row.packUnit }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原始规格" property="originSpecification" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                {{ scope.row.originSpecification }}
              </template>
            </el-table-column>
            <el-table-column label="是否高值" property="ifHighValue" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.ifHighValue == 1 ? '是' : '否' }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" property="specification" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.specification" maxlength="100"></el-input>
                <span v-else>{{ scope.row.specification }}</span>
                <span style="display: none">{{ scope.row.specification }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数量" property="quantity" width="80" show-overflow-tooltip sortable>
              <template #default="scope">
                {{ scope.row.quantity }}
              </template>
            </el-table-column>
            <el-table-column label="原价" property="originalPrice" width="80" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.originalPrice" format="0,0.0000" />
                <span style="display: none">{{ scope.row.originalPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单价" property="price" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.price" format="0,0.0000" />
                <span style="display: none">{{ scope.row.price }}</span>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.value" format="0,0.0000" />
                <span style="display: none">{{ scope.row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column label="税率" property="taxRate" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.taxRate" format="0,0.0000" />
                <span style="display: none">{{ scope.row.taxRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="税额" property="taxAmount" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.taxAmount" format="0,0.0000" />
                <span style="display: none">{{ scope.row.taxAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="价格类型" property="priceSource" width="120" show-overflow-tooltip>
              <template v-slot:header>
                价格类型
                <el-popover placement="top-start" :width="200" trigger="hover" content="描述：任意明细包含集采则是集采类型">
                  <template #reference>
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.priceSourceStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="不含税金额" property="noTaxValue" width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral v-if="isShowChecked" :value="scope.row.noTaxValue" format="0,0.0000" />
                <span style="display: none">{{ scope.row.noTaxValue }}</span>
              </template>
            </el-table-column>
            <el-table-column label="税收分类编码" property="taxTypeNoStr" width="180" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.taxTypeNoStr }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="应收单号" property="creditBillCode" width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="关联单号" property="relateCode" width="150" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" minwidth="180" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="行性质" property="tag" width="80" show-overflow-tooltip>
              <template #default="scope">
                <el-tag :type="scope.row.tag === '折扣行'
        ? 'error'
        : 'success'
        " disable-transitions>
                  {{
                  scope.row.tag === '折扣行'
                  ? '折扣行'
                  : '商品行'
                  }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            共 {{ crud1.data.length }} 条 ； 数量合计：
            <inno-numeral :value="dataSum(crud1.data.map((d) => d.quantity))" />；金额合计：
            <inno-numeral :value="dataSum(crud1.data.map((d) => d.value))" format="0,0.0000" />；不含税金额合计：
            <inno-numeral :value="dataSum(crud1.data.map((d) => d.noTaxValue))" format="0,0.0000" />
            <div class="flex-1" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <el-dialog v-model="dialogVisible"
               :title="(isSubmit ? '提交' : '编辑') + '开票单'"
               width="45%"
               :before-close="cancelSubmit"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <div>
        <div style="float: left; width: 20%; text-align: right">开票类型：</div>
        <el-select v-model="invoiceType" style="float: left; width: 75%; text-align: right" :disabled="crud.selections[0].isNoRedConfirm===1">
          <el-option v-for="item in InvoiceTypeEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <div v-if="redOffsetReason != null || customizeInvoiceValue < 0">
        <div>
          <div style="float: left; width: 20%; text-align: right">申请方：</div>
          <el-select v-model="redOffsetOpter" style="float: left; width: 75%" :disabled="[1, 3, 5].indexOf(invoiceType) > -1">
            <el-option v-for="item in RedOffsetOpterEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </div>
        <div v-show="isNoRedConfirm!=1">
          <div style="clear: both; margin: 0 0 10px 0"></div>
          <div>
            <div style="float: left; width: 20%; text-align: right">红字确认单号：</div>
            <el-input v-model="redOffsetCode" type="text" placeholder="请输入红字确认单号" style="float: left; width: 75%" maxlength="100" :disabled="[1, 3, 5].indexOf(invoiceType) > -1"></el-input>
          </div>
        </div>
        <div style="clear: both; margin: 0 0 10px 0"></div>
        <div>
          <div style="float: left; width: 20%; text-align: right">冲红原因：</div>
          <el-select v-model="redOffsetReason" style="float: left; width: 75%">
            <el-option v-for="item in RedOffsetReasonEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </div>
        <div style="clear: both; margin: 0 0 10px 0"></div>
        <div>
          <div style="float: left; width: 20%; text-align: right">蓝字发票号：</div>
          <el-input v-model="invoiceNo" type="text" placeholder="请输入蓝字发票号" maxlength="100" style="float: left; width: 75%" :disabled="crud.selections[0].isNoRedConfirm===1" />
        </div>
        <div style="clear: both; margin: 0 0 10px 0"></div>
        <div>
          <div style="float: left; width: 20%; text-align: right">蓝字发票代码：</div>
          <el-input v-model="invoiceCode" type="text" placeholder="请输入蓝字发票代码" maxlength="100" style="float: left; width: 75%" :disabled="crud.selections[0].isNoRedConfirm===1" />
        </div>
        <div style="clear: both; margin: 0 0 10px 0"></div>
        <div>
          <div style="float: left; width: 20%; text-align: right">蓝字红冲金额：</div>
          <el-input v-model="blueRedInvoiceAmount" type="number" placeholder="请输入蓝字红冲金额" maxlength="100" style="float: left; width: 75%" :disabled="crud.selections[0].isNoRedConfirm===1" />
        </div>
      </div>

      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">发票备注：</div>
        <el-input v-model="textarea" :rows="5" type="textarea" placeholder="请输入发票备注" maxlength="255" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">审批备注：</div>
        <el-input v-model="approveRemark" :rows="5" type="textarea" placeholder="请输入审批备注" maxlength="255" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="!isSubmit" type="primary" @click="editSubmit">确定</el-button>
          <el-button v-if="isSubmit" type="primary" @click="submitFun2">提交</el-button>
          <el-button @click="cancelSubmit">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible_Detail" title="拆分明细" width="30%" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-form :model="form" label-width="70px">
        <el-form-item label="开票名称:">
          <el-input v-model="textName" :rows="2" type="textarea" />
        </el-form-item>
        <el-form-item label="原始数量:">
          <el-input v-model="oldQty" disabled />
        </el-form-item>
        <el-form-item label="拆分类型:">
          <el-select v-model="spiltClassify" placeholder="请选择退款类型" clearable @change="selectspiltClassify">
            <el-option label="按数量拆分" value="0" />
            <el-option label="按份数拆分" value="1" />
          </el-select>
          <div style="color: red">
            按数量拆分：根据输入的数量N，将该明细拆分为两条明细，且数量为N，和原始数量-N
            <br />按份数拆分：根据输入的份数M，将该明细拆分为M条明细，且数量=原始数量/M（必须整除）
          </div>
        </el-form-item>
        <el-form-item :label="splitLable">
          <el-input v-model="qty" maxlength="17"  @input="handleqty"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="splitSubmit">确定</el-button>
          <el-button @click="dialogVisible_Detail = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 文件上传 -->
    <el-dialog v-model="comfile_upload" title="上传附件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <inno-file-uploader v-model="flieList"
                          list-type="text"
                          drag
                          multiple
                          bizType="finance"
                          fileMode="large"
                          appId="fam"
                          :limitType="[
        'doc',
        'docx',
        'pdf',
        'xls',
        'xlsx',
        'png',
        'jpg',
        'jpeg',
        'gif'
      ]"
                          :folders="folders"
                          :beforeUpload="fileBeforeUpload"
                          :on-success="fileOnSuccess">
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点此上传文件</em>
        </div>
        <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template>-->
      </inno-file-uploader>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosefile">取消</el-button>
          <el-button type="primary" @click="savefile">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 文件查看 -->
    <el-dialog v-model="comfile_show" title="附件查看" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小(b)" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog v-model="redOffsetdialogVisible"
               :title="redOffsetTitle"
               width="85%"
               :before-close="cancelRedOffset"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <div>
        <div style="float: left; width: 20%; text-align: right">申请方：</div>
        <el-select v-model="redOffsetOpter" style="float: left; width: 75%">
          <el-option v-for="item in RedOffsetOpterEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">冲红原因：</div>
        <el-select v-model="redOffsetReason" style="float: left; width: 75%" @change="changeRedOffsetReason">
          <el-option v-for="item in RedOffsetReasonEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div v-show="isNoRedConfirm==0">
        <div style="clear: both; margin: 0 0 10px 0"></div>
        <div>
          <div style="float: left; width: 20%; text-align: right">红字确认单号：</div>
          <el-input v-model="redOffsetCode" type="text" placeholder="请输入红字确认单号" style="float: left; width: 75%" maxlength="100"></el-input>
        </div>
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">蓝字发票号：</div>
        <el-input v-model="invoiceNo" type="text" placeholder="请输入蓝字发票号" maxlength="100" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">蓝字发票代码：</div>
        <el-input v-model="invoiceCode" type="text" placeholder="请输入蓝字发票代码" maxlength="100" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">红冲方式：</div>
        <el-select v-model="redWay" :disabled="redOffsetReason === 2 ? true : false" style="float: left; width: 75%" @change="changeRedWay">
          <el-option v-for="item in RedWayEnum" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div v-if="redWay === 0">
        <div style="float: left; width: 20%; text-align: right">红冲金额：</div>
        <el-input v-model="blueRedInvoiceAmount" type="number" placeholder="请输入蓝字红冲金额" maxlength="100" style="float: left; width: 75%" readonly="readonly" />
      </div>
      <div style="clear: both; margin: 0 0 10px 0"></div>
      <div v-if="redWay === 1">
        <div style="font-weight: bold; margin-bottom: 5px">请勾选需要红冲明细</div>
        <el-table id="tableDetailRed"
                  ref="tableRefRed"
                  v-inno-loading="crudRed.loading"
                  class="auto-layout-table"
                  highlight-current-row
                  border
                  :row-class-name="rowClassNameDetail1"
                  :data="crudRed.data"
                  @sort-change="crudRed.sortChange"
                  @selection-change="crudRed.selectionChangeHandler"
                  @row-click="crudRed.singleSelection">
          <el-table-column type="selection" fixed="left" width="35" />
          <el-table-column fixed="left" property="index" width="100" sortable>
            <template v-slot:header>
              排序号
              <el-popover placement="top-start" :width="200" trigger="hover" content="描述：针对开票明细的排序">
                <template #reference>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </template>
              </el-popover>
            </template>
            <template #default="scope">
              {{scope.row.sort}}
            </template>
          </el-table-column>
          <el-table-column label="货物或应税劳务服务名称(开票名称)" property="productName" minWidth="240" show-overflow-tooltip sortable>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="规格型号" property="specification" width="130" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column label="数量" property="quantity" width="140">
            <template #default="scope">
              <el-input-number v-model="scope.row.quantity"
                               v-if="isNoRedConfirm!==1"
                               :controls="false"
                               style="width: 100%;"
                               :precision="10"
                               @input="(val) => { val < 0 ? (scope.row.quantity = 0) : ''; } "></el-input-number>
              <span v-else>{{ scope.row.quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column label="单价" property="price" width="80" show-overflow-tooltip sortable>
            <template #default="scope">
              <inno-numeral :value="scope.row.price" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column :label="isNoRedConfirm!==0?'红冲金额':'金额'" property="value" width="150" show-overflow-tooltip sortable>
            <template #default="scope">
              <el-input-number v-model="scope.row.value"
                               v-if="isNoRedConfirm!==0"
                               :controls="false"
                               style="width: 100%;"
                               :precision="2"
                               :step="0.01"
                               :min="0"
                               onkeypress="return( /[\d.]/.test(String.fromCharCode(event.keyCode)))"
                               :max="scope.row.invoiceAmount"
                               placeholder="请输入红冲金额"></el-input-number>
              <span v-else>{{ scope.row.value }}</span>
            </template>
          </el-table-column>
          <el-table-column label="可红冲金额" property="redQuantity" width="130" show-overflow-tooltip sortable>
            <template #default="scope">
              <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
            </template>
          </el-table-column>

          <el-table-column label="税率" property="taxRate" width="70" show-overflow-tooltip>
            <template #default="scope">
              <inno-numeral :value="scope.row.taxRate" format="0,0.0000" />
            </template>
          </el-table-column>
          <el-table-column label="应收单号" property="creditBillCode" minWidth="240" show-overflow-tooltip sortable>
            <template #default="scope">
              <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
            </template>
          </el-table-column>

          <el-table-column label="行性质" property="tag" width="80" show-overflow-tooltip>
            <template #default="scope">
              <el-tag :type="scope.row.tag === '折扣行'
        ? 'error'
        : 'success'
        " disable-transitions>
                {{
                scope.row.tag === '折扣行'
                ? '折扣行'
                : '商品行'
                }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div class="app-page-footer background">
          共 {{ crudRed.data.length }} 条 ； 数量合计：
          <inno-numeral :value="dataSum(crudRed.data.map((d) => d.quantity))" format="0,0.0000" />；
          可红冲金额合计：
          <inno-numeral :value="dataSum(crudRed.data.map((d) => d.invoiceAmount))" format="0,0.0000" />；
          <div class="flex-1" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="!isSubmit" type="primary" @click="createOffsetInvoiceOpt">确定</el-button>
          <el-button @click="cancelRedOffset">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 上传结算清单附件 -->
    <el-dialog v-model="comfile_upload_jsqd" title="上传结算清单附件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <inno-file-uploader v-model="flieList_jsqd"
                          list-type="text"
                          drag
                          multiple
                          bizType="finance"
                          fileMode="large"
                          appId="fam"
                          :limitType="[
        'doc',
        'docx',
        'pdf',
        'xls',
        'xlsx',
        'png',
        'jpg',
        'jpeg',
        'gif'
      ]"
                          :folders="folders"
                          :beforeUpload="fileBeforeUpload"
                          :on-success="fileOnSuccess">
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点此上传文件</em>
        </div>
        <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template>-->
      </inno-file-uploader>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosefile">取消</el-button>
          <el-button type="primary" @click="savefile_jsqd">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 查看结算清单附件 -->
    <el-dialog v-model="comfile_show_jsqd" title="查看结算清单附件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-table :data="showfiles_jsqd" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">{{ format(scope.row.length) }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile_jsqd(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--提交前信息确认 -->
    <el-dialog v-model="show_beforeSumitdialog"
               title="开票主体信息确认"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable
               :before-close="cancel_beforeSumitdialog">
      <el-table ref="beforeRef"
                :data="data_beforeSumitdialog"
                stripe
                style="width: 100%"
                highlight-current-row
                @selection-change="data_beforeSelection"
                @row-click="data_beforeSingleSelection">
        <el-table-column fixed="left" width="55">
          <template #default="scope">
            <inno-table-checkbox :checked="scope.row.key === organization[0].key" />
          </template>
        </el-table-column>
        <el-table-column prop="invoiceBank" label="开户账号">
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.invoiceBank }} {{ scope.row.invoiceBankNo }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceName" label="机构名称" />
        <el-table-column prop="invoiceAddr" label="发票地址" />
        <el-table-column prop="invoiceCode" label="发票税号" />
        <el-table-column prop="salesInvoiceDetailsDesc" label="开票类型" />
      </el-table>
      <el-checkbox v-model="isPushDefaultEmail" @change="pushDefaultEmailChange">是否推送开票人邮箱</el-checkbox>
      <el-checkbox v-model="isPushCustomerEmail" @change="pushCustomerEmailChange">是否推送客户邮箱</el-checkbox>
      <el-input v-if="isPushCustomerEmail" v-model="customerEmail" v-email-validator clearable placeholder="多个邮箱使用分号隔开，最多支持两个邮箱" @change="validateEmail"></el-input>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel_beforeSumitdialog">取消</el-button>
          <el-button type="primary" @click="submit_beforeSumitdialog">确认并提交</el-button>
        </span>
      </template>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
    <!-- 导入初始应收开票 -->
    <!-- gatewayUrl + 'v1.0/finance-backend -->
    <!-- 'http://localhost:6211 -->
    <excel-import v-model:visibel="ExcelImportVisibel"
                  title="按Excel导入初始应收开票"
                  :action="gatewayUrl + 'v1.0/finance-backend/api/CustomizeInvoice/ExportInitCredit'"
                  :tipStyle="{ color: 'red' }"
                  tip="提示: 导入初始应收开票"
                  @submitSuccess="handleSuccess">
      <template v-slot:importTemplate>
        <a href="https://static.innostic.com/template/初始应收开票明细导入模板.xlsx?v=1.2" style="color: red">下载导入初始应收开票模板</a>
      </template>
    </excel-import>
    <DetailImport ref="ImportDetail" @onImportSuccess="onImportSuccess"></DetailImport>
    <el-dialog v-model="dialogVisibleEditBacthFun"
               :title="'批量编辑开票单'"
               width="40%"
               :before-close="cancelSubmit"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <div>
        <div style="float: left; width: 20%; text-align: right">发票备注：</div>
        <el-input v-model="textarea" :rows="5" type="textarea" placeholder="请输入发票备注" maxlength="255" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">审批备注：</div>
        <el-input v-model="approveRemark" :rows="5" type="textarea" placeholder="请输入审批备注" maxlength="255" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="editBatchSubmit">确定</el-button>
          <el-button @click="cancelSubmit">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleMergeDetailInfo"
               :title="'输入数量、单价合并开票明细'"
               width="400"
               :before-close="MergeByInputQuantityPriceCancel"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <div>
        <div style="float: left; width: 20%; text-align: right">数量：</div>
        <el-input v-model="mergeQuantity" type="text" placeholder="请输入数量" @input="handleMergeQuantity" maxlength="20" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">单价：</div>
        <el-input v-model="mergePrice" type="text" placeholder="请输入单价" @input="handleMergePrice" maxlength="16" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="MergeByInputQuantityPriceOpt">确定</el-button>
          <el-button @click="MergeByInputQuantityPriceCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleEditDetailInfo"
               :title="'输入数量、单价编辑开票明细'"
               width="400"
               :before-close="UpdateDetailQuantityPriceCancel"
               :close-on-click-modal="false"
               :destroy-on-close="true"
               modal-class="position-fixed"
               draggable>
      <div>
        <div style="float: left; width: 20%; text-align: right">数量：</div>
        <el-input v-model="editQuantity" type="text" placeholder="请输入数量" @input="handleInputQuantity" maxlength="20" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <div>
        <div style="float: left; width: 20%; text-align: right">单价：</div>
        <el-input v-model="editPrice" type="text" placeholder="请输入单价" @input="handleInputPrice" maxlength="16" style="float: left; width: 75%" />
      </div>
      <div style="clear: both; margin: 0 0 5px 0"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="UpdateDetailQuantityPriceOpt">确定</el-button>
          <el-button @click="UpdateDetailQuantityPriceCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="tsx" setup>
  import {
    ref,
    onBeforeMount,
    onMounted,
    onActivated,
    computed,
    watch,
    reactive,
    nextTick
  } from 'vue';
  import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
  import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
  import {
    ElTable,
    ElForm,
    ElMessage,
    ElMessageBox,
    ElLoading
  } from 'element-plus';

  import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
  import request from '@/utils/request';
  import { v4 as uuidv4 } from 'uuid';
  import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
  import approveProcess from '@/component/ApproveProcess.vue';
  // 引入导出Excel表格依赖
  import * as FileSaver from 'file-saver';
  import * as XLSX from 'xlsx';
  import { useRouter, useRoute } from 'vue-router';
  import {
    downloadFilesViaIdsAsZip
  } from '@inno/inno-mc-vue3/lib/components/fileUploader';
  import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
  import DetailImport from './components/detailImport.vue';
  const tableRef = ref < InstanceType < typeof ElTable >> ();
  const tableKey = ref(0);
  const approveProcessRef = ref(null);
  const isPushCustomerEmail = ref(false);
  const customerEmail = ref('');
  const ImportDetail = ref();
  const isEmailValid = ref(false);
  import { Decimal } from 'decimal.js';
  //是否推送客户邮箱
  const pushCustomerEmailChange = () => {
    if (isPushCustomerEmail.value) {
      //获取客户邮箱
      if (crudClassify.selections[0].customerEmail) {
        customerEmail.value = crudClassify.selections[0].customerEmail;
      } else {
        request({
          url: '/api/CustomizeInvoice/GetCustomerEmail',
          data: {
            customerId:
              crudClassify.selections.length > 0 ? crudClassify.selections[0].customerId : ''
          },
          method: 'post'
        }).then((res) => {
          customerEmail.value = res.data.data;
        });
      }
    } else {
      customerEmail.value = '';
    }
  }
  // 是否推送开票人邮箱
  const isPushDefaultEmail = ref(true);
  const pushDefaultEmailChange = () => {

  }

  const handleInputQuantity = (value) => {
    // 正则表达式匹配最多10位小数的数字，包括整数部分和小数点
    editQuantity.value = value.replace(/[^-?\d.]/g, '').replace(/(\..*)\./g, '$1'); // 移除多余的点
    editQuantity.value = editQuantity.value.replace(/^(-?)(\d+)(\.(\d{0,10}))(.*)$/, "$1$2$3"); // 限制小数点后最多10位
  }
  const handleInputPrice = (value) => {
    // 正则表达式匹配最多4位小数的数字，包括整数部分和小数点
    editPrice.value = value.replace(/[^?\d.]/g, '').replace(/(\..*)\./g, '$1'); // 移除多余的点
    editPrice.value = editPrice.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3'); // 限制小数点后最多10位
  }
  const handleMergeQuantity = (value) => {
    // 正则表达式匹配最多10位小数的数字，包括整数部分和小数点
    mergeQuantity.value = value.replace(/[^-?\d.]/g, '').replace(/(\..*)\./g, '$1'); // 移除多余的点
    mergeQuantity.value = mergeQuantity.value.replace(/^(\-)*(\d+)\.(\d\d\d\d\d\d\d\d\d\d).*$/, '$1$2.$3'); // 限制小数点后最多10位
  }
  const handleqty = (value) => {
    // 正则表达式匹配最多10位小数的数字，包括整数部分和小数点
    qty.value = value.replace(/[^-?\d.]/g, '').replace(/(\..*)\./g, '$1'); // 移除多余的点
    qty.value = qty.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 限制小数点后最多10位
  }
  const handleMergePrice = (value) => {
    // 正则表达式匹配最多4位小数的数字，包括整数部分和小数点
    mergePrice.value = value.replace(/[^?\d.]/g, '').replace(/(\..*)\./g, '$1'); // 移除多余的点
    mergePrice.value = mergePrice.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3'); // 限制小数点后最多10位
  }
  //校验邮箱
  const validateEmail = () => {
    if (isPushCustomerEmail.value) {
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      let parts = customerEmail.value.split(";");
      if (parts.length > 2) {
        isEmailValid.value = false;
      } else if (parts.length > 1) {
        var p1 = emailRegex.test(parts[0]);
        var p2 = emailRegex.test(parts[1]);
        if (p1 && p2) {
          isEmailValid.value = true;
        } else {
          isEmailValid.value = false;
        }
      } else {
        isEmailValid.value = emailRegex.test(customerEmail.value);
      }
    } else {
      isEmailValid.value = true;
    }
  }
  const crud = CRUD(
    {
      title: '开票单列表',
      url: '/api/CustomizeInvoice/GetCustomizeInvoiceItem',
      method: 'post',
      idField: 'id',
      userNames: ['createdBy'],
      query: { status: '-1' },
      resultKey: {
        list: 'list',
        total: 'total'
      },
      optShow: {
        exportCurrentPage: false // 为false则不会显示按钮
      },
      props: {
        // 默认隐藏搜索
        searchToggle: true
      },
      hooks: {
        [CRUD.HOOK.afterRefresh]: () => {
          //默认选中第一行
          if (crud.data.length && crud.data.length > 0) {
            crud.singleSelection(crud.data[0]);
          }
          loadItemTableData();
        }
      },
      tablekey: 'tablekey'
    },
    {
      table: tableRef
    }
  );
  const tabhandleClick = () => {
    crud.toQuery();
  };
  const rowClassNameDetail = (row) => {
    if (
      ((row.row.value === 0 || row.row.originalPrice != row.row.price) && row.row.quantity != 0)
    ) {
      return 'warning-row';
    }
    return crud1.tableRowClassName(row);
  };
  const rowClassNameDetail1 = (row) => {
    if (
      ((row.row.value === 0 || row.row.originalPrice != row.row.price) && row.row.quantity != 0)
    ) {
      return 'warning-row';
    }
    return crudRed.tableRowClassName(row);
  };
  const ClassifyTabhandleClick = () => {
    crudClassify.toQuery();
  };
  let tabCount = ref({
    waitSubmitCount: 0,
    auditingCount: 0,
    waitInvoiceCount: 0,
    invoicedCount: 0,
    allCount: 0,
    myAuditCount: 0,
    cancelCount: 0
  });
  const loadItemTableData = () => {
    request({
      url: '/api/CustomizeInvoice/GetTabCount',
      data: {
        status: crudClassify.query.status, //'-1',
        customizeInvoiceClassifyId:
          crudClassify.selections.length > 0 ? crudClassify.selections[0].id : ''
      },
      method: 'post'
    }).then((res) => {
      tabCount.value = res.data.data;
    });
  };

  let classifyTabCount = ref({
    waitSubmitCount: 0,
    auditingCount: 0,
    waitInvoiceCount: 0,
    invoicedCount: 0,
    allCount: 0,
    myAuditCount: 0,
    cancelCount: 0
  });
  const loadClassifyTableData = () => {
    request({
      url: '/api/CustomizeInvoice/GetClassifyTabCount',
      data: {
        status: '0',
        ...crudClassify.query
      },
      method: 'post'
    }).then((res) => {
      classifyTabCount.value = res.data.data;
    });
  };

  //开票明细
  const tableRef1 = ref < InstanceType < typeof ElTable >> ();
  const crud1 = CRUD(
    {
      title: '开票明细信息',
      url: '/api/CustomizeInvoice/GetCustomizeInvoiceDetail',
      method: 'post',
      idField: 'id',
      userNames: ['createdBy'],
      query: {},
      resultKey: {
        list: 'list',
        total: 'total'
      },
      hooks: {
        //   [CRUD.HOOK.afterRefresh]: () => {
        //     //默认选中第一行
        //     if (crud.data.length && crud.data.length > 0) {
        //       crud.singleSelection(crud.data[0]);
        //     }
        //   },
      },
      tablekey: 'tablekey1'
    },
    {
      table: tableRef1
    }
  );

  //红冲明细
  const tableRefRed = ref < InstanceType < typeof ElTable >> ();
  const crudRed = CRUD(
    {
      title: '开票明细信息',
      url: '/api/CustomizeInvoice/GetCustomizeInvoiceDetail',
      method: 'post',
      idField: 'id',
      userNames: ['createdBy'],
      query: {},
      resultKey: {
        list: 'list',
        total: 'total'
      },
      hooks: {},
      tablekey: 'tablekeyred'
    },
    {
      table: tableRefRed
    }
  );

  //开票分类
  const tableClassifyRef = ref < InstanceType < typeof ElTable >> ();
  const crudClassify = CRUD(
    {
      title: '开票分类信息',
      url: '/api/CustomizeInvoice/GetCustomizeInvoiceClassfiy',
      method: 'post',
      idField: 'id',
      userNames: ['createdBy'],
      query: { status: 0 },
      resultKey: {
        list: 'list',
        total: 'total'
      },
      optShow: {
        exportCurrentPage: false // 为false则不会显示按钮
      },
      props: {
        // 默认隐藏搜索
        searchToggle: true
      },
      hooks: {
        [CRUD.HOOK.afterRefresh]: () => {
          //默认选中第一行
          if (crudClassify.data.length && crudClassify.data.length > 0) {
            crudClassify.singleSelection(crudClassify.data[0]);
          } else {
            // 无数据清除上次关联id值
            crudClassify.selections = [];
            customizeInvoiceClassifyId.value = '';
            crud.query.customizeInvoiceClassifyId = '';
            loadItemTableData();
          }
          loadClassifyTableData();
        }
      },
      tablekey: 'tableClassify'
    },
    {
      table: tableClassifyRef
    }
  );

  //删除分类
  const delClassfiy = () => {
    if (crudClassify.selections && crudClassify.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要删除的发票分类',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      if (currUserName.value !== crudClassify.selections[0].createdBy) {
        ElMessage({
          showClose: true,
          message: '只有创建人才能操作删除!',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      ElMessageBox.confirm(
        '此操作将删除发票分类以及关联的发票单数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        request({
          url: `/api/CustomizeInvoice/DeleteCustomizeInvoiceClassify`,
          method: 'POST',
          data: { classifyOutput: crudClassify.selections[0] }
        })
          .then((res) => {
            if (res.data.code === 200) {
              crudClassify.toQuery();
              ElMessage({
                showClose: true,
                message: '删除成功!',
                type: 'success',
                duration: 3 * 1000
              });
            } else {
              ElMessage({
                showClose: true,
                message: res.data.msg != null ? res.data.msg : res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          });
      });
    }
  };
  const submitClassfiyLoading = ref(false);
  const submitClassfiy = () => {
    console.info(crud1.data)
    console.info('x', crud)
    var highvalueMap = new Map();
    if (crud1.data === null || crud1.data.length > 0) {
      crud1.data.forEach((c2) => {
        if (!highvalueMap.has(c2.ifHighValue)) {
          highvalueMap.set(c2.ifHighValue, 1)
        }
      })
    }

    console.log(highvalueMap)
    if (highvalueMap.has(1) && (highvalueMap.has(null) || highvalueMap.has(0))) {
      var nos = [];
      for (var i = 0; i < crud.data.length; i++) {
        nos.push(crud.data[i].code);
      }
      ElMessageBox.confirm(
        '您的开票单号' + nos.join(',') + '中同时存在高值和低值明细，是否确认提交?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          if (crudClassify.selections && crudClassify.selections.length == 0) {
            ElMessage({
              showClose: true,
              message: '请选择要操作的发票分类',
              type: 'error',
              duration: 3 * 1000
            });
          } else {
            if (crudClassify.selections[0].companyName === '昆明致新康德医疗供应链管理有限公司') {
              ElMessageBox.confirm(
                '系统已自动生成发票备注，请检查开票主体信息是否与发票备注中一致',
                '提示',
                {
                  confirmButtonText: '已确认一致',
                  cancelButtonText: '取消提交',
                  type: 'warning'
                }
              ).then(() => {
                submitClassfiyLoading.value = true;
                // 查询发票详情
                console.log(crudClassify);
                beforeSubmitOpt();
              })
            }
            else {
              submitClassfiyLoading.value = true;
              // 查询发票详情
              console.log(crudClassify);
              beforeSubmitOpt();
            }
          }
        }).catch(() => {

        })
      return
    }
    if (crudClassify.selections && crudClassify.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要操作的发票分类',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      if (crudClassify.selections[0].companyName === '昆明致新康德医疗供应链管理有限公司') {
        ElMessageBox.confirm(
          '系统已自动生成发票备注，请检查开票主体信息是否与发票备注中一致',
          '提示',
          {
            confirmButtonText: '已确认一致',
            cancelButtonText: '取消提交',
            type: 'warning'
          }
        ).then(() => {
          submitClassfiyLoading.value = true;
          // 查询发票详情
          console.log(crudClassify);
          beforeSubmitOpt();
        })
      }
      else {
        submitClassfiyLoading.value = true;
        // 查询发票详情
        console.log(crudClassify);
        beforeSubmitOpt();
      }
    }
  };
  const show_beforeSumitdialog = ref(false);
  const data_beforeSumitdialog = ref([]);
  const beforeRef = ref < InstanceType < typeof ElTable >> ();
  const beforeSubmitOpt = () => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    // 清空数据
    data_beforeSumitdialog.value = [];
    if (crudClassify.selections[0].creditSaleSubType === 1) {
      const salesOrderNos = [...new Set(crud1.data.map(item => item.relateCode))];
      const newArray = salesOrderNos.flatMap(item => item.split(","));
      request({
        url: `${window.gatewayUrl}v1.0/ic-backend/api/WangDianTong/trade/invoice`,
        method: 'POST',
        data: { classifyOutput: crudClassify.selections[0], salesOrderNos: newArray }
      })
        .then((res) => {
          if (res.data.code === 200) {
            show_beforeSumitdialog.value = true;
            data_beforeSumitdialog.value = res.data.data.map((v, i) => {
              v.key = i
              return v
            });
            nextTick(x => {
              // 默认选择开票主体
              let index = res.data.data.findIndex(item => item.companyNames !== '' && item.companyNames !== null && item.companyNames.includes(crudClassify.rowData.companyName));
              if (index === -1) {
                index = 0;
              }
              beforeRef.value?.toggleRowSelection(data_beforeSumitdialog.value[index])
            });
            submitClassfiyLoading.value = false;
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
            submitClassfiyLoading.value = false;
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
          submitClassfiyLoading.value = false;
        }).finally(() => {
          loading.close();
        });
    } else {
      request({
        url: `/api/CustomizeInvoice/BeforeSubmitClassfiy`,
        method: 'POST',
        data: { classifyOutput: crudClassify.selections[0] }
      })
        .then((res) => {
          if (res.data.code === 200) {
            show_beforeSumitdialog.value = true;
            data_beforeSumitdialog.value = res.data.data.map((v, i) => {
              v.key = i
              return v
            });
            nextTick(x => {
              // 默认选择开票主体
              let index = res.data.data.findIndex(item => item.companyNames !== '' && item.companyNames !== null && item.companyNames.includes(crudClassify.rowData.companyName));
              if (index === -1) {
                index = 0;
              }
              beforeRef.value?.toggleRowSelection(data_beforeSumitdialog.value[index])
            });
            submitClassfiyLoading.value = false;
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
            submitClassfiyLoading.value = false;
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
          submitClassfiyLoading.value = false;
        }).finally(() => {
          loading.close();
        });
    }

  };
  // 立即提交
  const submit_beforeSumitdialog = () => {
    validateEmail();
    if (isPushCustomerEmail.value && !isEmailValid.value) {
      ElMessage({
        showClose: true,
        message: '客户邮箱校验失败，请输入有效的邮箱地址（最多支持输入两个邮箱）',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    if (organization.value.length !== 1) {
      ElMessage({
        showClose: true,
        message: '只能选择一个开票主体提交',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    if (
      crudClassify.selections[0].attachFileIds === null ||
      crudClassify.selections[0].attachFileIds.length == 0
    ) {
      ElMessageBox.confirm('该开票数据没有上传结算清单, 是否继续?', '信息确认', {
        dangerouslyUseHTMLString: true, // 注意此属性
        confirmButtonText: '继续并提交',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        submitClassfiyOpt();
      });
    } else {
      submitClassfiyOpt();
    }
  };
  // 取消
  const cancel_beforeSumitdialog = () => {
    show_beforeSumitdialog.value = false;
    isPushCustomerEmail.value = false;
    customerEmail.value = '';
    data_beforeSumitdialog.value = [];
  };
  // 提交开票主体信息
  const organization = ref([]);
  const submitClassfiyOpt = () => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    var customerInvoice = organization.value[0];
    if (customerInvoice.invoiceAddr === null) {
      customerInvoice.invoiceAddr = '';
    }
    if (customerInvoice.invoiceTel === null) {
      customerInvoice.invoiceTel = '';
    }
    if (customerInvoice.invoiceBank === null) {
      customerInvoice.invoiceBank = '';
    }
    if (customerInvoice.invoiceBankNo === null) {
      customerInvoice.invoiceBankNo = '';
    }
    if (customerInvoice.invoiceCode === null) {
      customerInvoice.invoiceCode = '';
    }
    var data = {
      classifyOutput: crudClassify.selections[0],
      customerEmail: customerEmail.value,
      customerInvoice: organization.value[0],
      isPushDefaultEmail: isPushDefaultEmail.value
    }
    request({
      url: `/api/CustomizeInvoice/SubmitClassfiy`,
      method: 'POST',
      data: data
    })
      .then((res) => {
        if (res.data.code === 200) {
          crudClassify.toQuery();
          cancel_beforeSumitdialog();
          ElMessage({
            showClose: true,
            message: '提交成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
        loading.close();
        cancel_beforeSumitdialog();
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
        loading.close();
        cancel_beforeSumitdialog();
      });
  };
  //高级查询
  const queryList = computed(() => [
    {
      key: 'billCode',
      label: '单号',
      show: true
    },
    {
      key: 'relationCodeOfClassify',
      label: '关联单号',
      show: true
    },
    {
      key: 'customizeInvoiceItemCode',
      label: '开票单号',
      show: true
    },
    {
      key: 'customerId',
      label: '客户',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' },
      show: true
    },
    {
      key: 'companyId',
      label: '公司',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
      labelK: 'name',
      valueK: 'id',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { functionUri: 'metadata://fam' }
      },
      show: true
    },
    {
      key: 'invoiceStatus',
      label: '开票状态',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: [
        {
          name: '待开票',
          id: 2
        },
        {
          name: '已开票',
          id: 99
        },
        {
          name: '已作废',
          id: 9
        }
      ],
      show: true
    },
    {
      key: 'classify',
      label: '开票类型',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: [
        {
          name: '应收',
          id: 1
        },
        {
          name: '预开票',
          id: 2
        },
        {
          name: '初始应收',
          id: 3
        }
      ],
      show: true
    },
    {
      key: 'relationCode',
      label: '红冲单号',
      show: true
    },
    {
      key: 'creditCode',
      label: '应收单号',
      show: true
    },
    {
      key: 'saleSystemName',
      label: '销售子系统',
      show: true
    },
    {
      key: 'createdBy',
      label: '创建人',
      multiple: true,
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
      placeholder: '用户名称搜索',
      valueK: 'name',
      labelK: 'displayName',
      props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
      slots: {
        option: ({ item }) => (
          <>
            <span>{item.displayName}</span>
            <span style="float:right">{item.name}</span>
          </>
        )
      }
    },
    {
      key: 'billDateFrom',
      endDate: 'billDateTo',
      label: '制单时间',
      type: 'daterange',
      valueFormat: 'yyyy-MM-dd',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ]
    },
    {
      key: 'orderNo',
      label: '订单号',
      show: true
    },
    {
      key: 'isAttachment',
      label: '是否上传附件',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: [
        {
          name: '是',
          id: 1
        },
        {
          name: '否',
          id: 0
        },
      ],
      show: true
    }
  ]);
  const queryObject = computed(() =>
    Object.fromEntries(queryList.value.map((item) => [item.key, item]))
  );

  let currUserName = ref('');
  const route = useRoute();
  onMounted(() => {
    // 表头拖拽必须在这里执行
    tableDrag(tableRef);
    tableDrag(tableClassifyRef);
    currUserName.value = window.userName;
    crudClassify.toQuery();
  });

  //监听 crudClassify.rowData.id
  watch(
    () => crudClassify.rowData.id,
    (n, o) => {
      if (n != null) {
        crud.query.status = crudClassify.query.status; //全部
        crud.query.customizeInvoiceClassifyId = crudClassify.rowData.id;
        crud.toQuery();
      } else {
        customizeInvoiceClassifyId.value = '';
        crud.data = [];
        crud.page.total = 0;
        crud1.data = [];
        crud1.page.total = 0;
      }
    },
    { deep: true }
  );
  //监听 crud.rowData.id
  watch(
    () => crud.rowData.id,
    (n, o) => {
      if (n != null) {
        crud1.query = { customizeInvoiceItemId: crud.rowData.id, limit: 5000 };
        crud1.toQuery();
      } else {
        crud1.data = [];
        crud1.page.total = 0;
      }
    },
    { deep: true }
  );

  const changeRedWay = () => {
    crudRed.query = {
      customizeInvoiceItemId: crud.rowData.id,
      Opt: 2,
      limit: 5000
    };
    crudRed.toQuery();
  };

  //数组求和
  const dataSum = (arr) => {
    var s = 0;
    arr.forEach((val) => {
      s = new Decimal(s).add(parseFloat(val));
    }, 0);
    return s;
  };
  const form = reactive({});
  const dialogVisible = ref(false);
  const invoiceType = ref(1);
  const textarea = ref('');
  const approveRemark = ref('');
  const isSubmit = ref(false);
  //编辑
  const editFun = () => {
    if (crud.selections && crud.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要编辑的开票单',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      // if (currUserName.value !== crud.selections[0].createdBy) {
      //   ElMessage({
      //     showClose: true,
      //     message: '只有创建人才能操作编辑!',
      //     type: 'error',
      //     duration: 3 * 1000
      //   });
      //   return;
      // }
      dialogVisible.value = true;

      invoiceType.value = crud.selections[0].invoiceType === 0 ? 1 : crud.selections[0].invoiceType;
      textarea.value = crud.selections[0].remark;
      approveRemark.value = crud.selections[0].approveRemark;
      isNoRedConfirm.value = crud.selections[0].isNoRedConfirm;
      customizeInvoiceValue.value = crud.selections[0].invoiceTotalAmount;
      redOffsetCode.value = crud.selections[0].redOffsetCode;
      invoiceNo.value = crud.selections[0].invoiceNo;
      invoiceCode.value = crud.selections[0].invoiceCode;
      blueRedInvoiceAmount.value = crud.selections[0].blueRedInvoiceAmount;
      redOffsetOpter.value = crud.selections[0].redOffsetOpter;
      redOffsetReason.value = crud.selections[0].redOffsetReason;
      isSubmit.value = false;
    }
  };
  const dialogVisibleEditBacthFun = ref(false);

  //编辑开票 确定
  const editSubmit = () => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    var pushData = {
      customizeInvoiceItemId: crud.selections[0].id,
      invoiceType: invoiceType.value,
      remark: textarea.value,
      approveRemark: approveRemark.value,

      redOffsetCode: redOffsetCode.value,
      invoiceNo: invoiceNo.value,
      invoiceCode: invoiceCode.value,
      blueRedInvoiceAmount: blueRedInvoiceAmount.value,
      redOffsetOpter: redOffsetOpter.value,
      redOffsetReason: redOffsetReason.value
    };
    request({
      url: `/api/CustomizeInvoice/EditCustomizeInvoice`,
      method: 'POST',
      data: pushData
    })
      .then((res) => {
        loading.close();
        if (res.data.code === 200) {
          dialogVisible.value = false;
          textarea.value = '';
          approveRemark.value = '';
          crud.toQuery();
          ElMessage({
            showClose: true,
            message: '编辑成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch(() => {
        loading.close();
      });
  };
  //批量编辑
  const editBacthFun = () => {
    if (crud.selections && crud.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要编辑的开票单',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      // if (currUserName.value !== crud.selections[0].createdBy) {
      //   ElMessage({
      //     showClose: true,
      //     message: '只有创建人才能操作编辑!',
      //     type: 'error',
      //     duration: 3 * 1000
      //   });
      //   return;
      // }
      if (crud.selections.filter(n => n.status == 0).length != crud.selections.length) {
        ElMessage({
          showClose: true,
          message: '请选择全部是待提交状态下面的数据！',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      dialogVisibleEditBacthFun.value = true;
      textarea.value = crud.selections[0].remark;
      approveRemark.value = crud.selections[0].approveRemark;
      isSubmit.value = false;
    }
  };

  //编辑开票 确定
  const editBatchSubmit = () => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    let customizeInvoiceItemIds = crud.selections.map((s) => s.id);
    var pushData = {
      customizeInvoiceItemIds: customizeInvoiceItemIds,
      remark: textarea.value,
      approveRemark: approveRemark.value,
    };
    request({
      url: `/api/CustomizeInvoice/EditBatchCustomizeInvoice`,
      method: 'POST',
      data: pushData
    })
      .then((res) => {
        loading.close();
        if (res.data.code === 200) {
          dialogVisibleEditBacthFun.value = false;
          textarea.value = '';
          approveRemark.value = '';
          crud.toQuery();
          ElMessage({
            showClose: true,
            message: '编辑成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch(() => {
        loading.close();
      });
  };

  let customizeInvoiceValue = ref(0);

  const cancelSubmit = () => {
    textarea.value = '';
    approveRemark.value = '';
    isSubmit.value = false;
    dialogVisible.value = false;
    dialogVisibleEditBacthFun.value = false;
  };
  //提交（推送金碟）
  const submitFun2 = () => {
    if (crud.selections && crud.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要提交的开票单',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      if (currUserName.value !== crud.selections[0].createdBy) {
        ElMessage({
          showClose: true,
          message: '只有创建人才能操作提交!',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      ElMessageBox.confirm('此操作将提交开票单， 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/EditCustomizeInvoice`,
          method: 'POST',
          data: {
            customizeInvoiceItemId: crud.selections[0].id,
            invoiceType: invoiceType.value,
            remark: textarea.value,
            approveRemark: approveRemark.value,

            redOffsetCode: redOffsetCode.value,
            invoiceNo: invoiceNo.value,
            invoiceCode: invoiceCode.value,
            blueRedInvoiceAmount: blueRedInvoiceAmount.value,
            redOffsetOpter: redOffsetOpter.value,
            redOffsetReason: redOffsetReason.value
          }
        })
          .then((res) => {
            if (res.data.code === 200) {
              request({
                url: `/api/CustomizeInvoice/SubmitCustomizeInvoiceToOA`,
                method: 'POST',
                data: { customizeInvoiceItemId: crud.selections[0].id }
              }).then((res) => {
                if (res.data.code === 200) {
                  dialogVisible.value = false;
                  crud.toQuery();
                  ElMessage({
                    showClose: true,
                    message: '提交成功!',
                    type: 'success',
                    duration: 3 * 1000
                  });
                } else {
                  ElMessage({
                    showClose: true,
                    message: res.data.message,
                    type: 'error',
                    duration: 3 * 1000
                  });
                }
              });
            } else {
              ElMessage({
                showClose: true,
                message: res.data.msg != null ? res.data.msg : res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //撤回
  const recallFun = () => {
    if (crudClassify.selections && crudClassify.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请选择要撤回的开票单',
        type: 'error',
        duration: 3 * 1000
      });
    } else {
      if (currUserName.value !== crudClassify.selections[0].createdBy) {
        ElMessage({
          showClose: true,
          message: '只有创建人才能操作撤回!',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      ElMessageBox.confirm('此操作将撤回开票单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: `/api/CustomizeInvoice/RecallCustomizeInvoice`,
          method: 'POST',
          data: {
            CustomizeInvoiceClassifyId: crudClassify.selections[0].id,
            CustomizeInvoiceClassifyCode: crudClassify.selections[0].billCode
          }
        })
          .then((res) => {
            if (res.data.code === 200) {
              crudClassify.toQuery();
              ElMessage({
                showClose: true,
                message: '撤回成功!',
                type: 'success',
                duration: 3 * 1000
              });
            } else {
              ElMessage({
                showClose: true,
                message: res.data.msg != null ? res.data.msg : res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          });
      });
    }
  };
  //审批过程
  const auditProcessClick = (row) => {
    //审批过程
    approveProcessRef.value.requestId = row.oaRequestId;
    approveProcessRef.value.dialogApproveProcessVisible = true;
    approveProcessRef.value.activeName = 'first';
    approveProcessRef.value.GetRemark();
  };
  //批量下载附件
  const batchDownLoading = ref(false);
  const batchDownLoad = () => {
    batchDownLoading.value = true;
    request({
      url: `/api/CustomizeInvoice/GetAttachFileIds`,
      method: 'POST',
      data: crudClassify.query
    }).then((res) => {
      if (res.data.data) {
        downloadFilesViaIdsAsZip(res.data.data, '运营提交开票附件');
      }
      batchDownLoading.value = false;
    })
  }
  //保存 开票明细
  const Save = () => {
    // if (currUserName.value !== crud.selections[0].createdBy) {
    //   ElMessage({
    //     showClose: true,
    //     message: '只有创建人才能操作保存!',
    //     type: 'error',
    //     duration: 3 * 1000
    //   });
    //   return;
    // }
    if (!customizeInvoiceIndexCheck.value) {
      ElMessage({
        showClose: true,
        message: '分组号只能填写正整数！请检查',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    if (!sortCheck.value) {
      ElMessage({
        showClose: true,
        message: '序号只能填写正整数！请检查',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    isSubmit.value = true;
    if (crud1.data && crud1.data.length > 0) {
      request({
        url: `/api/CustomizeInvoice/SaveCustomizeInvoiceDetail`,
        method: 'POST',
        data: { ItemList: crud.selections, DetailList: crud1.data }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud1.toQuery();
            ElMessage({
              showClose: true,
              message: '保存成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          isSubmit.value = false;
        });
    } else {
      ElMessage({
        showClose: true,
        message: '没有要保存的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  const dialogVisible_Detail = ref(false);
  const textName = ref('');
  const oldQty = ref('');
  const qty = ref('');

  //新增折扣行
  const discount = () => {
    // if (currUserName.value !== crud.selections[0].createdBy) {
    //   ElMessage({
    //     showClose: true,
    //     message: '只有创建人才能操作!',
    //     type: 'error',
    //     duration: 3 * 1000
    //   });
    //   return;
    // }
    if (crud1.selections && crud1.selections.length > 0) {
      var ids = [];
      crud1.selections.forEach((element) => {
        if (
          (element.value === 0 || element.originalPrice != element.price) &&
          element.quantity !== 0
        ) {
          ids.push(element.id);
        }
      });
      if (ids.length <= 0) {
        ElMessage({
          showClose: true,
          message: '请选择金额为0或折扣数据操作!',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      isSubmit.value = true;
      request({
        url: `/api/CustomizeInvoice/adddiscount`,
        method: 'POST',
        data: {
          customizeInvoiceDetailIds: ids
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '操作成功',
              type: 'success',
              duration: 3 * 1000
            });
            crud1.toQuery();
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          isSubmit.value = false;
        });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择一行数据！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };

  const spiltClassify = ref('0');
  const splitLable = ref('拆分数量:');
  const selectspiltClassify = () => {
    if (spiltClassify.value == '0') {
      qty.value = oldQty.value > '0' ? '0.5' : '-0.5';
      splitLable.value = '拆分数量:';
    } else {
      qty.value = '1';
      splitLable.value = '拆分份数:';
    }
  };
  //拆分
  const splitFun = () => {
    if (crud1.selections && crud1.selections.length == 1) {
      dialogVisible_Detail.value = true;
      textName.value = crud1.selections[0].productName;
      oldQty.value = crud1.selections[0].quantity;
      qty.value = oldQty.value > '0' ? '0.5' : '-0.5';
      splitLable.value = '拆分数量:';
      spiltClassify.value = '0';
    } else {
      ElMessage({
        showClose: true,
        message: '请选择一个要拆分的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
    //拆分
  const splitFunCount = () => {
    if (crud1.selections && crud1.selections.length == 1) {
      dialogVisible_Detail.value = true;
      textName.value = crud1.selections[0].productName;
      oldQty.value = crud1.selections[0].quantity;
      qty.value = '1';
      splitLable.value = '拆分份数:';
      spiltClassify.value = '1';
    } else {
      ElMessage({
        showClose: true,
        message: '请选择一个要拆分的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  //拆分确定按钮
  const splitSubmit = () => {
    if (qty.value.trim() === '') {
      ElMessage({
        showClose: true,
        message: '请输入' + splitLable.value.replace(':', ''),
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    ElMessageBox.confirm('此操作将拆分数量, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      isSubmit.value = true;
      const loading = ElLoading.service({
        lock: true,
        text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      request({
        url: `/api/CustomizeInvoice/SplitSubmit`,
        method: 'POST',
        data: {
          CustomizeInvoiceDetailId: crud1.selections[0].id,
          ProductName: textName.value,
          Quantity: qty.value,
          Classify: spiltClassify.value
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '操作成功',
              type: 'success',
              duration: 3 * 1000
            });
            dialogVisible_Detail.value = false;
            crud1.toQuery();
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          isSubmit.value = false;
          loading.close();
        });
    });
  };

  //合并（折扣行合并明细）
  const MergeByDiscount = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将相同的商品行和折扣行合并数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MergeByDiscount`,
          method: 'POST',
          data: crud1.selections
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              dialogVisible_Detail.value = false;
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //合并（按产品名称和单价合并明细）
  const MargeByProductNamePrice = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将同开票名称、单价合并明细数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MargeByProductNamePrice`,
          method: 'POST',
          data: crud1.selections
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              dialogVisible_Detail.value = false;
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //合并（按产品名称和单价、规格合并明细）
  const MargeByProductNamePriceSpecification = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将同开票名称、单价、规格合并明细数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MargeByProductNamePriceSpecification`,
          method: 'POST',
          data: crud1.selections
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              dialogVisible_Detail.value = false;
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //合并（按产品名称和单价、原价合并明细）
  const MargeByProductNamePriceOriginPrice = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将同开票名称、单价、原价合并明细数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MargeByProductNamePriceOriginPrice`,
          method: 'POST',
          data: crud1.selections
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              dialogVisible_Detail.value = false;
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //合并（按原始规格和单价、原价合并明细）
  const MargeByProductNoPriceOriginPrice = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将同按原始规格和单价、原价合并明细数据, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MargeByProductNoPriceOriginPrice`,
          method: 'POST',
          data: crud1.selections
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              dialogVisible_Detail.value = false;
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };


  const dialogVisibleMergeDetailInfo = ref(false);
  //输入数量，单价合并
  const MergeByInputQuantityPrice = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要合并的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      dialogVisibleMergeDetailInfo.value = true;
    }
  };
  const mergePrice = ref(0);
  const mergeQuantity = ref(0);
  const MergeByInputQuantityPriceCancel = () => {
    dialogVisibleMergeDetailInfo.value = false;
    mergePrice.value = 0;
    mergeQuantity.value = 0;
  };
  const MergeByInputQuantityPriceOpt = () => {
    if (mergePrice.value == 0 || mergeQuantity.value == 0) {
      ElMessage({
        showClose: true,
        message: '请输入数量和单价',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将会将选择的开票明细，根据数量和单价合并为一条开票明细, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/MergeByInputQuantityPrice`,
          method: 'POST',
          data: { "customizeInvoiceDetails": crud1.selections, "quantity": mergeQuantity.value, "price": mergePrice.value }
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            MergeByInputQuantityPriceCancel();
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  const dialogVisibleEditDetailInfo = ref(false);
  const UpdateDetailQuantityPrice = () => {
    if (crud1.selections && crud1.selections.length == 0) {
      ElMessage({
        showClose: true,
        message: '请勾选要修改的开票明细',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      dialogVisibleEditDetailInfo.value = true;
    }
  };
  const editPrice = ref(0);
  const editQuantity = ref(0);
  const UpdateDetailQuantityPriceCancel = () => {
    dialogVisibleEditDetailInfo.value = false;
    editPrice.value = 0;
    editQuantity.value = 0;
  };
  const UpdateDetailQuantityPriceOpt = () => {
    if (editPrice.value == 0 || editQuantity.value == 0) {
      ElMessage({
        showClose: true,
        message: '请输入数量和单价',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      ElMessageBox.confirm(
        '此操作将会修改开票明细的数量和单价, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        isSubmit.value = true;
        const loading = ElLoading.service({
          lock: true,
          text: '数据处理中，时间有点长，请勿关闭，耐心等待，谢谢！',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        request({
          url: `/api/CustomizeInvoice/UpdateDetailQuantityPrice`,
          method: 'POST',
          data: { "customizeInvoiceDetails": crud1.selections, "quantity": editQuantity.value, "price": editPrice.value }
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功',
                type: 'success',
                duration: 3 * 1000
              });
              crud1.toQuery();
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            UpdateDetailQuantityPriceCancel();
            isSubmit.value = false;
            loading.close();
          });
      });
    }
  };
  //上传附件
  let flieList = ref('');
  let comfile_upload = ref(false);
  const openAttachment = (row) => {
    flieList = ref('');
    comfile_upload.value = true;
  };
  //上传结算清单附件
  let flieList_jsqd = ref('');
  let comfile_upload_jsqd = ref(false);
  const openAttachment_jsqd = (row) => {
    flieList_jsqd = ref('');
    comfile_upload_jsqd.value = true;
  };
  const redOffsetdialogVisible = ref(false);
  const redOffsetOpter = ref(0);
  const redOffsetCode = ref('');
  const invoiceNo = ref('');
  const invoiceCode = ref('');
  const redWay = ref(0);
  const blueRedInvoiceAmount = ref(0);
  const redOffsetReason = ref(1);

  const isNoRedConfirm = ref(0);
  const redOffsetTitle = ref('红冲发票-已有红字确认单号');
  //红冲发票有红字信息表编
  const createOffsetInvoice = (row) => {
    redOffsetdialogVisible.value = true;
    redOffsetTitle.value = '红冲发票-已有红字确认单号';
    isNoRedConfirm.value = 0;
    redOffsetCode.value = '';
    invoiceNo.value = '';
    invoiceCode.value = '';
    blueRedInvoiceAmount.value = row.invoiceTotalAmount;
    // 获取选择开票分类的开票类型，是初始应收只能整单红冲
    let currentClassify = crudClassify.rowData.classify;
    if (currentClassify === 3) {
      RedWayEnum.value = [
        { id: 0, name: '整单红冲' }
      ];
    } else {
      RedWayEnum.value = [
        { id: 0, name: '整单红冲' },
        { id: 1, name: '部分红冲' },
      ];
    }
  };
  //红冲发票无红字信息表编
  const createOffsetInvoiceNonRedComfirmNo = (row) => {
    redOffsetdialogVisible.value = true;
    redOffsetTitle.value = '红冲发票-暂无红字确认单号';
    isNoRedConfirm.value = 1;
    redOffsetCode.value = '';
    invoiceNo.value = '';
    invoiceCode.value = '';
    blueRedInvoiceAmount.value = row.invoiceTotalAmount;
    // 获取选择开票分类的开票类型，是初始应收只能整单红冲
    let currentClassify = crudClassify.rowData.classify;
    if (currentClassify === 3) {
      RedWayEnum.value = [
        { id: 0, name: '整单红冲' }
      ];
    } else {
      RedWayEnum.value = [
        { id: 0, name: '整单红冲' },
        { id: 1, name: '部分红冲' },
      ];
    }
  };

  const changeRedOffsetReason = () => {
    redWay.value = 0;
  };
  const cancelRedOffset = () => {
    redOffsetdialogVisible.value = false;
    redOffsetCode.value = '';
    invoiceNo.value = '';
    invoiceCode.value = '';
    redOffsetOpter.value = 0;
    redOffsetReason.value = 1;
    blueRedInvoiceAmount.value = 0;
    redWay.value = 0;
  };
  //发票红冲
  const createOffsetInvoiceOpt = () => {
    if (redWay.value == 1 && crudRed.selections.length <= 0) {
      ElMessage({
        showClose: true,
        message: '部分红冲，请选择红冲单据!',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    if (crud.selections && crud.selections.length == 1) {
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      var creditBillCodes = [];
      crud1.data.map((item) => {
        creditBillCodes.push(item.creditBillCode)
      })
      request({
        url: `/api/CustomizeInvoice/createOffsetInvoice`,
        method: 'POST',
        data: {
          id: crud.selections[0].id,
          blueRedInvoiceAmount: blueRedInvoiceAmount.value,
          invoiceCode: invoiceCode.value,
          invoiceNo: invoiceNo.value,
          redOffsetCode: redOffsetCode.value,
          redOffsetOpter: redOffsetOpter.value,
          redOffsetReason: redOffsetReason.value,
          redWay: redWay.value,
          redDetails: redWay.value == 0 ? [] : crudRed.selections,
          creditBillCodes: creditBillCodes,
          isNoRedConfirm: isNoRedConfirm.value
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '操作成功',
              type: 'success',
              duration: 3 * 1000
            });
            crudClassify.query.status = -1;
            crudClassify.toQuery();
            cancelRedOffset();
          } else {
            ElMessage({
              showClose: true,
              message: res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          loading.close();
        });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择一行数据！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  const hasRedInvoiceRelationCode = computed(() => {
    return crud.selections.length > 0 && !!crud.selections[0].relationCode;
  });
  const handleClosefile = () => {
    comfile_upload.value = false;
    comfile_upload_jsqd.value = false;
  };
  let customizeInvoiceItemId = ref('');
  const comfile_show = ref(false);
  const showfiles = ref([]);
  const showAttachFile = (showAttachFileids, id) => {
    if (showAttachFileids == '' || showAttachFileids.length <= 0) {
      ElMessage({
        showClose: true,
        message: '操作失败，原因：该数据没有附件！',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      customizeInvoiceItemId.value = id;
      request({
        url: `/api/CustomizeInvoice/GetAttachFile`,
        method: 'POST',
        data: {
          customizeInvoiceItemId: id
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            comfile_show.value = true;
            showfiles.value = res.data.data;
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    }
  };

  let customizeInvoiceClassifyId = ref('');
  const comfile_show_jsqd = ref(false);
  const showfiles_jsqd = ref([]);
  var ciid = '';
  var cstatus = 0;
  const showAttachFile_jsqd = (showAttachFileids, id, status) => {
    ciid = id;
    cstatus = status;
    console.log('cstatus:' + status);
    if (showAttachFileids == '' || showAttachFileids.length <= 0) {
      ElMessage({
        showClose: true,
        message: '操作失败，原因：该数据没有附件！',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    } else {
      customizeInvoiceClassifyId.value = id;
      request({
        url: `/api/CustomizeInvoice/GetAttachFile_jsqd`,
        method: 'POST',
        data: {
          customizeInvoiceItemId: id
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            comfile_show_jsqd.value = true;
            showfiles_jsqd.value = res.data.data;
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    }
  };
  const showFileInfo = (fileid) => {
    FileViewer.show(
      [fileid], // 可以为数组和逗号隔开的字符串
      0, // 默认打开的下标
      {} // FileViewer props
    );
  };
  const deleteFile = (fileid) => {
    ElMessageBox.confirm('是否确定?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: `/api/CustomizeInvoice/DeleteAttachFileIds`,
        method: 'POST',
        data: {
          customizeInvoiceItemId: customizeInvoiceItemId.value,
          attachFileId: fileid
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            ElMessage({
              showClose: true,
              message: '操作成功！',
              type: 'success',
              duration: 3 * 1000
            });
            if (res.data.data == '' || res.data.data.length == 0) {
              crud.toQuery();
              comfile_show.value = false;
            } else {
              showAttachFile(res.data.data, customizeInvoiceItemId.value);
            }
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        });
    });
  };
  const deleteFile_jsqd = (fileid) => {
    customizeInvoiceClassifyId.value = ciid;
    if (cstatus === 0 || true) {
      ElMessageBox.confirm('是否确定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
          url: `/api/CustomizeInvoice/DeleteAttachFileIds_jsqd`,
          method: 'POST',
          data: {
            customizeInvoiceItemId: customizeInvoiceClassifyId.value,
            attachFileId: fileid
          }
        })
          .then((res) => {
            if (res.data.code === 200) {
              ElMessage({
                showClose: true,
                message: '操作成功！',
                type: 'success',
                duration: 3 * 1000
              });
              if (res.data.data == '' || res.data.data.length == 0) {
                crudClassify.toQuery();
                comfile_show_jsqd.value = false;
              } else {
                showAttachFile_jsqd(
                  res.data.data,
                  customizeInvoiceClassifyId.value,
                  0
                );
              }
            } else {
              ElMessage({
                showClose: true,
                message: res.data.msg != null ? res.data.msg : res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          });
      });
    } else {
      //已开票不允许删除附件
      ElMessage({
        showClose: true,
        message: '已提交状态不允许删除附件!',
        type: 'warning',
        duration: 3 * 1000
      });
    }
  };
  const savefile = () => {
    if (flieList.value == '' || flieList.value.length <= 0) {
      ElMessage({
        showClose: true,
        message: '操作失败，原因：请上传文件！',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    request({
      url: `/api/CustomizeInvoice/AttachFileIds`,
      method: 'POST',
      data: {
        customizeInvoiceItemId: crud.selections[0].id,
        AttachFileIds: flieList.value
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_upload.value = false;
          crud.toQuery();
          ElMessage({
            showClose: true,
            message: '保存成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  };
  //上传结算清单附件
  const savefile_jsqd = () => {
    if (flieList_jsqd.value == '' || flieList_jsqd.value.length <= 0) {
      ElMessage({
        showClose: true,
        message: '操作失败，原因：请上传文件！',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
    const ids = crudClassify.selections.map(item => item.id);
    request({
      url: `/api/CustomizeInvoice/AttachFileIds_jsqd`,
      method: 'POST',
      data: {
        customizeInvoiceItemIds: ids,
        AttachFileIds: flieList_jsqd.value
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_upload_jsqd.value = false;
          crudClassify.toQuery();
          ElMessage({
            showClose: true,
            message: '保存成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  };
  //设置为另一个开票单
  const SetAsAnotherInvoice = () => {
    if (crud1.selections && crud1.selections.length > 0) {
      // if (currUserName.value !== crud.selections[0].createdBy) {
      //   ElMessage({
      //     showClose: true,
      //     message: '只有创建人才能操作设置为另一个开票单!',
      //     type: 'error',
      //     duration: 3 * 1000
      //   });
      //   return;
      // }

      ElMessageBox.confirm('此操作将设置为另一个开票单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        isSubmit.value = true;
        request({
          url: `/api/CustomizeInvoice/SetAsAnotherInvoice`,
          method: 'POST',
          data: {
            ItemList: crud.selections,
            DetailList: crud1.data,
            SelDetailList: crud1.selections
          }
        })
          .then((res) => {
            if (res.data.code === 200) {
              crud.toQuery();
              ElMessage({
                showClose: true,
                message: '设置成功!',
                type: 'success',
                duration: 3 * 1000
              });
            } else {
              ElMessage({
                showClose: true,
                message: res.data.msg != null ? res.data.msg : res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          })
          .finally(() => {
            isSubmit.value = false;
          });
      });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择要设置为另一个开票单的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  const BatchSetAsAnotherInvoice = () => {
    ElMessageBox.confirm(
      '此操作会将相同分组号的明细设置为一个开票单, 是否继续?',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      isSubmit.value = true;
      const loading = ElLoading.service({
        lock: true,
        text: '数据处理中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      request({
        url: `/api/CustomizeInvoice/BatchSetAsAnotherInvoice`,
        method: 'POST',
        data: {
          ItemList: crud.selections,
          DetailList: crud1.data
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crudClassify.toQuery();
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '设置成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          isSubmit.value = false;
          loading.close();
        });
    });
  };

  //删除规格
  const deleteSpec = () => {
    if (crud1.selections && crud1.selections.length > 0) {
      // if (currUserName.value !== crud.selections[0].createdBy) {
      //   ElMessage({
      //     showClose: true,
      //     message: '只有创建人才能操作删除规格!',
      //     type: 'error',
      //     duration: 3 * 1000
      //   });
      //   return;
      // }
      ElMessageBox.confirm('此操作将删除规格, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crud1.selections.forEach((d) => {
          d.specification = '';
        });
      });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择要删除规格的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };

  //删除计量单位
  const deletePackUnit = () => {
    if (crud1.selections && crud1.selections.length > 0) {
      // if (currUserName.value !== crud.selections[0].createdBy) {
      //   ElMessage({
      //     showClose: true,
      //     message: '只有创建人才能操作删除计量单位!',
      //     type: 'error',
      //     duration: 3 * 1000
      //   });
      //   return;
      // }
      ElMessageBox.confirm('此操作将删除计量单位, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crud1.selections.forEach((d) => {
          d.packUnit = '';
        });
      });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择要删除计量单位的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  //删除货号
  const deleteProductNo = () => {
    if (crud1.selections && crud1.selections.length > 0) {
      if (currUserName.value !== crud.selections[0].createdBy) {
        ElMessage({
          showClose: true,
          message: '只有创建人才能操作删除货号!',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      }
      ElMessageBox.confirm('此操作将删除货号, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crud1.selections.forEach((d) => {
          d.productNo = '';
        });
      });
    } else {
      ElMessage({
        showClose: true,
        message: '请选择要删除货号的开票明细！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  };
  const InvoiceTypeEnum = ref([
    { id: 1, name: '电子普通发票' },
    { id: 2, name: '电子专用发票' },
    { id: 3, name: '纸质普通发票' },
    { id: 4, name: '纸质专用发票' },
    { id: 5, name: '增值税普通发票(卷票)' },
    { id: 6, name: '数电票(增值税专用发票)' },
    { id: 7, name: '数电票(普通发票)' }
  ]);
  const RedOffsetOpterEnum = ref([
    { id: 0, name: '购方申请-已抵扣' },
    { id: 1, name: '购方申请-未抵扣' },
    { id: 2, name: '销方申请' }
  ]);
  const RedWayEnum = ref([
    { id: 0, name: '整单红冲' },
    { id: 1, name: '部分红冲' }
  ]);
  const RedOffsetReasonEnum = ref([
    { id: 1, name: '销货退回' },
    { id: 2, name: '开票有误' },
    { id: 3, name: '服务中止' },
    { id: 4, name: '销售折让' }
  ]);
  let isShowChecked = ref(true);
  const isDigitalInvoice = (invoiceType) => {
    return [6, 7].includes(invoiceType); // 数电票对应的 ID
  };
  const tableDetail = ref < InstanceType < typeof ElTable >> ();
  const exportDetailClick = () => {
    isShowChecked.value = false;
    nextTick(() => {
      var wb = XLSX.utils.table_to_book(
        document.querySelector('#tableDetail')?.cloneNode(true)
      ); //关联dom节点
      /* get binary string as output */
      var wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      });
      try {
        FileSaver.saveAs(
          new Blob([wbout], {
            type: 'application/octet-stream'
          }),
          '导出数据（开票明细信息）.xlsx'
        ); //自定义文件名
        isShowChecked.value = true;
      } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbout);
        isShowChecked.value = true;
      }
      return wbout;
    });
  };
  //下载
  const exportClick = async () => {
    ElMessageBox.confirm(
      '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      const loading = ElLoading.service({
        lock: true,
        text: '数据处理中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
        await ExportCustomizeInvoiceList(crudClassify.query, '运营提交开票');
      } catch (error) {
        loading.close();
      }
      loading.close();
    });
  };
  const ExportCustomizeInvoiceList = async (data, filename) => {
    await request({
      url: '/api/CustomizeInvoice/DownLoad',
      data: data,
      method: 'POST',
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
      responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
    })
      .then((res) => {
        const xlsx =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
        const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
        a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
        a.href = window.URL.createObjectURL(blob);
        a.click();
        a.remove();
      })
      .catch((err) => {
        throw '请求错误';
      });
  };
  const searchCrudClassify = () => {
    crudClassify.query.status = -1;
    crudClassify.toQuery();
  };
  //文件大小格式化
  const format = (size) => {
    if (size > 0) {
      if (size < 1000) {
        return size + 'B';
      } else if (size < 1000000) {
        return (size / 1000).toFixed(1) + 'KB';
      } else if (size < 1000000000) {
        return (size / 1000000).toFixed(1) + 'MB';
      } else {
        return (size / 1000000000).toFixed(1) + 'GB';
      }
    } else {
      return 0;
    }
  }
  // 分组号校验
  const customizeInvoiceIndexCheck = ref(true);
  const customizeInvoiceIndexBlur = (val) => {
    var regex = /^[1-9]\d*$/;
    if (!regex.test(val)) {
      customizeInvoiceIndexCheck.value = false;
    } else {
      customizeInvoiceIndexCheck.value = true;
    }
  }
  // 序号校验
  const sortCheck = ref(true);
  const sortBlur = (val) => {
    var regex = /^[1-9]\d*$/;
    if (!regex.test(val)) {
      sortCheck.value = false;
    } else {
      sortCheck.value = true;
    }
  }
  // 提交前弹窗主体选择
  const data_beforeSelection = (val) => {
    organization.value = val;
  }
  const data_beforeSingleSelection = (row, column) => {
    beforeRef.value?.clearSelection();
    beforeRef.value?.toggleRowSelection(row, !organization.value.includes(row));
  }
  // 导入初始应收开票
  const ExcelImportVisibel = ref(false);
  const exportInitCredit = () => {
    ExcelImportVisibel.value = true;
  }
  // 按Excel导入成功后的回调
  const handleSuccess = (res) => {
    if (res.code === 200) {
      crudClassify.toQuery();
      ElMessage({
        showClose: true,
        message: res.message,
        type: 'success',
        duration: 3 * 1000
      });
      ExcelImportVisibel.value = false;
    } else {
      ElMessage({
        showClose: true,
        message: res.message,
        type: 'error',
        duration: 3 * 1000
      });
      ExcelImportVisibel.value = false;
    }
  };
  // 同步税收分类编码
  const syncTaxTypeNoLoading = ref(false);
  const productNoCheck = ref(true);
  const productNoErrMsg = ref('');
  const syncTaxTypeNo = () => {
    console.log(JSON.stringify(crud1.data));
    productNoErrMsg.value = '';
    crud1.data.map(item => {
      if (item.productNo === null) {
        productNoErrMsg.value += item.productName;
        productNoCheck.value = false;
      }
    })
    ElMessageBox.confirm(
      '确认同步税收分类编码吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      syncTaxTypeNoLoading.value = true;
      request({
        url: `/api/CustomizeInvoice/SyncTaxTypeNo`,
        method: 'POST',
        data: {
          companyId: crudClassify.rowData.companyId,
          list: crud1.data
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud.toQuery();
            crud1.toQuery();
            ElMessage({
              showClose: true,
              message: '同步成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          syncTaxTypeNoLoading.value = false;

        });
    });
  }
  // 设置另一个
  const anotherLoading = ref(false);
  const anotherFun = () => {
    ElMessageBox.confirm(
      '确认设置为另一个开票分类吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      console.log(JSON.stringify(crud.selections))
      if (crud.selections.length === crud.data.length) {
        ElMessage({
          showClose: true,
          message: '不能全部设置为另一个开票分类!',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
      anotherLoading.value = true;
      request({
        url: `/api/CustomizeInvoice/SetAnotherCic`,
        method: 'POST',
        data: {
          list: crud.selections
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crudClassify.toQuery();
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '设置成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          anotherLoading.value = false;
        });
    })
  }
  const sortMethodFunc = (obj1, obj2, column) => {
    if (column === 'index' && isNumeric(obj1.customizeInvoiceIndex) && isNumeric(obj2.customizeInvoiceIndex)) {
      let val1 = obj1.customizeInvoiceIndex
      let val2 = obj2.customizeInvoiceIndex
      if (Number(val1) > Number(val2)) {
        return -1
      } else {
        return 1
      }
    }
  }
  const isNumeric = (str) => {
    return !isNaN(parseFloat(str)) && isFinite(str);
  }
  const onImportSuccess = async (datas: any) => {
    crud1.toQuery();
  };
  const openImportModel = () => {
    var negativeCount = 0;
    var manyCredit = 0;
    crud1.data.map(item => {
      if (item.quantity === null || item.quantity <= 0) {
        negativeCount++;
      }
      if (item.creditBillCode.indexOf(',') > -1) {
        manyCredit++
      }
    })
    if (negativeCount > 0) {
      ElMessage({
        showClose: true,
        message: '明细中含有' + negativeCount + '条数量为负数的数据，暂不支持导入',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    }
    if (manyCredit > 0) {
      ElMessage({
        showClose: true,
        message: '明细中含有' + manyCredit + '条应收合并的数据，暂不支持导入',
        type: 'warning',
        duration: 3 * 1000
      });
      return;
    }
    ImportDetail.value.detailImportModel.showDialog = true;
    let param = {
      customizeInvoiceItemId: crud.selections[0].id
    };
    ImportDetail.value.setImportCondition(param);
  }

  //合并
  const merge = (rowData: any) => {
    ElMessageBox.confirm(
      '确认合并开票申请单吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      request({
        url: `/api/CustomizeInvoice/MergeCii`,
        method: 'POST',
        data: {
          list: crud.selections
        }
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '合并成功!',
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {
          //crudClassify.toQuery();
          //crud.toQuery();
        });
    })
  }
  const updateSerialNumber = () => {
    if (crud1.data.length > 0) {
      ElMessageBox.confirm('此操作会使排序号从1开始重新排序, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(crud1.data.map(el=>el.id),'=====================3333333333333333333333333333333333333333')
        crud1.data.forEach((item: any, index: any) => {
          item.sort = index + 1; // 从1开始
        });
        ElMessage.success({ showClose: true, message: '更新成功！' });
        forceRerender();
      });

    } else {
      ElMessage({
        showClose: true,
        message: '开票明细不能为空！',
        type: 'error',
        duration: 3 * 1000
      });
    }
  }
const forceRerender = async () => {
  tableKey.value += 1; // 改变 key 强制销毁并重建表格
};
const handleSortChange = ({ prop, order }) => {
  let sortedData = tableRef1.value?.store.states.data.value;
  crud1.data = [...sortedData];
  // if (order) {
  //   sortedData.sort((a, b) => {
  //     if (order === 'ascending') {
  //       return a[prop] > b[prop] ? 1 : -1;
  //     } else {
  //       return a[prop] < b[prop] ? 1 : -1;
  //     }
  //   });
    // console.log(sortedData.map(el=>el.id),'===================1111111111111111111111111111');
    // console.log(crud1.data.map(el=>el.id),'===================2222222222222222222222222222');
    // 直接更新原始数据
    
  // } 
};
</script>
<style scoped>
  :deep(.warning-row) {
    --el-table-tr-bg-color: var(--el-color-warning-light-3);
  }

  :deep(.el-upload-list__item-name) {
    min-width: 300px !important;
  }
</style>
