﻿using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Npoi.Mapper;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 销售出库
    /// </summary>
    public class StoreOutSaleAppService : BaseAppService, IStoreOutSaleAppService
    {
        private IInventoryApiClient _inventoryApiClient;
        public IBDSApiClient _bDSApiClient;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IBaseAllQueryService<DebtDetailExcutePo> _debtDetailExcuteQueryService;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly ISellApiClient _sellApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly DaprClient _daprClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly FinanceDbContext _db;
        private readonly ISellRecognizeApiClient _sellRecognizeApiClient;
        private readonly IRecognizeReceiveAppService _recognizeReceiveAppService;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly ICompanyDateService _companyDateService;

        public StoreOutSaleAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
            IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
            IBaseAllQueryService<DebtDetailExcutePo> debtDetailExcuteQueryService,
            IDebtDetailRepository debtDetailRepository,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            IAppServiceContextAccessor? contextAccessor,
            ISellApiClient sellApiClient,
            IEasyCachingProvider easyCaching,
            DaprClient daprClient,
            IRecognizeReceiveAppService recognizeReceiveAppService,
            FinanceDbContext db,
            ISellRecognizeApiClient sellRecognizeApiClient,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            ICompanyDateService companyDateService,
            Func<int, TimeSpan> sleepDurationProvider = null) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._easyCaching = easyCaching;
            _sellApiClient = sellApiClient;
            _bDSApiClient = bDSApiClient;
            _inventoryApiClient = inventoryApiClient;
            _debtDetailQueryService = debtDetailQueryService;
            _purchasePayPlanQueryService = purchasePayPlanQueryService;
            _debtDetailRepository = debtDetailRepository;
            _debtDetailExcuteQueryService = debtDetailExcuteQueryService;
            _kingdeeApiClient = kingdeeApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
            _daprClient = daprClient;
            _db = db;
            _sellRecognizeApiClient = sellRecognizeApiClient;
            _recognizeReceiveAppService = recognizeReceiveAppService;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _companyDateService = companyDateService;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            try
            {
                var requestBody = JsonConvert.SerializeObject(input);
                InventoryStoreOutOutput storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);
                if (storeout == null || !storeout.Details.Any())
                {
                    throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                }
                if (string.IsNullOrEmpty(storeout.checker))
                {
                    throw new Exception("该单据没有复核人");
                }
                ret = await CreateCreditForSellsStoreOut(storeout, input.BusinessSubType, requestBody, input.useBillDate, input.IsAutoBill);
                return ret;
            }
            catch
            {
                throw;
                //throw new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }
        #region 出库应收应付
        /// <summary>
        /// 应收
        /// </summary>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateCreditForSellsStoreOut(InventoryStoreOutOutput input,
            string classify,
            string preRequestBody,
            bool? useBillDate = false,
            bool? isAutoBill = false)
        {
            await Task.Delay(5000);//等待应付完全写成功
            var ret = BaseResponseData<int>.Success("操作成功");

            if (input != null)
            {
                var check = await base.IsCreatedCreditForBill(input.storeOutCode);
                if (check)
                {
                    if (isAutoBill.HasValue && isAutoBill.Value)
                    {
                        return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                    }
                    else
                    {
                        return BaseResponseData<int>.Success("该单据已生成过应收");
                    }
                }
                var saleSystemName = string.Empty;
                Guid? saleSystemId = Guid.Empty;
                string? hospitalId = "";
                string hospitalName = string.Empty;
                SaleSourceEnum? source = null;
                SaleTypeEnum? saleType = null;
                RebateTypeEnum? rebateType = null;
                string deptName = string.Empty;
                string orginOrderNo = string.Empty;
                string? customerOrderCode = string.Empty;
                string? customerPersonName = string.Empty;
                string? sunPurchaseRelatecode = string.Empty;
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { input.companyId.ToString() }
                })).FirstOrDefault();
                if (companyInfo != null)
                {
                    var serviceIds = input.Details.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
                    var services = new List<ServiceMetaOutput>();
                    if (serviceIds.Any())
                    {
                        services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                        {
                            ids = serviceIds.Select(p => p.ToString()).ToList()
                        });
                    }
                    var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                    var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                    var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                    var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    PriceSourceEnum? priceSource = null;
                    if (input.relateCode.ToUpper().Contains("SA"))
                    {
                        var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.relateCode);
                        if (saleOut != null)
                        {
                            rebateType = saleOut.RebateType;
                            saleSystemName = saleOut.SaleSystemName;
                            saleSystemId = saleOut.SaleSystemId;
                            hospitalId = saleOut.HospitalId;
                            hospitalName = saleOut.HospitalName;
                            source = saleOut.Source;
                            saleType = saleOut.SaleType;
                            deptName = saleOut.DeptName;
                            if (saleOut.TempInventoryDetails != null && saleOut.TempInventoryDetails.Any())
                            {
                                deptName = string.Join(",", saleOut.TempInventoryDetails.Select(p => p.deptName).Distinct());
                            }
                            //if (!string.IsNullOrEmpty(saleOut.RelateCode) && (saleOut.RelateCode.Contains("-PSA-") || saleOut.SaleType == SaleTypeEnum.SaleForB)) //原始订单号
                            {
                                orginOrderNo = saleOut.RelateCode;
                            }
                            customerOrderCode = saleOut.CustomerOrderCode;
                            customerPersonName = saleOut.CustomerPersonName;
                            sunPurchaseRelatecode = saleOut.SunPurchaseRelatecode;
                            priceSource = saleOut.PriceSourceType == PriceSourceEnum.OPERATION_APPLY ? null : saleOut.PriceSourceType;
                        }
                    }
                    var groupDetail = input.Details.GroupBy(p => new { p.mark, p.businessUnitId, p.projectId });
                    var index = 1;
                    #region 
                    var traceInfos = new List<TraceCodeOutput>();
                    var traceCodes = input.Details.Where(p => p.mark == 0 || p.mark == 3).Select(p => p.traceCode).Distinct().ToList();
                    if (traceCodes.Any())
                    {
                        traceInfos = await _inventoryApiClient.QueryTraceInfoByCodes(traceCodes);
                        if (traceInfos == null || !traceInfos.Any())
                        {
                            return BaseResponseData<int>.Failed(500, $"{string.Join(",", traceCodes)},没有找到traceInfos数据");
                        }
                    }
                    var debtsDetails = new List<DebtDetailPo>();

                    var storeInItemCodes = traceInfos.Where(p => p.StoreInType == 1 || p.StoreInType == 13)
                                                    .Select(p => p.StoreInCode)
                                                    .Distinct()
                                                    .ToList();

                    debtsDetails = await _db.DebtDetails.Include(p => p.Debt)
                                                        .Where(p => p.Status == DebtDetailStatusEnum.WaitExecute &&
                                                                   ((p.AccountPeriodType == 0 && !p.CreditId.HasValue) || p.AccountPeriodType == 2) &&
                                                                    storeInItemCodes.ToHashSet().Contains(p.Debt.RelateCode) &&
                                                                    !p.ProbablyPayTime.HasValue).OrderBy(p => p.DebtId)
                                                                    .ThenBy(p => p.AccountPeriodType).Distinct().AsNoTracking().ToListAsync();

                    var debtDetailIds = debtsDetails.Select(p => p.Id).ToList();

                    foreach (var debtDetail in debtsDetails)
                    {
                        string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                        var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                        if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                        {
                            return BaseResponseData<int>.Failed(500, $"应付单{debtDetail.Debt.BillCode}正在被拆分中,请稍后重试！");
                        }
                        await _easyCaching.SetAsync<string>(cachekey, debtDetail.Id.ToString(), TimeSpan.FromSeconds(120));
                    }
                    var purchaseCodes = traceInfos.Select(p => p.PurchaseOrderCode).Distinct().ToHashSet();
                    var purchasePayPlans = await _purchasePayPlanQueryService.GetAllListAsync(p => purchaseCodes.Contains(p.PurchaseCode));
                    #endregion
                    var kingdeeCredits = new List<KingdeeCredit>();
                    var Ids = new List<Guid>();
                    //应收集合
                    var credits = new List<CreditDto>();
                    foreach (var g in groupDetail)
                    {
                        var thisProjectInfo = projectInfo.FirstOrDefault(p => p.Id == g.Key.projectId);
                        var credit = new CreditDto
                        {
                            PurchaseCost = g.Sum(p => p.unitCost* p.quantity),
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            NameCode = companyInfo.nameCode,
                            AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                            Value = g.ToList().Sum(p => p.quantity * (p.price.HasValue ? p.price.Value : 0)),
                            BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                            BillDate = (useBillDate.HasValue && useBillDate.Value ? DateTimeHelper.LongToDateTime(input.billDate) : billDate),//DateTimeHelper.LongToDateTime(input.billDate),
                            CreatedBy = input.createdBy ?? "none",
                            CreatedTime = DateTime.Now,
                            CreditType = g.Key.mark == 0 || g.Key.mark == 3 ? CreditTypeEnum.selforder.ToString() : CreditTypeEnum.sale.ToString(),
                            Mark = g.Key.mark,
                            CustomerId = input.customerId ?? Guid.Empty,
                            CustomerName = input.customerName,
                            Id = Guid.NewGuid(),
                            InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                            ServiceId = g.Key.businessUnitId,
                            RelateCode = input.storeOutCode,
                            IsSureIncome = 0,
                            OrderNo = input.relateCode,
                            BusinessDeptFullName = input.businessDeptFullName,
                            BusinessDeptFullPath = input.businessDeptFullPath,
                            BusinessDeptId = input.businessDeptId.ToString(),
                            SaleSystemId = saleSystemId,
                            SaleSystemName = saleSystemName,
                            HospitalId = hospitalId,
                            HospitalName = hospitalName,
                            SaleSource = source,
                            SaleType = saleType,
                            Note = input.remark,
                            DeptName = deptName,
                            ProjectName = thisProjectInfo?.Name,
                            ProjectId = g.Key.projectId,
                            ProjectCode = thisProjectInfo?.Code,
                            OriginOrderNo = orginOrderNo,
                            CustomerOrderCode = customerOrderCode,
                            CustomerPersonName = customerPersonName,
                            SunPurchaseRelatecode = sunPurchaseRelatecode,
                            AgentName = string.Join(",", g.Select(p => p.agentName).Distinct().ToList()),
                            ProducerName = string.Join(",", g.Select(p => p.producerName).Distinct().ToList()),
                            PriceSource = priceSource,
                            //IsInternalTransactions = Utility.IsInternalTransactions(input.relateCodeType)
                        };
                        Ids.Add(credit.Id);
                        credits.Add(credit);
                        if (g.Key.businessUnitId.HasValue)
                        {
                            credit.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.ToString().ToLower())?.name;
                        }
                        #region 包装金蝶应收参数
                        var amount = 0m;//不含税总额
                        var jfzx_alltotalcost = 0m;
                        var kingdeeCredit = new KingdeeCredit()
                        {
                            asstact_number1 = input.customerId.Value,
                            billno = credit.BillCode,
                            billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                            bizdate = credit.BillDate.Value,
                            org_number = credit.NameCode,
                            jfzx_businessnumber = input.businessDeptId.ToString().ToUpper(),
                            jfzx_ordernumber = input.relateCode,
                            jfzx_creator = credit.CreatedBy ?? "none",
                            jfzx_serviceid = credit.ServiceName,
                        };

                        kingdeeCredit.jfzx_rebate = rebateType.HasValue;
                        var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                        g.ToList().GroupBy(a => new { a.productId, a.price, a.projectId, a.priceTaxRate, a.agentId, a.unitCost, a.standardUnitCost, a.taxRate }).ForEach(b =>
                        {
                            var d = new KingdeeCreditDetail();
                            d.e_taxunitprice = b.Key.price.Value;
                            d.e_unitprice = d.e_taxunitprice / (1 + b.Key.priceTaxRate.Value / 100.00M);
                            d.e_quantity = b.Sum(c => c.quantity);
                            d.salestaxrate = b.Key.priceTaxRate.Value;
                            var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == b.First().productNameId);
                            if (thisProductInfo.classificationNewGuid.HasValue)
                            {
                                d.e_material_number1 = thisProductInfo.classificationNewGuid.ToString();
                            }
                            else
                            {
                                d.e_material_number1 = thisProductInfo.classificationGuid.ToString();
                            }
                            d.jfzx_outbound_type = "A";
                            var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.Key.projectId);
                            d.jfzx_projectnumber = thisProject?.Code;
                            d.jfzx_unitcost = Math.Round(b.Key.unitCost.Value / (1 + b.Key.taxRate.Value / 100.00M), 2); //b.Key.unitCost.HasValue ? b.Key.unitCost.Value : 0M;
                            d.jfzx_supplier = b.Key.agentId.ToString().ToUpper();
                            if (g.Key.mark != 0 && g.Key.mark != 3)//寄售货需要将标准成本传入金蝶
                            {
                                d.jfzx_standardtotal = b.Key.standardUnitCost.Value;
                            }
                            if (rebateType.HasValue)
                            {
                                d.jfzx_rebateType = (int)rebateType;
                            }
                            kingdeeCreditDetails.Add(d);

                            ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                            //明细总成本
                            d.jfzx_totalcostMany = (b.Key.unitCost / (1 + b.Key.taxRate / 100.00M)) * d.e_quantity;
                            d.jfzx_totalcostMany = Math.Round(d.jfzx_totalcostMany ?? 0, 2);
                            amount += d.e_unitprice * d.e_quantity;
                            jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                        });

                        //应收不含税总额
                        kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                        //应收不含税总额
                        kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                        //总成本
                        kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                        kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                        kingdeeCredits.Add(kingdeeCredit);
                        #endregion
                        var insertRes = await base.CreateCredit(credit);
                        if (insertRes.Code == CodeStatusEnum.Success)
                        {
                            #region 经销货处理应付明细的拆分
                            var thisDetails = g.Where(p => p.mark == 0 || p.mark == 3).ToList();
                            var updateDebtDetailList = new List<DebtDetailUpdateDto>();
                            if (thisDetails.Any())
                            {
                                foreach (var d in thisDetails)
                                {
                                    var thisTrance = traceInfos.FirstOrDefault(q => q.TraceCode == d.traceCode);
                                    if (thisTrance != null)
                                    {
                                        //分别找出销售账期和回款账期的付款计划
                                        var thisPlanForSales = purchasePayPlans.Where(q => q.AccountPeriodType == AccountPeriodTypeEnum.Sale &&
                                                                                           q.PurchaseCode == thisTrance.PurchaseOrderCode &&
                                                                                           q.ProductId == thisTrance.ProductId).ToList();
                                        var thisPlanForRepayments = purchasePayPlans.Where(q => q.AccountPeriodType == AccountPeriodTypeEnum.Repayment &&
                                                                                                q.PurchaseCode == thisTrance.PurchaseOrderCode &&
                                                                                                q.ProductId == thisTrance.ProductId).ToList();
                                        if (thisPlanForSales != null && thisPlanForSales.Count > 0)
                                        {
                                            int startIndex = 0;
                                            foreach (var thisPlanForSale in thisPlanForSales)
                                            {
                                                var thisDebtDetailForSale = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForSale.PurchaseCode &&
                                                                                                            !q.ProbablyPayTime.HasValue && q.AccountPeriodType == 2 &&
                                                                                                            q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                            q.AccountPeriodDays == thisPlanForSale.AccountPeriodDays);
                                                if (thisPlanForSale.AccountPeriodDays == 0)
                                                {
                                                    thisDebtDetailForSale = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForSale.PurchaseCode &&
                                                                                                            !q.ProbablyPayTime.HasValue && q.AccountPeriodType == 2 &&
                                                                                                            q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                            (q.AccountPeriodDays == 0 || q.AccountPeriodDays == null));
                                                }

                                                if (thisDebtDetailForSale != null)//销售账期的付款计划拆分
                                                {
                                                    if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForSale.Id && p.Type == 1))
                                                    {
                                                        updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                        {
                                                            Id = thisDebtDetailForSale.Id,
                                                            Value = thisDebtDetailForSale.Value,
                                                            Type = 1,
                                                            AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForSale.AccountPeriodType,
                                                            AccountPeriodDays = thisDebtDetailForSale.AccountPeriodDays,
                                                            Code = thisDebtDetailForSale.Code,
                                                            DebtId = thisDebtDetailForSale.DebtId,
                                                            ReceiveCode = thisDebtDetailForSale.ReceiveCode,
                                                            RecognizeReceiveCode = thisDebtDetailForSale.RecognizeReceiveCode,
                                                            Status = (DebtDetailStatusEnum)thisDebtDetailForSale.Status,
                                                            PurchaseCode = thisDebtDetailForSale.PurchaseCode,
                                                            CreatedBy = input.createdBy ?? "none",

                                                        });
                                                    }
                                                    if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForSale.Id && p.Type == 2 && p.DetailId == d.id.ToString() && p.DebtId == thisDebtDetailForSale.DebtId) || startIndex == 0)
                                                    {
                                                        updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                        {
                                                            Id = thisDebtDetailForSale.Id,
                                                            //Value = Math.Round(d.quantity * thisPlanForSale.RatioPrice, 2),
                                                            Value = d.quantity * thisTrance.unitCost * thisPlanForSale.Ratio,
                                                            DetailId = d.id.ToString(),
                                                            Type = 2,
                                                            OriginValue = thisDebtDetailForSale.OriginValue,
                                                            AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForSale.AccountPeriodType,
                                                            AccountPeriodDays = thisDebtDetailForSale.AccountPeriodDays,
                                                            Code = thisDebtDetailForSale.Code,
                                                            CreditId = credit.Id,
                                                            DebtId = thisDebtDetailForSale.DebtId,
                                                            Discount = thisDebtDetailForSale.Discount,
                                                            ReceiveCode = thisDebtDetailForSale.ReceiveCode,
                                                            RecognizeReceiveCode = thisDebtDetailForSale.RecognizeReceiveCode,
                                                            Status = (DebtDetailStatusEnum)thisDebtDetailForSale.Status,
                                                            PurchaseCode = thisDebtDetailForSale.PurchaseCode,
                                                            CreatedBy = input.createdBy ?? "none",
                                                            OrderNo = input.relateCode,
                                                            ProbablyPayTime = DateTime.Now.Date.AddDays(thisPlanForSale.AccountPeriodDays.HasValue ? thisPlanForSale.AccountPeriodDays.Value : 0)
                                                        });
                                                    }
                                                }
                                                startIndex++;
                                            }
                                        }
                                        if (thisPlanForRepayments != null && thisPlanForRepayments.Count > 0)
                                        {
                                            int startIndex = 0;
                                            foreach (var thisPlanForRepayment in thisPlanForRepayments)
                                            {
                                                var thisDebtDetailForRepayments = debtsDetails.Where(q => q.PurchaseCode == thisPlanForRepayment.PurchaseCode &&
                                                                                                          q.AccountPeriodType == 0 &&
                                                                                                          q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                          q.AccountPeriodDays == thisPlanForRepayment.AccountPeriodDays).ToList();
                                                var thisDebtDetailForRepayment = thisDebtDetailForRepayments.FirstOrDefault();
                                                if (thisPlanForRepayment.AccountPeriodDays == 0)
                                                {
                                                    thisDebtDetailForRepayment = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForRepayment.PurchaseCode &&
                                                                                                                  q.AccountPeriodType == 0 &&
                                                                                                                  q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                                 (q.AccountPeriodDays == 0 || q.AccountPeriodDays == null));
                                                }

                                                if (thisDebtDetailForRepayment != null)//回款账期的付款计划拆分
                                                {
                                                    if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForRepayment.Id && p.Type == 1))
                                                    {
                                                        updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                        {
                                                            Id = thisDebtDetailForRepayment.Id,
                                                            Value = thisDebtDetailForRepayment.Value,
                                                            Type = 1,
                                                            OriginValue = thisDebtDetailForRepayment.OriginValue,
                                                            AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForRepayment.AccountPeriodType,
                                                            AccountPeriodDays = thisDebtDetailForRepayment.AccountPeriodDays,
                                                            Code = thisDebtDetailForRepayment.Code,
                                                            DebtId = thisDebtDetailForRepayment.DebtId,
                                                            Discount = thisDebtDetailForRepayment.Discount,
                                                            ReceiveCode = thisDebtDetailForRepayment.ReceiveCode,
                                                            RecognizeReceiveCode = thisDebtDetailForRepayment.RecognizeReceiveCode,
                                                            Status = thisDebtDetailForRepayment.Status,
                                                            PurchaseCode = thisDebtDetailForRepayment.PurchaseCode,
                                                            CreatedBy = input.createdBy ?? "none",
                                                        });
                                                    }
                                                    if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForRepayment.Id && p.Type == 2 && p.DetailId == d.id.ToString() && p.DebtId == thisDebtDetailForRepayment.DebtId) || startIndex == 0)
                                                    {
                                                        updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                        {
                                                            Id = thisDebtDetailForRepayment.Id,
                                                            //Value = Math.Round(d.quantity * thisPlanForRepayment.RatioPrice, 2),
                                                            Value = d.quantity * thisTrance.unitCost * thisPlanForRepayment.Ratio,
                                                            Type = 2,
                                                            DetailId = d.id.ToString(),
                                                            OriginValue = thisDebtDetailForRepayment.OriginValue,
                                                            AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForRepayment.AccountPeriodType,
                                                            AccountPeriodDays = thisDebtDetailForRepayment.AccountPeriodDays,
                                                            Code = thisDebtDetailForRepayment.Code,
                                                            CreditId = credit.Id,
                                                            DebtId = thisDebtDetailForRepayment.DebtId,
                                                            Discount = thisDebtDetailForRepayment.Discount,
                                                            ReceiveCode = thisDebtDetailForRepayment.ReceiveCode,
                                                            RecognizeReceiveCode = thisDebtDetailForRepayment.RecognizeReceiveCode,
                                                            Status = thisDebtDetailForRepayment.Status,
                                                            PurchaseCode = thisDebtDetailForRepayment.PurchaseCode,
                                                            CreatedBy = input.createdBy ?? "none",
                                                            OrderNo = input.relateCode,
                                                            ProbablyPayTime = null //DateTime.Now.Date.AddDays(thisPlanForRepayment.AccountPeriodDays.HasValue ? thisPlanForRepayment.AccountPeriodDays.Value : 0)
                                                        });
                                                    }
                                                }
                                                startIndex++;
                                            }
                                        }
                                    }
                                }
                            }
                            if (updateDebtDetailList.Any())
                            {
                                var toUpdateList = new List<DebtDetailUpdateDto>();
                                var toInsertList = new List<DebtDetailUpdateDto>();
                                var ugroup = updateDebtDetailList.GroupBy(p => new { p.DebtId, p.AccountPeriodDays });
                                ugroup.ForEach(u =>
                                {
                                    var saleForUpdateAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale && p.Type == 1).Sum(t => t.Value);
                                    var saleForInsertAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale && p.Type == 2).Sum(t => t.Value);
                                    var RepaymentForUpdateAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment && p.Type == 1).Sum(t => t.Value);
                                    var RepaymentForInsertAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment && p.Type == 2).Sum(t => t.Value);


                                    var waitforupdateList = u.ToList().Where(p => p.Type == 1).ToList();
                                    var waitforInsertList = u.ToList().Where(p => p.Type == 2).ToList();
                                    waitforupdateList.ForEach(p =>
                                    {
                                        var splitAmount = waitforInsertList.Where(t => t.Id == p.Id).Sum(q => q.Value);
                                        p.Value -= splitAmount;
                                        p.UpdatedBy = input.checker;
                                    });
                                    var insertList = waitforInsertList.GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                    {
                                        Id = Guid.NewGuid(),
                                        Value = p.Sum(t => t.Value),
                                        Type = 2,
                                        AccountPeriodType = p.Key.AccountPeriodType,
                                        AccountPeriodDays = p.Key.AccountPeriodDays,
                                        Code = p.First().Code,
                                        CreditId = credit.Id,
                                        DebtId = p.First().DebtId,
                                        Discount = p.First().Discount,
                                        ReceiveCode = p.First().ReceiveCode,
                                        RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                        ProbablyPayTime = p.First().ProbablyPayTime,
                                        Status = p.First().Status,
                                        CreatedBy = input.createdBy ?? "none",
                                        PurchaseCode = p.First().PurchaseCode,
                                        OrderNo = input.relateCode,
                                    }).ToList();


                                    var sale = waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale).FirstOrDefault();
                                    if (sale != null && sale.Value <= 0)
                                    {
                                        var insert = u.ToList().FirstOrDefault(p => p.Type == 2 && p.AccountPeriodType == AccountPeriodTypeEnum.Sale);
                                        sale.Value = saleForUpdateAmount;
                                        sale.CreditId = insert.CreditId;
                                        sale.ProbablyPayTime = insert.ProbablyPayTime;
                                        sale.OrderNo = input.relateCode;
                                        toUpdateList.Add(sale);
                                    }
                                    else
                                    {
                                        toUpdateList.AddRange(waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale));
                                        var list = waitforInsertList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale).GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                        {
                                            Id = Guid.NewGuid(),
                                            Value = p.Sum(t => t.Value),
                                            Type = 2,
                                            AccountPeriodType = p.Key.AccountPeriodType,
                                            AccountPeriodDays = p.Key.AccountPeriodDays,
                                            Code = p.First().Code,
                                            CreditId = credit.Id,
                                            DebtId = p.First().DebtId,
                                            Discount = p.First().Discount,
                                            ReceiveCode = p.First().ReceiveCode,
                                            RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                            ProbablyPayTime = p.First().ProbablyPayTime,
                                            Status = p.First().Status,
                                            CreatedBy = input.createdBy ?? "none",
                                            PurchaseCode = p.First().PurchaseCode,
                                            OrderNo = input.relateCode,
                                        }).ToList();
                                        toInsertList.AddRange(list);
                                    }
                                    var repay = waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment).FirstOrDefault();
                                    if (repay != null && repay.Value <= 0)
                                    {
                                        var insert = u.ToList().FirstOrDefault(p => p.Type == 2 && p.AccountPeriodType == AccountPeriodTypeEnum.Repayment);
                                        repay.Value = RepaymentForUpdateAmount;
                                        repay.CreditId = insert.CreditId;
                                        repay.ProbablyPayTime = insert.ProbablyPayTime;
                                        repay.OrderNo = input.relateCode;
                                        toUpdateList.Add(repay);
                                    }
                                    else
                                    {
                                        toUpdateList.AddRange(waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment));
                                        var list = waitforInsertList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment).GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                        {
                                            Id = Guid.NewGuid(),
                                            Value = p.Sum(t => t.Value),
                                            Type = 2,
                                            AccountPeriodType = p.Key.AccountPeriodType,
                                            AccountPeriodDays = p.Key.AccountPeriodDays,
                                            Code = p.First().Code,
                                            CreditId = credit.Id,
                                            DebtId = p.First().DebtId,
                                            Discount = p.First().Discount,
                                            ReceiveCode = p.First().ReceiveCode,
                                            RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                            ProbablyPayTime = p.First().ProbablyPayTime,
                                            Status = p.First().Status,
                                            CreatedBy = input.createdBy ?? "none",
                                            PurchaseCode = p.First().PurchaseCode,
                                            OrderNo = input.relateCode,
                                        }).ToList();
                                        toInsertList.AddRange(list);
                                    }
                                });

                                var toUpdateList1 = toUpdateList.Adapt<List<DebtDetail>>();
                                var toInsertLis1 = toInsertList.Adapt<List<DebtDetail>>();
                                if (toUpdateList1.Any())
                                {
                                    toUpdateList1 = toUpdateList1.Where(p => p.Value != 0).Distinct().ToList();
                                    await _debtDetailRepository.UpdateManyAsync(toUpdateList1);
                                }
                                if (toInsertLis1.Any())
                                {
                                    toInsertLis1 = toInsertLis1.Where(p => p.Value != 0).Distinct().ToList();
                                    await _debtDetailRepository.AddManyAsync(toInsertLis1);
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, "生成应收失败");
                        }
                        index++;
                    }
                    #region 分配订单认款应收金额
                    var addrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    var delrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    //查询预收订单认款数据
                    var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.OrderNo == input.relateCode && string.IsNullOrEmpty(x.CreditCode)).AsNoTracking().ToListAsync();
                    var rriIds = rrdcs.Select(x => x.RecognizeReceiveItemId);
                    var rris = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Where(x => rriIds.Any(p => p == x.Id)).AsNoTracking().ToListAsync();
                    //应收可分配集合
                    var creditSurplusBox = credits.Select(x => new CreditSurplusBoxDto
                    {
                        Code = x.BillCode,
                        CreditSurplusTotalValue = x.Value
                    }).ToList();
                    foreach (var rrdc in rrdcs)
                    {
                        var surplusAmount = rrdc.CurrentValue;
                        foreach (var c in credits)
                        {
                            if (surplusAmount <= 0)
                            {
                                //剩余分配金额为0
                                continue;
                            }
                            //获取应收剩余可分配金额
                            decimal? surplusCreditAmount = c.Value;
                            var singleSurplusBox = creditSurplusBox.FirstOrDefault(x => x.Code == c.BillCode);
                            if (singleSurplusBox != null)
                            {
                                surplusCreditAmount = singleSurplusBox.CreditSurplusTotalValue;
                            }
                            if (surplusCreditAmount <= 0)
                            {
                                continue;
                            }
                            //认款金额大于应收金额，则写入应收全部金额
                            if (surplusAmount >= c.Value)
                            {
                                //deepcopy并继续添加
                                var model = JsonConvert.DeserializeObject<RecognizeReceiveDetailCreditPo>(JsonConvert.SerializeObject(rrdc));
                                if (model == null)
                                {
                                    continue;
                                }
                                model.Id = Guid.NewGuid();
                                model.CreditCode = c.BillCode;
                                model.CreditId = c.Id;
                                model.CurrentValue = surplusCreditAmount;
                                addrrdcs.Add(model);
                                //分配完金额
                                creditSurplusBox.Where(x => x.Code == c.BillCode).ForEach(x =>
                                {
                                    x.CreditSurplusTotalValue -= model.CurrentValue;
                                });
                                surplusAmount -= model.CurrentValue;
                            }
                            else
                            {
                                //deepcopy
                                var model = JsonConvert.DeserializeObject<RecognizeReceiveDetailCreditPo>(JsonConvert.SerializeObject(rrdc));
                                if (model == null)
                                {
                                    continue;
                                }
                                if (surplusCreditAmount < surplusAmount)
                                {
                                    model.Id = Guid.NewGuid();
                                    model.CreditCode = c.BillCode;
                                    model.CreditId = c.Id;
                                    model.CurrentValue = surplusCreditAmount;
                                    addrrdcs.Add(model);
                                    //分配完金额
                                    creditSurplusBox.Where(x => x.Code == c.BillCode).ForEach(x =>
                                    {
                                        x.CreditSurplusTotalValue -= surplusCreditAmount;
                                    });
                                    surplusAmount -= surplusCreditAmount;
                                }
                                else
                                {
                                    model.Id = Guid.NewGuid();
                                    model.CreditCode = c.BillCode;
                                    model.CreditId = c.Id;
                                    model.CurrentValue = surplusAmount;
                                    addrrdcs.Add(model);
                                    //分配完金额
                                    creditSurplusBox.Where(x => x.Code == c.BillCode).ForEach(x =>
                                    {
                                        x.CreditSurplusTotalValue -= surplusAmount;
                                    });
                                    surplusAmount = 0;
                                    break;
                                }
                            }
                        }
                        delrrdcs.Add(rrdc);
                    }
                    if (addrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.AddRange(addrrdcs);
                    }
                    if (delrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.RemoveRange(delrrdcs);
                    }
                    #endregion
                    var kingdeeRes = await _kingdeeApiClient.PushCreditsToKingdee(kingdeeCredits, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await _unitOfWork.CommitAsync();
                        var debtIds = debtsDetails.Select(p => p.DebtId.Value).Distinct().ToList();
                        await _debtRepository.RepaireDebtDiff(debtIds);
                        await CreditAllocationNotice(credits, input, companyInfo, addrrdcs, rris);
                        await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                        {
                            CreditBillCodes = kingdeeCredits.Select(p => p.billno).ToList(),
                            RelateCodes = kingdeeCredits.Select(p => p.jfzx_ordernumber).ToList(),
                        });
                        foreach (var debtDetail in debtsDetails)
                        {
                            string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                            _easyCaching.Remove(cachekey);
                        }
                    }
                    else
                    {
                        foreach (var debtDetail in debtsDetails)
                        {
                            string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                            _easyCaching.Remove(cachekey);
                        }
                        return BaseResponseData<int>.Failed(500, "生成应收到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }

                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "未找到对应的公司");
                }
            }
            return ret;

        }

        /// <summary>
        /// 通知金蝶结算信息
        /// </summary>
        /// <param name="credits"></param>
        /// <param name="input"></param>
        /// <param name="companyInfo"></param>
        /// <param name="addrrdcs"></param>
        /// <param name="rris"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<BaseResponseData<int>> CreditAllocationNotice(List<CreditDto> credits, InventoryStoreOutOutput input, DaprCompanyInfoOutput companyInfo, List<RecognizeReceiveDetailCreditPo> addrrdcs, List<RecognizeReceiveItemPo> rris)
        {
            try
            {
                #region 通知金蝶结算信息
                //查询销售订单
                //var salesDetails = new List<SellsDetailOutputDto>();
                //var saleCodes = new List<string>
                //    {
                //        input.relateCode
                //    };
                //var saleRes = await _sellRecognizeApiClient.GetSalesDetailByCodes(saleCodes);
                //if (saleRes.Code != CodeStatusEnum.Success || saleRes.Data == null)
                //{
                //    throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：销售系统未能查到订单号{input.relateCode}信息");
                //}
                //salesDetails = saleRes.Data.Data;
                //货款
                var kdInputGoods = new List<BatchSaveAcceptanceInput>();
                //暂收款
                var kdInputsTemps = new List<SavePaymentModificationInput>();
                var companyIdToDateMap = new Dictionary<string, DateTime>();

                foreach (var entity in rris)
                {
                    //通知金蝶
                    if (string.IsNullOrEmpty(entity.RelateCode))
                    {
                        //货款
                        var kdInput = new BatchSaveAcceptanceInput
                        {
                            operation = "update",
                            billno = entity.Code,
                            jfzx_amountfield = entity.RecognizeReceiveDetails.Sum(p => p.Value),
                            jfzx_gatheringamount = entity.ReceiveValue,
                            jfzx_gatheringdate = entity.ReceiveDate.ToString("yyyy-MM-dd HH:mm:ss"),
                            jfzx_gatheringnum = entity.ReceiveCode,
                            jfzx_gatheringorg = companyInfo.nameCode,
                            jfzx_payer = entity.CustomerId,
                            jfzx_bizorg = entity.BusinessDepId.ToString(),
                            billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                            acceptanceEntrys = new List<AcceptanceEntrysItemInput> { }
                        };
                        if (entity.Type == "应付")
                        {
                            kdInput.billtype = "ap_finarbill";
                        }
                        //结算明细
                        var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
                        foreach (var detail in entity.RecognizeReceiveDetails)
                        {
                            //var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                            var recognizeReceiveDetailCredit = addrrdcs.Where(x => x.RecognizeReceiveDetailId == detail.Id).ToList();
                            foreach (var rrdc in recognizeReceiveDetailCredit)
                            {
                                if (string.IsNullOrEmpty(rrdc.CreditCode))
                                {
                                    //预收的不传结算明细
                                    continue;
                                }
                                var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                                var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                                settlementEntrie.receivableNumber = rrdc.CreditCode;
                                settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                                settlementEntrie.orderNumber = rrdc.OrderNo;
                                settlementEntrie.settleAmount = rrdc.CurrentValue;
                                settlementEntrie.projectsNumber = currentCredit != null ? currentCredit.ProjectCode : string.Empty;
                                settlementEntrie.revenueConfirm = false;
                                if (currentCredit != null && currentCredit.IsSureIncome.HasValue && currentCredit.IsSureIncome == 1)
                                {
                                    settlementEntrie.revenueConfirm = true;
                                }
                                settlementEntries.Add(settlementEntrie);
                            }
                        }
                        kdInput.settlementEntries = settlementEntries;
                        kdInputGoods.Add(kdInput);
                    }
                    else
                    {
                        // 使用缓存中的实际日期
                        if (!companyIdToDateMap.TryGetValue(entity.CompanyId, out var actualDate))
                        {
                            actualDate = await _companyDateService.GetActualDateAsync(entity.CompanyId);
                            companyIdToDateMap[entity.CompanyId] = actualDate;
                        }

                        //收款调整单（暂收款转货款）
                        var kdInput = new SavePaymentModificationInput
                        {
                            operation = "update",
                            billno = entity.Code,
                            jfzx_sourceorder = entity.RelateCode,
                            jfzx_billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                            jfzx_accbillno = entity.ReceiveCode,
                            org = companyInfo.nameCode,
                            jfzx_adjustmentdate = DateTime.Now.ToString("yyyy-MM-dd"),//entity.ReceiveDate,
                            entryentity = new List<PaymentModificationEntryModel> { }
                        };
                        if (entity.Type == "应付")
                        {
                            kdInput.jfzx_billtype = "ap_finarbill";
                        }
                        //结算明细
                        var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
                        foreach (var detail in entity.RecognizeReceiveDetails)
                        {
                            //var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                            var recognizeReceiveDetailCredit = addrrdcs.Where(x => x.RecognizeReceiveDetailId == detail.Id).ToList();
                            foreach (var rrdc in recognizeReceiveDetailCredit)
                            {
                                if (string.IsNullOrEmpty(rrdc.CreditCode))
                                {
                                    //预收的不传结算明细
                                    continue;
                                }
                                var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                                var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                                settlementEntrie.receivableNumber = rrdc.CreditCode;
                                settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                                settlementEntrie.orderNumber = rrdc.OrderNo;
                                settlementEntrie.settleAmount = rrdc.CurrentValue;
                                settlementEntrie.projectsNumber = currentCredit != null ? currentCredit.ProjectCode : string.Empty;
                                settlementEntrie.revenueConfirm = false;
                                if (currentCredit != null && currentCredit.IsSureIncome.HasValue && currentCredit.IsSureIncome == 1)
                                {
                                    settlementEntrie.revenueConfirm = true;
                                }
                                settlementEntries.Add(settlementEntrie);
                            }
                        }
                        kdInput.settlementEntries = settlementEntries;
                        kdInputsTemps.Add(kdInput);
                    }
                }

                if (kdInputGoods.Any())
                {
                    var kdResult = await _kingdeeApiClient.PushBatchSaveAcceptances(kdInputGoods);
                    if (kdResult.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{kdResult.Message}");
                    }
                }
                if (kdInputsTemps.Any())
                {
                    string errMsg = string.Empty;
                    foreach (var kdInput in kdInputsTemps)
                    {
                        var kdResult = await _kingdeeApiClient.SavePaymentModification(kdInput);
                        if (kdResult.Code != CodeStatusEnum.Success)
                        {
                            errMsg += "[" + kdResult.Message + "]";
                        }
                        if (!string.IsNullOrEmpty(errMsg))
                        {
                            throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{errMsg}");
                        }
                    }
                }
                return BaseResponseData<int>.Success("通知成功");
                #endregion
            }
            catch (Exception ex)
            {
                throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{ex.Message}");
            }
        }

        #endregion

        public async Task<BaseResponseData<List<KingdeeCredit>>> GetSaleStoreOutKindeeCreditParam(EventBusDTO dto)
        {
            var input = await _inventoryApiClient.QueryStoreOutByCode(dto.BusinessCode);

            if (input == null || !input.Details.Any())
            {
                return BaseResponseData<List<KingdeeCredit>>.Failed(500, "未获取到上游单据");
            }
            var groupDetail = input.Details.GroupBy(p => new { p.mark, p.businessUnitId, p.projectId });
            var index = 1;

            var kingdeeCredits = new List<KingdeeCredit>();
            var Ids = new List<Guid>();

            PriceSourceEnum? priceSource = null;
            if (input.Details.Where(x => x.priceSource == PriceSourceEnum.TEMP_APPLY).Count() > 0)
            {
                priceSource = PriceSourceEnum.TEMP_APPLY;
            }
            else if (input.Details.Where(x => x.priceSource == PriceSourceEnum.CHANGE_APPLY).Count() > 0)
            {
                priceSource = PriceSourceEnum.CHANGE_APPLY;
            }

            foreach (var g in groupDetail)
            {
                var credit = new CreditDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    Value = g.ToList().Sum(p => p.quantity * (p.price.HasValue ? p.price.Value : 0)),
                    BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                    //BillDate = billDate,
                    BillDate = DateTimeHelper.LongToDateTime(input.billDate),
                    CreatedBy = input.checker ?? "none",
                    CreatedTime = DateTime.Now,
                    CreditType = g.Key.mark == 0 || g.Key.mark == 0 ? CreditTypeEnum.selforder.ToString() : CreditTypeEnum.sale.ToString(),
                    Mark = g.Key.mark,
                    CustomerId = input.customerId ?? Guid.Empty,
                    CustomerName = input.customerName,
                    Id = Guid.NewGuid(),
                    InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                    ServiceId = g.Key.businessUnitId,
                    RelateCode = input.storeOutCode,
                    IsSureIncome = 0,
                    OrderNo = input.relateCode,
                    BusinessDeptFullName = input.businessDeptFullName,
                    BusinessDeptFullPath = input.businessDeptFullPath,
                    BusinessDeptId = input.businessDeptId.ToString(),
                    //ShipmentCode = input.storeOutCode,
                    Note = input.remark,
                    AgentName = string.Join(",", g.Select(p => p.agentName).Distinct().ToList()),
                    ProducerName = string.Join(",", g.Select(p => p.producerName).Distinct().ToList()),
                    PriceSource = priceSource
                };
                var amount = 0m;//不含税总额
                var jfzx_alltotalcost = 0m;
                #region 包装金蝶应收参数
                var kingdeeCredit = new KingdeeCredit()
                {
                    asstact_number1 = input.customerId.Value,
                    billno = credit.BillCode,
                    billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                    bizdate = credit.BillDate.Value,
                    org_number = credit.NameCode,
                    jfzx_businessnumber = input.businessDeptId.ToString().ToUpper(),
                    jfzx_ordernumber = input.relateCode,
                    jfzx_creator = credit.CreatedBy ?? "none",
                    jfzx_serviceid = credit.ServiceName,
                };

                var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                g.ToList().GroupBy(a => new { a.productId, a.price, a.projectId, a.priceTaxRate, a.agentId, a.unitCost, a.standardUnitCost, a.taxRate }).ForEach(b =>
                {
                    var d = new KingdeeCreditDetail();
                    d.e_taxunitprice = b.Key.price.Value;
                    d.e_unitprice = d.e_taxunitprice / (1 + b.Key.priceTaxRate.Value / 100.00M);
                    d.e_quantity = b.Sum(c => c.quantity);
                    d.salestaxrate = b.Key.priceTaxRate.Value;
                    d.jfzx_outbound_type = "A";
                    d.jfzx_unitcost = Math.Round(b.Key.unitCost.Value / (1 + b.Key.taxRate.Value / 100.00M), 2); //b.Key.unitCost.HasValue ? b.Key.unitCost.Value : 0M;
                    d.jfzx_supplier = b.Key.agentId.ToString().ToUpper();
                    if (g.Key.mark != 0 && g.Key.mark != 3)//寄售货需要将标准成本传入金蝶
                    {
                        d.jfzx_standardtotal = b.Key.standardUnitCost.Value;
                    }
                    kingdeeCreditDetails.Add(d);

                    ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                    //明细总成本
                    d.jfzx_totalcostMany = (b.Key.unitCost / (1 + b.Key.taxRate / 100.00M)) * d.e_quantity;
                    d.jfzx_totalcostMany = Math.Round(d.jfzx_totalcostMany ?? 0, 2);
                    amount += d.e_unitprice * d.e_quantity;
                    jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                });

                //应收不含税总额
                kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                //应收不含税总额
                kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                //总成本
                kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                kingdeeCredits.Add(kingdeeCredit);
                #endregion
                index++;
            }
            return new BaseResponseData<List<KingdeeCredit>>()
            {
                Code = CodeStatusEnum.Success,
                Data = kingdeeCredits
            };
        }
    }
}
