﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.SendNotification;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 发票查询
    /// </summary>
    public class InvoiceQueryService : QueryAppService, IInvoiceQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _iBDSApiClient;
        private IPCApiClient _pCApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IICApiClient _iCApiClient;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ISendNotificationClient _sendNotificationClient;
        private readonly IRecognizeReceiveAppService _recognizeReceiveService;
        private readonly ILogger<InvoiceQueryService> _logger;
        private readonly IEasyCachingProvider _easyCaching;
        // 状态码映射字典
        private static readonly Dictionary<string, string> StatusMappings = new()
        {
            ["00"] = "待确认",
            ["01"] = "已作废",
            ["10"] = "未验收",
            ["20"] = "已验收",
            ["21"] = "已拒收",
            ["30"] = "已支付"
        };
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="iBDSApiClient"></param>
        public InvoiceQueryService(
            IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db, IUnitOfWork unitOfWork,
            IBDSApiClient iBDSApiClient,
            IICApiClient iCApiClient,
            IEasyCachingProvider easyCaching,
            ILogisticsApiClient logisticsApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IPCApiClient pCApiClient,
            ISendNotificationClient sendNotificationClient,
            IRecognizeReceiveAppService recognizeReceiveService,
            ILogger<InvoiceQueryService> logger) :
            base(contextAccessor)
        {
            this._db = db;
            this._iBDSApiClient = iBDSApiClient;
            this._pCApiClient = pCApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._iCApiClient = iCApiClient;
            this._logisticsApiClient = logisticsApiClient;
            this._unitOfWork = unitOfWork;
            this._kingdeeApiClient = kingdeeApiClient;
            this._sendNotificationClient = sendNotificationClient;
            this._logger = logger;
            this._recognizeReceiveService = recognizeReceiveService;
            this._easyCaching = easyCaching;
        }

        /// <summary>
        /// 获取应付单列表 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceQueryListOutput>, int)> GetListAsync(InvoiceQueryInput query)
        {
            //Expression<Func<InvoiceCreditPo, bool>> exp = z => 1 == 1;

            var Query = from ic in _db.InvoiceCredits
                        join ci in _db.CustomizeInvoiceItem on ic.CustomizeInvoiceCode equals ci.Code into ciGroup
                        from ci in ciGroup.DefaultIfEmpty()
                        join i in _db.Invoices on ic.InvoiceNo equals i.InvoiceNo
                        select new InvoiceQueryListOutput
                        {
                            Id = ic.Id,
                            InvoiceId = i.Id,
                            InvoiceNo = ic.InvoiceNo,
                            InvoiceCode = ic.InvoiceCode,
                            InvoiceCheckCode = ic.InvoiceCheckCode,
                            InvoiceTime = ic.InvoiceTime,
                            InvoiceAmount = ic.InvoiceAmount,
                            CreditAmount = ic.CreditAmount,
                            Type = ic.Type,
                            CreditId = ic.CreditId,
                            Remark = ic.Remark,
                            IsCancel = ic.IsCancel,
                            ChangedStatus = !string.IsNullOrEmpty(ci.RelationCode) ? null : ci.ChangedStatus,
                            BlueInvoiceNo = string.IsNullOrEmpty(ci.InvoiceNo) ? i.RelateInvoiceNo : ci.InvoiceNo,
                            CreatedTime = ic.CreatedTime,
                            HospitalName = i.HospitalName,
                            CustomerName = i.CustomerName,
                            CustomerId = i.CustomerId,
                            CompanyId = i.CompanyId,
                        };

            #region 查询条件
            if (query.CreditId != null)
            {
                Query = Query.Where(z => z.CreditId != null && z.CreditId == query.CreditId);
                var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    Query = Query.Where(z => 1 != 1);
                }
                var strategryquery = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };

                if (user.Data.List.First().InstitutionType == 4)
                {
                    var dictionaryOutputs = await _iBDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
                    var dictionaryNames = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
                    // 拆分（业务单元_公司_客户）
                    var newStrs = new List<string>();
                    foreach (var dic in dictionaryNames)
                    {
                        string[] result = dic.Split("_");
                        if (result.Count() >= 3)
                        {
                            var serviceId = result[0].ToUpper();
                            var companyId = result[1].ToUpper();
                            var customerId = result[2].ToUpper();
                            // 需要作为判断的新字符串（去掉业务单元）
                            var newStr = string.Concat(serviceId, "_", companyId, "_", customerId);
                            newStrs.Add(newStr);
                        }
                    }
                    var currentList = await Query.ToListAsync();
                    // 1.终端医院为空直接展示，终端医院等于客户显示
                    var ids = new List<Guid>();
                    var partIds1 = currentList.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.Id).ToList();
                    ids.AddRange(partIds1);
                    // 2.托管公司配置里存在的不显示
                    var partlist = currentList.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName).ToList();
                    var invoiceNos = partlist.Select(x => x.InvoiceNo).ToList();
                    if (invoiceNos != null && invoiceNos.Any() && newStrs.Any())
                    {
                        var restrictedInvoiceNos = await (from ic in _db.InvoiceCredits
                                                          join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                                          from c in icGroup.DefaultIfEmpty()
                                                          where c.ServiceId.HasValue && c.CompanyId.HasValue && c.CustomerId.HasValue && invoiceNos.ToHashSet().Contains(ic.InvoiceNo) && newStrs.ToHashSet().Contains(c.ServiceId.Value.ToString().ToUpper() + "_" + c.CompanyId.Value.ToString().ToUpper() + "_" + c.CustomerId.Value.ToString().ToUpper())
                                                          select ic.InvoiceNo).ToListAsync();
                        if (restrictedInvoiceNos != null && restrictedInvoiceNos.Any())
                        {
                            var partIds = partlist.Where(x => !restrictedInvoiceNos.ToHashSet().Contains(x.InvoiceNo)).Select(x => x.Id).ToList();
                            ids.AddRange(partIds);
                        }
                        else
                        {
                            var partIds = partlist.Select(x => x.Id).ToList();
                            ids.AddRange(partIds);
                        }
                    }
                    if (ids.Any())
                    {
                        Query = Query.Where(x => ids.Contains(x.Id));
                    }
                    else
                    {
                        Query = Query.Where(x => 1 != 1);
                    }
                }
            }
            else
            {
                Query = Query.Where(z => 1 != 1);
            }
            #endregion


            #region 排序
            {
                Query = Query.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await Query.CountAsync();

            //分页
            var list = await Query.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<InvoiceQueryListOutput>()).ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 获取应付单列表 (按发票号查询，不分页)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceQueryListOutput>, int)> GetListByInvoiceNoAsync(InvoiceQueryInput query)
        {
            var Query = from ic in _db.InvoiceCredits
                        join ci in _db.CustomizeInvoiceItem on ic.CustomizeInvoiceCode equals ci.Code into ciGroup
                        from ci in ciGroup.DefaultIfEmpty()
                        join i in _db.Invoices on ic.InvoiceNo equals i.InvoiceNo
                        select new InvoiceQueryListOutput
                        {
                            Id = ic.Id,
                            InvoiceNo = ic.InvoiceNo,
                            InvoiceCode = ic.InvoiceCode,
                            InvoiceCheckCode = ic.InvoiceCheckCode,
                            InvoiceTime = ic.InvoiceTime,
                            InvoiceAmount = ic.InvoiceAmount,
                            CreditAmount = ic.CreditAmount,
                            Type = ic.Type,
                            CreditId = ic.CreditId,
                            Remark = ic.Remark,
                            IsCancel = ic.IsCancel,
                            ChangedStatus = !string.IsNullOrEmpty(ci.RelationCode) ? null : ci.ChangedStatus,
                            BlueInvoiceNo = string.IsNullOrEmpty(ci.InvoiceNo) ? i.RelateInvoiceNo : ci.InvoiceNo,
                            CreatedTime = ic.CreatedTime
                        };


            #region 查询条件
            if (query.InvoiceNo != null)
            {
                Query = Query.Where(z => z.InvoiceNo != null && EF.Functions.Like(z.InvoiceNo, $"{query.InvoiceNo}%"));
            }
            #endregion

            #region 排序
            {
                Query = Query.OrderByDescending(z => z.CreatedTime);
            }
            #endregion
            //总条数
            var count = await Query.CountAsync();
            var sql = Query.ToQueryString();
            //分页
            //var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<InvoiceQueryListOutput>()).ToListAsync();
            //不分页
            var list = await Query.ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 获取发票清单
        /// </summary>
        /// <param name="input">入参</param>
        /// <param name="isExport">是否导出</param>
        /// <returns></returns>
        public async Task<(List<InvoicesQueryListOutput>, int)> GetInvoiceListAsync(InvoicesQueryInput input, bool isExport = false)
        {
            var Query = await BuildInvoiceQuery(input);


            if (isExport)
            {
                var exportList = await Query.OrderByDescending(x => x.InvoiceTime).ToListAsync();
                var _names = exportList.Select(x => x.ApplyUserName)?.Distinct().ToList();
                var _userInfos = await _iBDSApiClient.GetUserByNamesAsync(new GetUserInput()
                {
                    Names = _names,
                    Limit = _names.Count()
                });
                exportList.ForEach(x =>
                {
                    x.ApplyUserByName = _userInfos.Data.List.Where(z => z.Name == x.ApplyUserName).FirstOrDefault()?.DisplayName;
                });
                return (exportList, exportList.Count());
            }

            int count = await Query.CountAsync();
            var list = await Query.OrderByDefault(input.sort).Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

            if (input.IsSimple.HasValue && input.IsSimple.HasValue)
            {
                return (list, count);
            }
            // 计算冲销金额
            var invoiceNos = list.Select(x => x.InvoiceNo).ToList();
            var recognizeReceiveAmounts = await _recognizeReceiveService.GetRecognizeReceiveAmount(invoiceNos, 1);


            var names = list.Select(x => x.ApplyUserName)?.Distinct().ToList();
            var userInfos = await _iBDSApiClient.GetUserByNamesAsync(new GetUserInput()
            {
                Names = names,
                Limit = names.Count()
            });

            foreach (var item in list)
            {
                item.ApplyUserByName = userInfos.Data.List.Where(z => z.Name == item.ApplyUserName).FirstOrDefault()?.DisplayName;
                var invoiceValue = item.InvoiceAmount - Math.Abs(item.RedAmount ??= 0);
                //发票可认款金额等于发票金额-初始化金额-红冲金额-新表对应发票使用金额和应收金额-记录对应应收用认款金额之和对比，取小
                decimal? canAmount = invoiceValue;
                // 初始化发票则加入计算
                if (item.IsInit.HasValue && item.IsInit.Value)
                {
                    invoiceValue -= Math.Abs(item.ReceiveAmount ?? 0);
                }
                var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.InvoiceNo);
                if (rra != null)
                {
                    canAmount = invoiceValue - rra.Amount > rra.CreditSurplusTotalValue ? rra.CreditSurplusTotalValue : invoiceValue - rra.Amount;
                }
                if (canAmount < 0)
                {
                    canAmount = 0;
                }
                item.ResidueWriteOffAmount = canAmount;
                item.AlreadyWriteOffAmount = item.InvoiceAmount - item.ResidueWriteOffAmount;
                if (item.AlreadyWriteOffAmount < 0)
                {
                    item.AlreadyWriteOffAmount = 0;
                }
                //针对先认款再红冲会重复计算，所以超出直接展示发票金额
                if (item.AlreadyWriteOffAmount > item.InvoiceAmount)
                {
                    item.AlreadyWriteOffAmount = item.InvoiceAmount;
                }
            }
            return (list, count);
        }

        /// <summary>
        /// 获取发票清单
        /// </summary>
        /// <param name="input">入参</param>
        /// <param name="isExport">是否导出</param>
        /// <returns></returns>
        public async Task<(List<InvoicesQueryListOutput>, int)> GetInvoiceListByRecognizeReceiveAsync(InvoicesQueryInput input)
        {
            var Query = await BuildInvoiceQuery(input);

            int count = await Query.CountAsync();
            var list = await Query.OrderByDefault(input.sort).Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
            return (list, count);
        }

        /// <summary>
        /// 获取SPD发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        public async Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSPDTabCount(InvoicesQueryInput query)
        {
            var ret = BaseResponseData<SPDInvoiceListTabOutput>.Success("操作成功");
            query.Status = 0;
            query.limit = 9999;
            query.SPDStatus = SPDStatusEnum.all;
            query.IsSimple = true;
            var (list, count) = await GetInvoiceListAsync(query);
            var data = new SPDInvoiceListTabOutput();
            data.WaitSubmitCount = list.Where(x => x.SPDStatus == SPDStatusEnum.waitSubmit).Count();
            data.WaitAuditCount = list.Where(x => x.SPDStatus == SPDStatusEnum.waitAudit).Count();
            data.RejectCount = list.Where(x => x.SPDStatus == SPDStatusEnum.Reject).Count();
            data.ComplateCount = list.Where(x => x.SPDStatus == SPDStatusEnum.Complate).Count();
            data.AllCount = list.Count();
            ret.Data = data;
            return ret;
        }

        /// <summary>
        /// 获取阳采发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        public async Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSunPurchaseTabCount(InvoicesQueryInput query)
        {
            var ret = BaseResponseData<SPDInvoiceListTabOutput>.Success("操作成功");
            query.Status = 0;
            query.limit = 9999;
            query.SunPurchaseStatus = SunPurchaseStatusEnum.all;
            query.IsSimple = true;
            var (list, count) = await GetInvoiceListAsync(query);
            var data = new SPDInvoiceListTabOutput();
            data.WaitSubmitCount = list.Where(x => x.SunPurchaseStatus == SunPurchaseStatusEnum.waitSubmit).Count();
            data.RejectCount = list.Where(x => x.SunPurchaseStatus == SunPurchaseStatusEnum.Ignore).Count();
            data.ComplateCount = list.Where(x => x.SunPurchaseStatus == SunPurchaseStatusEnum.Complate).Count();
            data.AllCount = list.Count();
            ret.Data = data;
            return ret;
        }

        /// <summary>
        /// 忽略阳采发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> IgnoreSunPurchaseInvoice([FromBody] InvoicesQueryInput query)
        {
            var invoice = await _db.Invoices.FirstAsync(x => x.InvoiceNo == query.InvoiceNo);
            if (invoice == null)
            {
                return BaseResponseData<string>.Failed(500, $"发票号{query.InvoiceNo}不存在或已被删除");
            }
            if (invoice.SunPurchaseStatus != SunPurchaseStatusEnum.waitSubmit)
            {
                return BaseResponseData<string>.Failed(500, $"发票号{query.InvoiceNo}当前状态不可忽略");
            }
            invoice.SunPurchaseStatus = SunPurchaseStatusEnum.Ignore;
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("操作成功");
        }

        /// <summary>
        /// 打包发票发送邮箱
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SendEmailInvoices(InvoicesQueryInput query)
        {
            if (!query.CompanyId.HasValue)
            {
                return BaseResponseData<string>.Failed(500, $"请选择公司");
            }
            if (string.IsNullOrEmpty(query.CustomerEmail))
            {
                return BaseResponseData<string>.Failed(500, $"请填邮箱");
            }
            var (list, count) = await GetInvoiceListAsync(query, true);
            if (list != null && list.Count > 1000)
            {
                return BaseResponseData<string>.Failed(500, $"每次最多下载1000张发票");
            }
            var input = new SendMailInvoicesInput();
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                input.dateStart = dateTime.AddMilliseconds(long.Parse(query.billDateBeging)).AddHours(8);
                input.dateEnd = dateTime.AddMilliseconds(long.Parse(query.billDateEnd)).AddHours(8);
            };
            var company = (await _iBDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { query.CompanyId.ToString() }
            })).FirstOrDefault();
            if (company == null)
            {
                return BaseResponseData<string>.Failed(500, $"未找到该公司，发送失败");
            }
            input.salertaxNo = company.latestUniCode;
            input.invoiceNo = list.Select(x => x.InvoiceNo).ToList();
            input.email = query.CustomerEmail;
            var kdRet = await _kingdeeApiClient.SendEmailInvoices(input);
            return kdRet;
        }

        /// <summary>
        /// 同步阳采发票状态
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SyncSunPurchaseInvoiceStatus([FromBody] InvoicesQueryInput query)
        {
            //查询发票
            var invoice = await _db.Invoices
                   .FirstOrDefaultAsync(x => x.InvoiceNo == query.InvoiceNo);

            if (invoice == null)
            {
                return BaseResponseData<string>.Failed(404, $"发票号 {query.InvoiceNo} 不存在");
            }
            //构造IC接口入参
            var input = new SearchInvoiceInput
            {
                FPDM = invoice.InvoiceCode ?? "0000",
                QSRQ = invoice.InvoiceTime?.ToString("yyyyMMdd") ?? string.Empty,
                JZRQ = invoice.InvoiceTime?.AddDays(29).ToString("yyyyMMdd") ?? string.Empty,
                FPH = invoice.SunPurchaseInvoiceNo ?? invoice.InvoiceNo ?? string.Empty,
                CompanyId = invoice.CompanyId.Value
            };

            if (!Guid.TryParse(invoice.CustomerId, out var customerId))
            {
                return BaseResponseData<string>.Failed(400, "无效的客户ID格式");
            }
            input.CustomerId = customerId;
            //调用IC接口
            var ret = await _iCApiClient.SyncSunPurchaseInvoiceStatus(input);
            if (ret.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<string>.Failed(500, "【IC】" + ret.Message);
            }

            var list = ret.Data?.OrderByDescending(p => p.FPZT).ToList();
            if (list == null || !list.Any())
            {
                return BaseResponseData<string>.Failed(500, "【IC】未返回阳采发票数据");
            }

            var single = list.FirstOrDefault(x => x.FPH == input.FPH);
            if (single == null)
            {
                return BaseResponseData<string>.Failed(500, "【IC】未返回符合当前发票的状态数据");
            }

            var FPZTLst = list.Select(p => p.FPZT).Distinct().ToList();
            var hasValidStatus = FPZTLst.Intersect(new[] { "00", "10", "20", "30" }).Any();
            if (!hasValidStatus)
            {
                invoice.SunPurchaseStatus = SunPurchaseStatusEnum.waitSubmit;
            }
            //处理状态
            var tempstatusNameLst = FPZTLst
                .Where(code => StatusMappings.ContainsKey(code))
                .Select(code => StatusMappings[code])
                .ToList();

            invoice.SunPurchaseInvoiceStatus = tempstatusNameLst.Any()
                ? string.Join(",", tempstatusNameLst)
                : string.Empty;

            //数据库事务，仅包裹数据变更
            using var transaction = await _db.Database.BeginTransactionAsync();
            try
            {
                _db.Invoices.Update(invoice);
                await _db.SaveChangesAsync();
                // 提交事务
                await transaction.CommitAsync();

                return BaseResponseData<string>.Success(
                    $"操作成功!同步阳采状态为：{invoice.SunPurchaseInvoiceStatus}"
                );
            }
            catch (Exception ex)
            {
                if (transaction != null)
                {
                    await transaction.RollbackAsync();
                }
                _logger.LogError(ex, "同步阳采状态失败");
                return BaseResponseData<string>.Failed(500, $"【IC】{ex.Message}");
            }
        }

        /// <summary>
        /// 编辑阳采发票详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> EditSunPurchaseInvoiceDetail(EditSunPurchaseInvoiceDetailInput query)
        {
            var single = await _db.SunPurchaseInvoiceDetails.FirstAsync(x => x.Id == query.id);
            if (single == null)
            {
                return BaseResponseData<string>.Failed(500, $"所选择的数据不存在或已被删除");
            }
            if (!string.IsNullOrEmpty(query.hctbdm))
            {
                single.HCTBDM = query.hctbdm;
            }
            if (!string.IsNullOrEmpty(query.qybddm))
            {
                single.QYBDDM = query.qybddm;
            }
            if (!string.IsNullOrEmpty(query.zczh))
            {
                single.ZCZH = query.zczh;
            }
            if (query.hsdj.HasValue)
            {
                single.HSDJ = query.hsdj.Value;
                single.HSJE = single.HSDJ * single.SPSL;
            }
            if (query.spsl.HasValue)
            {
                single.SPSL = query.spsl.Value;
            }
            if (!string.IsNullOrEmpty(query.purchaseCode))
            {
                single.PurchaseCode = query.purchaseCode;
            }
            if (!string.IsNullOrEmpty(query.xsddh))
            {
                single.XSDDH = query.xsddh;
            }
            if (!string.IsNullOrEmpty(query.productNo))
            {
                single.ProductNo = query.productNo;
            }
            if (!string.IsNullOrEmpty(query.scph))
            {
                single.SCPH = query.scph;
            }
            if (!string.IsNullOrEmpty(query.glmxbh))
            {
                single.GLMXBH = query.glmxbh;
            }
            if (!string.IsNullOrEmpty(query.ggxhsm))
            {
                single.GGXHSM = query.ggxhsm;
            }
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("操作成功");
        }

        /// <summary>
        /// 提交阳采发票填报
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SubmitSunPurchaseInvoice(SunPurchaseInvoiceSubmitInput query)
        {
            if (string.IsNullOrEmpty(query.invoiceNo))
            {
                return BaseResponseData<string>.Failed(500, "未获取到发票号");
            }
            var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == query.invoiceNo);
            if (invoice == null)
            {
                return BaseResponseData<string>.Failed(500, $"发票号{query.invoiceNo}不存在或已被删除");
            }
            // 拼装入参
            var sunPurchaseInput = new SunPurchaseDto();
            sunPurchaseInput.CompanyId = invoice.CompanyId.Value;
            sunPurchaseInput.CustomerId = Guid.Parse(invoice.CustomerId);
            var createInvoiceInput = new CreateInvoiceInput();
            createInvoiceInput.CGLX = "3";//query.cglx ??= string.Empty;
            createInvoiceInput.CZLX = "1"; //操作类型 1：新增，3：作废
            createInvoiceInput.YYBM = query.yybm ??= string.Empty;
            createInvoiceInput.FPBH = "";
            createInvoiceInput.PSDBM = query.psdbm ??= string.Empty;
            createInvoiceInput.FPRQ = invoice.InvoiceTime.HasValue ? invoice.InvoiceTime.Value.ToString("yyyyMMdd") : string.Empty;

            createInvoiceInput.FPH = query.finishInvoiceNo;
            createInvoiceInput.FPBZ = query.remark;
            createInvoiceInput.FPDM = String.IsNullOrEmpty(query.finishInvoiceCode) ? (string.IsNullOrEmpty(invoice.InvoiceCode) ? "0000" : invoice.InvoiceCode) : query.finishInvoiceCode;
            createInvoiceInput.JYM = invoice.InvoiceCheckCode;
            var sunPurchaseInvoiceDetails = await _db.SunPurchaseInvoiceDetails.Where(x => x.InvoiceId == invoice.Id).ToListAsync();
            createInvoiceInput.FPHSZJE = invoice.InvoiceAmount.Value;
            createInvoiceInput.FPWSZJE = invoice.InvoiceAmountNoTax.Value;
            var invoiceDetailInputs = sunPurchaseInvoiceDetails.Adapt<List<InvoiceDetailInput>>();
            createInvoiceInput.JLS = invoiceDetailInputs.Count;

            sunPurchaseInput.CreateInvoiceInput = createInvoiceInput;
            int index = 1;
            foreach (var item in invoiceDetailInputs)
            {
                item.SCRQ = DateTime.Parse(item.SCRQ).ToString("yyyyMMdd");
                item.YXRQ = DateTime.Parse(item.YXRQ).ToString("yyyyMMdd");
                item.HCXFDM = "";
                item.WPSFPSM = "";
                item.SXH = index.ToString();
                index++;
            }
            sunPurchaseInput.InvoiceDetailInput = invoiceDetailInputs;
            //推送IC集成平台
            var ret = await _iCApiClient.ICPushSunPurchaseInvoice(sunPurchaseInput);
            if (ret.Code == CodeStatusEnum.Success)
            {
                invoice.SunPurchaseStatus = SunPurchaseStatusEnum.Complate;
                invoice.SunPurchaseInvoiceNo = query.finishInvoiceNo;
                invoice.SunPurchaseInvoiceCode = query.finishInvoiceCode;
                invoice.UpdatedBy = query.updatedBy;
                ret.Message = "操作成功！";
                _db.SaveChanges();
            }
            return ret;
        }

        /// <summary>
        /// 获取阳采发票配送编码
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>> GetPsdByPurchaseCodeForPm([FromBody] SunPurchaseInvoiceSubmitInput query)
        {
            if (string.IsNullOrEmpty(query.purchaseCode))
            {
                return BaseResponseData<SunPurchaseInvoiceInfoForPmInput>.Failed(500, "未获取到阳采采购单号");
            }
            var ret = await _logisticsApiClient.getPsdByPurchaseCodeForPm(query.purchaseCode);
            if (ret != null && ret.Code == CodeStatusEnum.Success)
            {
                return BaseResponseData<SunPurchaseInvoiceInfoForPmInput>.Success(ret.Data);
            }
            return BaseResponseData<SunPurchaseInvoiceInfoForPmInput>.Failed(500, "获取配送点编码信息失败");
        }

        /// <summary>
        /// 获取阳采配送单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ShycShipmentDetailOutput>>> SunPurchaseGetFullDetails([FromBody] ShycShipmentDetaillnput query)
        {
            var ret = await _logisticsApiClient.getFullDetails(query);
            if (ret != null && ret.Code == CodeStatusEnum.Success)
            {
                return ret;
            }
            return BaseResponseData<List<ShycShipmentDetailOutput>>.Failed(500, "获取阳采配送单信息失败");
        }

        /// <summary>
        /// SPD审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> SPDApprove(List<SPDApproveInput> input)
        {
            if (input.Count == 0)
            {
                throw new Exception("数据为空");
            }
            var invoiceNos = input.Select(x => x.invoice_num).ToList();
            var invoices = await (from i in _db.Invoices where invoiceNos.Contains(i.InvoiceNo) select i).ToListAsync();
            foreach (var item in input)
            {
                var single = invoices.FirstOrDefault(x => x.InvoiceNo == item.invoice_num);
                if (single != null)
                {
                    single.SPDStatus = item.apply_status;
                }
            }
            _db.Invoices.UpdateRange(invoices);
            return await _unitOfWork.CommitAsync();
        }

        /// <summary>
        /// 数据策略权限增加(发票清单)
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<IQueryable<InvoicesQueryListOutput>> AddStrategyQueryAsync(StrategyQueryInput queryModel, IQueryable<InvoicesQueryListOutput> query)
        {
            if (queryModel != null)
            {
                var strategry = await _pCApiClient.GetStrategyAsync(queryModel);
                if (strategry != null && strategry.RowStrategies.Count > 0)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                    {
                        query = query.Where(z => 1 != 1);
                    }
                    query = AnalysisStrategy(strategry.RowStrategies, query);
                }
                else
                {
                    query = query.Where(z => 1 != 1);
                }

            }

            return query;
        }
        /// <summary>
        /// 增加数据策略权限(发票清单)
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected IQueryable<InvoicesQueryListOutput> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<InvoicesQueryListOutput> query)
        {

            foreach (var key in rowStrategies.Keys)
            {

                if (key.ToLower() == "company")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToGuidHashSet();
                        query = query.Where(z => z.CompanyId != null && strategList.Contains(z.CompanyId.Value));
                    }
                }
                if (key.ToLower() == "accountingdept")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToHashSet();
                        query = query.Where(z => z.BusinessDeptId != null && strategList.Contains(z.BusinessDeptId));
                    }
                }
                if (key.ToLower() == "customer")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToHashSet();
                        query = query.Where(z => z.CustomerId != null && strategList.Contains(z.CustomerId));
                    }
                }
            }
            return query;
        }

        /// <summary>
        /// 根据发票号获取应收明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceCreditsOutput>, int)> GetInvoiceCreditsByInvoiceNo(InvoiceQueryInput query)
        {
            var Query = from ic in _db.InvoiceCredits
                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                        from c in icGroup.DefaultIfEmpty()
                        where (string.IsNullOrEmpty(query.CompanyId) ? true : c.CompanyId == Guid.Parse(query.CompanyId)) && EF.Functions.Like(ic.InvoiceNo, $"{query.InvoiceNo}%")
                        select new InvoiceCreditsOutput
                        {
                            BillCode = c.BillCode,
                            BillDate = c.BillDate,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            SaleSystemName = c.SaleSystemName,
                            CreditAmount = ic.CreditAmount,
                            IsSureIncome = c.IsSureIncome,
                            ServiceId = c.ServiceId,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            BusinessDeptFullPath = c.BusinessDeptFullPath,
                            ShipmentCode = c.ShipmentCode,
                            Value = c.Value,
                            CreditType = c.CreditType,
                            AgentName = c.AgentName,
                            ProducerName = c.ProducerName,
                            PriceSource = c.PriceSource,
                            Mark = ic.Mark
                        };


            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                Query = Query.Where(z => 1 != 1);
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                //本来就没有这个业务单元权限
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        //query.ServiceId ??= serviceIds.FirstOrDefault();
                        Query = Query.Where(t => serviceIds.Contains(t.ServiceId));
                    }
                }
                //var dictionaryOutputs = await _iBDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
                //var dictionaryNames = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
                //// 拆分（业务单元_公司_客户）
                //var newStrs = new List<string>();
                //foreach (var dic in dictionaryNames)
                //{
                //    string[] result = dic.Split("_");
                //    if (result.Count() >= 3)
                //    {
                //        var serviceId = result[0];
                //        var companyId = result[1];
                //        var customerId = result[2];
                //        // 需要作为判断的新字符串（业务单元）
                //        var newStr = string.Concat(serviceId).ToUpper();
                //        newStrs.Add(newStr);
                //    }
                //}
                //var currentList = await Query.ToListAsync();
                //// 业务单元端不展示客户与终端医院不一致的数据
                //// 1.终端医院为空直接展示，终端医院等于客户显示
                //var billCodes = new List<string?>();
                //// 2.有这个业务单元但是数据字典配置了不给看
                //var partBillCodes = currentList.Where(x=> !newStrs.Any(p => p == x.ServiceId.ToString().ToUpper())).Select(x => x.BillCode).ToList();
                //billCodes.AddRange(partBillCodes);
                //if (billCodes != null && billCodes.Any())
                //{
                //    Query = Query.Where(x => billCodes.AsQueryable().Contains(x.BillCode));
                //}
                //else
                //{
                //    Query = Query.Where(x => 1 != 1);
                //}
            }

            if (!string.IsNullOrEmpty(query.BillCode))
            {
                Query = Query.Where(z => z.BillCode != null && z.BillCode.Contains(query.BillCode));
            }
            if (!string.IsNullOrEmpty(query.RelateCode))
            {
                Query = Query.Where(z => z.RelateCode != null && z.RelateCode.Contains(query.RelateCode));
            }
            if (!string.IsNullOrEmpty(query.OrderNo))
            {
                Query = Query.Where(z => z.OrderNo != null && z.OrderNo.Contains(query.OrderNo));
            }

            //总条数
            var count = await Query.CountAsync();
            //不分页
            //var list = Query.AsParallel().ToList();
            //分页
            var list = Query.AsParallel().Skip((query.page - 1) * query.limit).Take(query.limit).ToList();
            //对供应商和厂家去重
            foreach (var item in list)
            {
                item.AgentName = string.IsNullOrEmpty(item.AgentName) ? "" : string.Join(',', item.AgentName.Split(',').Distinct().ToArray());
                item.ProducerName = string.IsNullOrEmpty(item.ProducerName) ? "" : string.Join(',', item.ProducerName.Split(',').Distinct().ToArray());
            }
            return (list, count);
        }

        /// <summary>
        /// 根据发票号获取应收明细（SPD版）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceCreditsOutput>, int)> GetSPDInvoiceCreditsByInvoiceNo(SPDInvoiceQueryInput query)
        {
            var Query = from ic in _db.InvoiceCredits
                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                        from c in icGroup.DefaultIfEmpty()
                        where (query.InvoiceNos != null && query.InvoiceNos.ToHashSet().Contains(ic.InvoiceNo))
                           && (query.CompanyIds != null && query.CompanyIds.ToHashSet().Contains(c.CompanyId.ToString()))
                        select new InvoiceCreditsOutput
                        {
                            InvoiceNo = ic.InvoiceNo,
                            BillCode = c.BillCode,
                            BillDate = c.BillDate,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            SaleSystemName = c.SaleSystemName,
                            CreditAmount = ic.CreditAmount,
                            IsSureIncome = c.IsSureIncome,
                            ServiceId = c.ServiceId,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            BusinessDeptFullPath = c.BusinessDeptFullPath,
                            ShipmentCode = c.ShipmentCode,
                            RedReversalConsumNo = c.RedReversalConsumNo
                        };


            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                Query = Query.Where(z => 1 != 1);
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        //query.ServiceId ??= serviceIds.FirstOrDefault();
                        Query = Query.Where(t => serviceIds.Contains(t.ServiceId));
                    }
                }
            }

            //总条数
            var count = await Query.CountAsync();
            //不分页
            //var list = Query.AsParallel().ToList();
            //分页
            var list = Query.AsParallel().Skip((query.page - 1) * query.limit).Take(query.limit).ToList();
            return (list, count);
        }

        /// <summary>
        /// 根据发票号获取应收明细（SPD版）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<SunPurchaseInvoiceDetailsOutput>, int)> GetSunPurchaseInvoiceDetailByInvoiceNo([FromBody] InvoicesQueryInput query)
        {
            var Query = from i in _db.Invoices
                        join spid in _db.SunPurchaseInvoiceDetails on i.Id equals spid.InvoiceId
                        where i.InvoiceNo == query.InvoiceNo
                        select spid;

            //总条数
            var count = await Query.CountAsync();
            //不分页
            //var list = Query.AsParallel().ToList();
            //分页
            var list = Query.AsParallel().Skip((query.page - 1) * query.limit).Take(query.limit).Adapt<List<SunPurchaseInvoiceDetailsOutput>>().ToList();
            return (list, count);
        }

        /// <summary>
        /// 根据发票号获取应收明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceCreditsOutput>, int)> GetInvoiceCredits(SPDInvoiceQueryInput query)
        {
            var Query = from ic in _db.InvoiceCredits
                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                        from c in icGroup.DefaultIfEmpty()
                        where (query.CompanyIds != null && query.CompanyIds.ToHashSet().Contains(c.CompanyId.ToString())) && query.InvoiceNos.Any(p => p == ic.InvoiceNo)
                        select new InvoiceCreditsOutput
                        {
                            BillCode = c.BillCode,
                            BillDate = c.BillDate,
                            InvoiceNo = ic.InvoiceNo,
                            CompanyId = c.CompanyId,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            SaleSystemName = c.SaleSystemName,
                            CreditAmount = ic.CreditAmount,
                            IsSureIncome = c.IsSureIncome,
                            ServiceId = c.ServiceId,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            BusinessDeptFullPath = c.BusinessDeptFullPath,
                            ProjectName = c.ProjectName,
                            ProjectCode = c.ProjectCode
                        };


            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                Query = Query.Where(z => 1 != 1);
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        //query.ServiceId ??= serviceIds.FirstOrDefault();
                        Query = Query.Where(t => serviceIds.Contains(t.ServiceId));
                    }
                }
            }

            //总条数
            var count = await Query.CountAsync();
            var list = Query.AsParallel().ToList();
            return (list, count);
        }

        public async Task<InvoicesQueryListOutput> GetInvoiceByInvoiceNo(string invoiceNo)
        {
            var invoice = await (from it in _db.Invoices
                                 join ci in _db.CustomizeInvoiceItem
                                 on it.CustomizeInvoiceCode equals ci.Code
                                 where it.InvoiceNo.Equals(invoiceNo)
                                 select new InvoicesQueryListOutput
                                 {
                                     InvoiceNo = invoiceNo,
                                     InvoiceAmount = it.InvoiceAmount,
                                     Type = it.Type,
                                     CustomerId = ci.CustomerId,
                                     CompanyId = ci.CompanyId,
                                     CustomerName = ci.CustomerName,
                                 }).FirstOrDefaultAsync();
            return invoice.Adapt<InvoicesQueryListOutput>();
        }

        public async Task<List<InvoicesQueryListOutput>> GetInvoiceByInvoiceNos(List<string> invoiceNos)
        {
            var invoices = await (from it in _db.Invoices
                                  join ci in _db.CustomizeInvoiceItem
                                  on it.CustomizeInvoiceCode equals ci.Code
                                  where invoiceNos.Contains(it.InvoiceNo)
                                  select new InvoicesQueryListOutput
                                  {
                                      InvoiceNo = it.InvoiceNo,
                                      InvoiceAmount = it.InvoiceAmount,
                                      Type = it.Type,
                                      CustomerId = ci.CustomerId,
                                      CompanyId = ci.CompanyId,
                                      CustomerName = ci.CustomerName,
                                  }).ToListAsync();
            return invoices.Adapt<List<InvoicesQueryListOutput>>();
        }

        /// <summary>
        /// 获取可入账的发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceReceiptDetailVoOutput>, int)> GetReceiptInvoiceList(InvoiceReceiptDetailQueryInput input)
        {
            if (!input.ServiceId.HasValue || !input.ServiceId.HasValue || !input.CompanyId.HasValue)
            {
                return (new List<InvoiceReceiptDetailVoOutput>(), 0);
            }

            var baseQuery = (from ic in _db.InvoiceCredits.AsNoTracking()
                             join c in _db.Credits.AsNoTracking() on ic.CreditId equals c.Id
                             join i in _db.Invoices.AsNoTracking() on ic.InvoiceNo equals i.InvoiceNo
                             // 初始化发票已入账标识过滤
                             where (!i.InitInvoiceReceipt.HasValue || i.InitInvoiceReceipt.Value != 1) && ic.IsInvoiceReceipt != 1
                             select new InvoiceReceiptDetailVoOutput
                             {
                                 InvoiceAmount = ic.InvoiceAmount,
                                 InvoiceCode = ic.InvoiceCode,
                                 InvoiceCheckCode = ic.InvoiceCheckCode,
                                 InvoiceNo = ic.InvoiceNo,
                                 InvoiceTime = ic.InvoiceTime,
                                 IsCancel = ic.IsCancel,
                                 CreditCode = c.BillCode,
                                 Remark = ic.Remark,
                                 CompanyId = c.CompanyId,
                                 CompanyName = c.CompanyName,
                                 ServiceId = c.ServiceId,
                                 ServiceName = c.ServiceName,
                                 CustomerId = c.CustomerId,
                                 CustomerName = c.CustomerName
                             });

            if (input.ServiceId.HasValue)
            {
                baseQuery = baseQuery.Where(x => x.ServiceId == input.ServiceId);
            }
            if (input.CompanyId.HasValue)
            {
                baseQuery = baseQuery.Where(x => x.CompanyId == input.CompanyId);
            }
            if (input.CustomerId.HasValue)
            {
                baseQuery = baseQuery.Where(x => x.CustomerId == input.CustomerId);
            }
            if (!string.IsNullOrEmpty(input.CreditCode))
            {
                baseQuery = baseQuery.Where(x => x.CreditCode == input.CreditCode);
            }
            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                baseQuery = baseQuery.Where(x => x.InvoiceNo == input.InvoiceNo);
            }
            if (input.ExcludeIds != null && input.ExcludeIds.Any())
            {
                baseQuery = baseQuery.Where(x => !input.ExcludeIds.Contains(x.InvoiceNo));
            }
            if (input.StartDate != "0" && input.EndDate != "0")
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(long.Parse(input.StartDate)).AddHours(8);
                var endTime = dateTime.AddMilliseconds(long.Parse(input.EndDate)).AddHours(8);
                baseQuery = baseQuery.Where(z => z.InvoiceTime <= endTime && z.InvoiceTime >= startTime);
            }


            #region 排序
            if (input.sort != null && input.sort.Any())
            {
                for (int i = input.sort.Count - 1; i >= 0; i--)
                {
                    var ss = input.sort[i].Split(',');
                    if (ss.Length > 1 && ss[0] == "invoiceTime")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.InvoiceTime);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.InvoiceTime); }
                    }
                    else if (ss.Length > 1 && ss[0] == "invoiceNo")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.InvoiceNo);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.InvoiceNo); }
                    }
                }
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion
            baseQuery = baseQuery.Distinct();
            //总条数
            var count = await baseQuery.CountAsync();
            var list = baseQuery.Adapt<List<InvoiceReceiptDetailVoOutput>>().AsParallel().Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
            return (list, count);
        }

        /// <summary>
        /// 获取初始化发票至金蝶列表数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<InvoiceChildInitRequestVo>> GetInitEasInvoices(InitInvoicesInput input)
        {
            if (!input.InvoiceTimeStart.HasValue || !input.InvoiceTimeEnd.HasValue)
            {
                return new List<InvoiceChildInitRequestVo>();
            }

            var list = await (from i in _db.Invoices
                              join ic in _db.InvoiceCredits on i.InvoiceNo equals ic.InvoiceNo
                              join c in _db.Credits on ic.CreditId equals c.Id
                              where i.IsInit == true && i.CreatedTime > input.InvoiceTimeStart && i.CreatedTime < input.InvoiceTimeEnd
                              select new InvoiceChildInitRequestVo
                              {
                                  buyerName = c.HospitalName,
                                  buyerTaxNo = c.CustomerId.ToString(), //暂存客户id
                                  salerName = c.CompanyName,
                                  salerTaxNo = c.NameCode,
                                  invoicecode = i.InvoiceCode,
                                  invoiceno = i.InvoiceNo,
                                  payee = string.Empty, //暂时不给
                                  drawer = string.Empty, //暂时不给
                                  totalAmount = ic.InvoiceAmount,
                                  issuetime = ic.InvoiceTime.HasValue ? ic.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty,
                                  //jfzx_customerf = c.CustomerName
                                  jfzx_customerf = c.CustomerId.HasValue ? c.CustomerId.Value.ToString().ToUpper() : ""
                              }).Distinct().ToListAsync();

            if (list != null && list.Any())
            {
                var ids = new List<string>();
                foreach (var item in list)
                {
                    if (!string.IsNullOrEmpty(item.buyerTaxNo))
                    {
                        ids.Add(item.buyerTaxNo);
                    }
                }
                var customers = await _iBDSApiClient.GetHospitalMetaAsync(new HospitalMetaInput
                {
                    ids = ids,
                    page = 1,
                    limit = int.MaxValue
                });

                foreach (var item in list)
                {
                    var customer = customers.FirstOrDefault(x => x.customerId == item.buyerTaxNo);
                    item.buyerTaxNo = customer != null ? customer.latestUniCode : string.Empty;
                }
            }
            return list;
        }

        /// <summary>
        /// 根据应收单号获取冲销记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<AbatementByCreditBillCodeQueryOutput>, int)> GetAbatementByCreditBillCode([FromBody] AbamentByCreditBillCodeQueryInput input)
        {
            if (string.IsNullOrEmpty(input.InvoiceNo))
            {
                return (new List<AbatementByCreditBillCodeQueryOutput>(), 0);
            }
            var creditCodes = await _db.InvoiceCredits.Where(x => x.InvoiceNo == input.InvoiceNo && x.Credit != null).Include(x => x.Credit).Select(x => x.Credit.BillCode).Distinct().ToListAsync();
            if (creditCodes == null || !creditCodes.Any())
            {
                var Query = _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(x => x.Type == 1 && x.Code == input.InvoiceNo && x.Value > 0 && (x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Auditing || x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Completed)).Select(x => new AbatementByCreditBillCodeQueryOutput
                {
                    // 收款单号
                    CreditBillCode = x.RecognizeReceiveItem.ReceiveCode,
                    Value = x.Value,
                    Abadate = x.RecognizeDate,
                    CreatedBy = x.CreatedBy
                }).Distinct();

                // 总条数
                var count = await Query.CountAsync();
                var list = Query.AsParallel().Skip((input.page - 1) * input.limit).Take(input.limit).ToList();

                return (list, count);
            }
            else
            {
                var creditCodesHashSet = creditCodes.ToHashSet();
                var rrdIds = await _db.RecognizeReceiveDetailCredits.Where(x => creditCodesHashSet.Contains(x.CreditCode) && x.InvoiceNo == input.InvoiceNo).Select(x => x.RecognizeReceiveDetailId).ToListAsync();
                if (rrdIds == null || !rrdIds.Any())
                {
                    return (new List<AbatementByCreditBillCodeQueryOutput>(), 0);
                }
                var Query = _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(x => rrdIds.ToHashSet().Contains(x.Id) && x.Value > 0 && (x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Auditing || x.RecognizeReceiveItem.Status == RecognizeReceiveItemStatusEnum.Completed)).Select(x => new AbatementByCreditBillCodeQueryOutput
                {
                    // 收款单号
                    CreditBillCode = x.RecognizeReceiveItem.ReceiveCode,
                    Value = x.Value,
                    Abadate = x.RecognizeDate,
                    CreatedBy = x.CreatedBy
                }).Distinct();

                // 总条数
                var count = await Query.CountAsync();
                var list = Query.AsParallel().Skip((input.page - 1) * input.limit).Take(input.limit).ToList();

                return (list, count);
            }
        }
        /// <summary>
        /// 获取发票清单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<IQueryable<InvoicesQueryListOutput>> BuildInvoiceQuery(InvoicesQueryInput input)
        {
            var Query = (from i in _db.Invoices
                         join ci in _db.CustomizeInvoiceItem on i.CustomizeInvoiceCode equals ci.Code into ciGroup
                         from ci in ciGroup.DefaultIfEmpty()
                         select new InvoicesQueryListOutput
                         {
                             Id = i.Id,
                             InvoiceNo = i.InvoiceNo,
                             SunPurchaseInvoiceNo = i.SunPurchaseInvoiceNo,
                             SunPurchaseInvoiceStatus = i.SunPurchaseInvoiceStatus,
                             InvoiceCode = i.InvoiceCode,
                             Type = i.Type,
                             InvoiceTime = i.InvoiceTime,
                             InvoiceAmount = i.InvoiceAmount,
                             InvoiceAmountNoTax = i.InvoiceAmountNoTax,
                             CustomerId = i.CustomerId,
                             CustomerName = i.CustomerName,
                             ProjectId = i.ProjectId,
                             ProjectName = i.ProjectName,
                             ProjectCode = i.ProjectCode,
                             Code = ci.Code,
                             CompanyId = i.CompanyId,
                             CompanyName = i.CompanyName,
                             IsCancel = i.IsCancel,
                             CreatedTime = i.CreatedTime,
                             CreatedBy = i.CreatedBy,
                             SPDStatus = i.SPDStatus,
                             BusinessDeptId = i.BusinessDeptId,
                             BusinessDeptFullPath = i.BusinessDeptFullPath,
                             BusinessDeptFullName = i.BusinessDeptFullName,
                             ChangedStatus = !string.IsNullOrEmpty(ci.RelationCode) ? null : ci.ChangedStatus,
                             BlueInvoiceNo = string.IsNullOrEmpty(ci.InvoiceNo) ? i.RelateInvoiceNo : ci.InvoiceNo,
                             SunPurchaseStatus = i.SunPurchaseStatus,
                             PreCustomizeInvoiceCode = i.PreCustomizeInvoiceCode,
                             Remark = i.Remark,
                             ReceiveAmount = i.ReceiveAmount,
                             IsInit = i.IsInit,
                             RedAmount = i.RedAmount,
                             HospitalId = i.HospitalId,
                             HospitalName = i.HospitalName,
                             AgentName = i.AgentName,
                             ProducerName = i.ProducerName,
                             SunPurchaseInvoiceCode = i.SunPurchaseInvoiceCode,
                             UpdatedBy = i.UpdatedBy,
                             PriceSource = i.PriceSource,
                             ApplyUserName = ci.CreatedBy
                         }).Distinct();
            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                Query = Query.Where(z => 1 != 1);
            }
            #region 获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            Query = await AddStrategyQueryAsync(strategryquery, Query);
            #endregion
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        input.ServiceIds = serviceIds.ToList();
                    }
                }
            }
            #region 查询条件 
            if (input.IsgreaterThanZero != null && input.IsgreaterThanZero.Value)
            {
                Query = Query.Where(x => x.InvoiceAmount > 0);
            }
            if (input.Status == 1)
            {
                Query = Query.Where(x => x.IsCancel == true);
            }
            if (input.Status == -1)
            {
                Query = Query.Where(x => x.IsCancel != true);
            }
            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.InvoiceNo) && x.InvoiceNo == input.InvoiceNo);
            }
            if (!string.IsNullOrEmpty(input.InvoiceNoLike))
            {
                Query = Query.Where(x => EF.Functions.Like(x.InvoiceNo, $"%{input.InvoiceNoLike}%"));
            }
            if (!string.IsNullOrEmpty(input.InvoiceCode))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.InvoiceCode) && EF.Functions.Like(x.InvoiceCode, $"{input.InvoiceCode}%"));
            }
            if (!string.IsNullOrEmpty(input.Type))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.Type) && x.Type.Equals(input.Type));
            }
            if (!string.IsNullOrEmpty(input.CustomerId))
            {
                Query = Query.Where(x => x.CustomerId == input.CustomerId);
            }
            if (input.IsPreInvoice.HasValue && input.IsPreInvoice.Value >= 0)
            {
                if (input.IsPreInvoice.Value == 1)
                {
                    Query = Query.Where(x => !string.IsNullOrEmpty(x.PreCustomizeInvoiceCode));
                }
                else
                {
                    Query = Query.Where(x => string.IsNullOrEmpty(x.PreCustomizeInvoiceCode));
                }
            }
            if (!string.IsNullOrWhiteSpace(input.billDateBeging) && !string.IsNullOrWhiteSpace(input.billDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(long.Parse(input.billDateBeging)).AddHours(8);
                var endTime = dateTime.AddMilliseconds(long.Parse(input.billDateEnd)).AddHours(8);
                Query = Query.Where(z => z.InvoiceTime <= endTime && z.InvoiceTime >= startTime);
                //var startTime =  DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateBeging));
                //var endTime =  DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateEnd));
                //Query = Query.Where(z => z.InvoiceTime <= DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateEnd)) && z.InvoiceTime >= DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateBeging)));
            }
            if (input.CompanyId.HasValue)
            {
                Query = Query.Where(x => x.CompanyId.HasValue && x.CompanyId == input.CompanyId);
            }
            if (input.CompanyIdList != null && input.CompanyIdList.Any())
            {
                Query = Query.Where(x => x.CompanyId.HasValue && input.CompanyIdList.ToHashSet().Contains(x.CompanyId.Value));
            }
            if (!string.IsNullOrEmpty(input.Code))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.Code) && EF.Functions.Like(x.Code, $"%{input.Code}%"));
            }
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                Query = Query.Where(x =>
                (!string.IsNullOrEmpty(x.Code) && x.Code.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.CompanyName) && x.CompanyName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.CustomerName) && x.CustomerName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.InvoiceNo) && x.InvoiceNo.Equals(input.searchKey)));
            }
            if (input.ServiceId.HasValue && !input.ServiceIds.Any())
            {
                var InvoiceNos = await (from ic in _db.InvoiceCredits
                                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                        from c in icGroup.DefaultIfEmpty()
                                        where c.ServiceId == input.ServiceId
                                        select ic.InvoiceNo).ToListAsync();
                Query = Query.Where(x => InvoiceNos.ToHashSet().Contains(x.InvoiceNo));
            }
            // 业务单元
            if (input.ServiceIds != null && input.ServiceIds.Any())
            {
                var InvoiceNos = await (from ic in _db.InvoiceCredits
                                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                        from c in icGroup.DefaultIfEmpty()
                                        where input.ServiceIds.Contains(c.ServiceId)
                                        && (input.ServiceId.HasValue ? c.ServiceId.Equals(input.ServiceId.Value) : true)
                                        select ic.InvoiceNo).ToListAsync();
                //var InvoiceNos = invoiceCredits.Select(i => i.InvoiceNo).ToList();
                Query = Query.Where(x => InvoiceNos.ToHashSet().Contains(x.InvoiceNo));

                Query = Query.Where(x => x.CustomerId != null && x.CompanyId != null);
            }
            if (input.SPDStatus.HasValue)
            {
                if (input.SPDStatus.Value != SPDStatusEnum.all)
                {
                    Query = Query.Where(x => x.SPDStatus == input.SPDStatus);
                }
                else
                {
                    //全部（除阳采）
                    Query = Query.Where(x => x.SPDStatus.HasValue);
                }
            }
            if (input.SunPurchaseStatus.HasValue)
            {
                if (input.SunPurchaseStatus.Value != SunPurchaseStatusEnum.all)
                {
                    Query = Query.Where(x => x.SunPurchaseStatus == input.SunPurchaseStatus);
                }
                else
                {
                    //全部（除SPD）
                    Query = Query.Where(x => x.SunPurchaseStatus.HasValue);
                }
            }
            if (!string.IsNullOrEmpty(input.ShipmentCode))
            {
                var InvoiceNos = await (from ic in _db.InvoiceCredits
                                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                        from c in icGroup.DefaultIfEmpty()
                                        where (!string.IsNullOrEmpty(c.ShipmentCode) && c.ShipmentCode.Contains(input.ShipmentCode))
                                        || (!string.IsNullOrEmpty(c.CustomerOrderCode) && c.CustomerOrderCode.Contains(input.ShipmentCode))
                                        select ic.InvoiceNo).ToListAsync();
                //var InvoiceNos = invoiceCredits.Select(i => i.InvoiceNo).ToList();
                Query = Query.Where(x => InvoiceNos.ToHashSet().Contains(x.InvoiceNo));
            }
            if (input.IsPassZero == 1)
            {
                Query = Query.Where(z => z.InvoiceAmount > 0);
            }
            if (!string.IsNullOrEmpty(input.ProjectName))
            {
                Query = Query.Where(z => !string.IsNullOrEmpty(z.ProjectName) && z.ProjectName.Contains(input.ProjectName));
            }
            if (input.QuantityFilter.HasValue && input.QuantityFilter.Value != 0)
            {
                if (input.QuantityFilter < 0)
                {
                    Query = Query.Where(z => z.InvoiceAmount < 0);
                }
                else
                {
                    Query = Query.Where(z => z.InvoiceAmount > 0);
                }
            }
            if (!string.IsNullOrEmpty(input.PreCustomizeInvoiceCode))
            {
                Query = Query.Where(z => z.PreCustomizeInvoiceCode == input.PreCustomizeInvoiceCode);
            }
            //核算部门
            if (!string.IsNullOrEmpty(input.BusinessDeptId))
            {
                Query = Query.Where(z => z.BusinessDeptId == input.BusinessDeptId);
            }
            if (!string.IsNullOrEmpty(input.AgentName))
            {
                Query = Query.Where(z => EF.Functions.Like(z.AgentName, $"%{input.AgentName}%"));
            }
            if (!string.IsNullOrEmpty(input.ProducerName))
            {
                Query = Query.Where(z => EF.Functions.Like(z.ProducerName, $"%{input.ProducerName}%"));
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var InvoiceNos = await (from ic in _db.InvoiceCredits
                                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                        from c in icGroup.DefaultIfEmpty()
                                        where (!string.IsNullOrEmpty(c.OrderNo) && c.OrderNo.Contains(input.OrderNo))
                                        select ic.InvoiceNo).Distinct().ToListAsync();
                Query = Query.Where(x => InvoiceNos.ToHashSet().Contains(x.InvoiceNo));
            }
            if (input.UpdatedBy != null && input.UpdatedBy.Count > 0)
            {
                Query = Query.Where(z => input.UpdatedBy.Contains(z.UpdatedBy));

            }
            if (input.ApplyUserName != null && input.ApplyUserName.Count > 0)
            {
                Query = Query.Where(z => input.ApplyUserName.Contains(z.ApplyUserName));
            }

            if (user.Data.List.First().InstitutionType == 4)
            {
                var dictionaryOutputs = await _iBDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
                var dictionaryNames = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
                // 拆分（业务单元_公司_客户）
                var newStrs = new List<string>();
                foreach (var dic in dictionaryNames)
                {
                    string[] result = dic.Split("_");
                    if (result.Count() >= 3)
                    {
                        var serviceId = result[0].ToUpper();
                        var companyId = result[1].ToUpper();
                        var customerId = result[2].ToUpper();
                        // 需要作为判断的新字符串（去掉业务单元）
                        var newStr = string.Concat(serviceId, "_", companyId, "_", customerId);
                        newStrs.Add(newStr);
                    }
                }
                var list = await Query.ToListAsync();
                // 1.终端医院为空直接展示，终端医院等于客户显示
                var ids = new List<Guid>();
                var partIds1 = list.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.Id).ToList();
                ids.AddRange(partIds1);
                // 2.托管公司配置里存在的不显示
                var partlist = list.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName).ToList();
                var invoiceNos = partlist.Select(x => x.InvoiceNo).ToList();
                if (invoiceNos != null && invoiceNos.Any() && newStrs.Any())
                {
                    var restrictedInvoiceNos = await (from ic in _db.InvoiceCredits
                                                      join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                                                      from c in icGroup.DefaultIfEmpty()
                                                      where c.ServiceId.HasValue && c.CompanyId.HasValue && c.CustomerId.HasValue && invoiceNos.ToHashSet().Contains(ic.InvoiceNo) && newStrs.ToHashSet().Contains(c.ServiceId.Value.ToString().ToUpper() + "_" + c.CompanyId.Value.ToString().ToUpper() + "_" + c.CustomerId.Value.ToString().ToUpper())
                                                      select ic.InvoiceNo).ToListAsync();
                    if (restrictedInvoiceNos != null && restrictedInvoiceNos.Any())
                    {
                        var partIds = partlist.Where(x => !restrictedInvoiceNos.ToHashSet().Contains(x.InvoiceNo)).Select(x => x.Id).ToList();
                        ids.AddRange(partIds);
                    }
                    else
                    {
                        var partIds = partlist.Select(x => x.Id).ToList();
                        ids.AddRange(partIds);
                    }
                }

                //var partIds2 = list.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName && x.CompanyId.HasValue && !string.IsNullOrEmpty(x.CustomerId) && !newStrs.Contains(x.CompanyId.Value.ToString().ToUpper() + "_" + x.CustomerId.ToUpper())).Select(x => x.Id).ToList();
                //ids.AddRange(partIds2);
                if (ids.Any())
                {
                    Query = Query.Where(x => ids.Contains(x.Id));
                }
                else
                {
                    Query = Query.Where(x => 1 != 1);
                }


                //if (string.IsNullOrEmpty(input.billDateBeging) || string.IsNullOrEmpty(input.billDateEnd))
                //{
                //    DateTime firstDayOfMonth = DateTime.Now.AddDays(1 - DateTime.Now.Day);
                //    DateTime lastDayOfNow = DateTime.Now.AddDays(1);
                //    Query = Query.Where(z => (z.InvoiceTime != null && z.InvoiceTime.Value >= firstDayOfMonth && z.InvoiceTime.Value <= lastDayOfNow));
                //}
                if (!string.IsNullOrEmpty(input.CreatedByName))
                {
                    var userInput = new GetUserInput()
                    {
                        DisplayName = input.CreatedByName
                    };
                    var userRet = await _iBDSApiClient.GetUserByNamesAsync(userInput);
                    if (userRet.Code == 200 && userRet.Data.List.Count() > 0)
                    {
                        Query = Query.Where(z => z.CreatedBy == userRet.Data.List.FirstOrDefault().Name);
                    }
                    else
                    {
                        Query = Query.Where(z => false);
                        return Query;
                    }
                }


            }
            #endregion
            return Query;
        }
        /// <summary>
        /// 获取发票金额
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<InvoiceAmountOutput> GetInvoiceAmountAsync(InvoicesQueryInput input)
        {
            //发票金额
            var query = await BuildInvoiceQuery(input);
            var invoiceAmount = await query.SumAsync(x => x.InvoiceAmount);
            var invoiceNos = await query.Select(x => x.InvoiceNo).ToListAsync();
            var recognizeReceiveItemStatusLst = new List<RecognizeReceiveItemStatusEnum> { RecognizeReceiveItemStatusEnum.Auditing, RecognizeReceiveItemStatusEnum.Auditing };
            // 计算冲销金额
            var temps = await _db.RecognizeReceiveDetails.Include(x => x.RecognizeReceiveItem).Where(x =>
            x.Type == 1 &&
            x.Value > 0 &&
            recognizeReceiveItemStatusLst.Contains(x.RecognizeReceiveItem.Status)).Select(p => new { p.Value, p.Code }).ToListAsync();
            var aWOAmount = temps.Where(p => invoiceNos.Contains(p.Code)).Sum(p => p.Value);

            aWOAmount += await query.Where(x => x.IsInit.HasValue && x.IsInit == true).SumAsync(x => x.ReceiveAmount) ?? 0;
            return new InvoiceAmountOutput
            {
                TotalInvoiceAmount = invoiceAmount,
                TotalAWOAmount = aWOAmount,
                TotalUWOAmount = invoiceAmount - aWOAmount,
            };
        }


        /// <summary>
        /// 批量打包发票发送邮箱
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task BatchSendEmailInvoices(BatchDownLoadInvoiceInput query)
        {
            if (query.CompanyIdList == null || query.CompanyIdList.Count == 0)
            {
                throw new ApplicationException("请选择公司");
            }
            if (string.IsNullOrEmpty(query.CustomerEmail))
            {
                throw new ApplicationException("请填邮箱潮信");
            }

            var jsonStr = JsonConvert.SerializeObject(query);
            _logger.LogWarning($"开始处理批量打包下载发票广播,参数：" + jsonStr);

            var messageNotification = new MessageNotificationInput();
            messageNotification.SourceModule = "批量打包下载发票";
            messageNotification.lstToUser = new List<UserInput>()
            {
                new()
                {
                    UserName=query.UserName,
                    TrueName=query.TrueName
                }
            };

            List<DaprCompanyInfoOutput> companyInfos = new List<DaprCompanyInfoOutput>();
            try
            {
                companyInfos = await _iBDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = query.CompanyIdList.Select(o => o.ToString()).ToList()
                });
                if (companyInfos == null || companyInfos.Count == 0)
                {
                    messageNotification.MsgContent = $"批量打包下载发票失败，您所选择的ID【{string.Join(",", query.CompanyIdList)}】的公司不存在。";
                    await _sendNotificationClient.SendNotification(messageNotification);

                    _logger.LogWarning($"处理批量打包下载发票：未查询到对应的公司信息");
                    return;
                }
            }
            catch (Exception ex)
            {
                messageNotification.MsgContent = $"批量打包下载发票失败，{ex.Message}。";
                await _sendNotificationClient.SendNotification(messageNotification);
                _logger.LogWarning($"开始处理批量打包下载发票广播，发送通知异常：" + ex.Message);
                return;
            }

            var sendMailInvoicesInput = new SendMailInvoicesInput() { email = query.CustomerEmail };
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                sendMailInvoicesInput.dateStart = dateTime.AddMilliseconds(long.Parse(query.billDateBeging)).AddHours(8);
                sendMailInvoicesInput.dateEnd = dateTime.AddMilliseconds(long.Parse(query.billDateEnd)).AddHours(8);
            };

            StringBuilder sb = new StringBuilder();
            foreach (var companyInfo in companyInfos)
            {
                var list = query.CompanyInvoices?.Where(x => x.CompanyId.ToString().Equals(companyInfo.companyId, StringComparison.OrdinalIgnoreCase)).Select(x => x.InvoiceNo).ToList();
                if (list == null || list.Count == 0)
                {
                    sb.Append($"公司【{companyInfo.companyName}】未有符合条件的发票。");
                    _logger.LogWarning($"开始处理批量打包下载发票广播，{companyInfo.companyName}查询发票异常。");
                    break;
                }
                if (list != null && list.Count > 1000)
                {
                    sb.Append($"公司【{companyInfo.companyName}】每次最多下载1000张发票。");
                    break;
                }
                sendMailInvoicesInput.salertaxNo = companyInfo.latestUniCode;
                sendMailInvoicesInput.invoiceNo = list;

                BaseResponseData<string> kdResponse = new BaseResponseData<string>();
                try
                {
                    kdResponse = await _kingdeeApiClient.SendEmailInvoices(sendMailInvoicesInput);
                }
                catch (Exception ex)
                {
                    sb.Append($"公司【{companyInfo.companyName}】批量打包下载发票失败：{ex.Message}。");
                    _logger.LogWarning($"开始处理批量打包下载发票广播，{companyInfo.companyName}金蝶打包下载异常：" + ex.Message);
                    break;
                }

                if (kdResponse.ErrorCode == 500)
                {
                    sb.Append($"公司【{companyInfo.companyName}】批量打包下载发票失败：{kdResponse.Message}。");
                }
                else
                {
                    sb.Append($"公司【{companyInfo.companyName}】批量打包下载发票成功。");
                }
                await Task.Delay(5000);
            }

            messageNotification.MsgContent = $"批量打包下载发票已完成：{sb.ToString()}";
            await _sendNotificationClient.SendNotification(messageNotification);
            _logger.LogWarning($"结束处理批量打包下载发票广播，：" + messageNotification.MsgContent);
        }
        /// <summary>
        /// 获取应收开票金额合计
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<InvoiceCreditsSumOutput> GetInvoiceCreditsSumByInvoiceNo(InvoiceQueryInput query)
        {
            var Query = from ic in _db.InvoiceCredits
                        join c in _db.Credits on ic.CreditId equals c.Id into icGroup
                        from c in icGroup.DefaultIfEmpty()
                        where (string.IsNullOrEmpty(query.CompanyId) ? true : c.CompanyId == Guid.Parse(query.CompanyId)) &&
                         ic.InvoiceNo.Equals(query.InvoiceNo)
                        select new InvoiceCreditsOutput
                        {
                            BillCode = c.BillCode,
                            BillDate = c.BillDate,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            SaleSystemName = c.SaleSystemName,
                            CreditAmount = ic.CreditAmount,
                            IsSureIncome = c.IsSureIncome,
                            ServiceId = c.ServiceId,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            BusinessDeptFullPath = c.BusinessDeptFullPath,
                            ShipmentCode = c.ShipmentCode,
                            Value = c.Value,
                            CreditType = c.CreditType,
                            AgentName = c.AgentName,
                            ProducerName = c.ProducerName,
                        };


            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                Query = Query.Where(z => 1 != 1);
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                //本来就没有这个业务单元权限
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        //query.ServiceId ??= serviceIds.FirstOrDefault();
                        Query = Query.Where(t => serviceIds.Contains(t.ServiceId));
                    }
                }

            }

            if (!string.IsNullOrEmpty(query.BillCode))
            {
                Query = Query.Where(z => z.BillCode != null && z.BillCode.Contains(query.BillCode));
            }
            if (!string.IsNullOrEmpty(query.RelateCode))
            {
                Query = Query.Where(z => z.RelateCode != null && z.RelateCode.Contains(query.RelateCode));
            }
            if (!string.IsNullOrEmpty(query.OrderNo))
            {
                Query = Query.Where(z => z.OrderNo != null && z.OrderNo.Contains(query.OrderNo));
            }
            var list = Query.AsParallel().ToList();
            var sum = new InvoiceCreditsSumOutput()
            {
                CreditAmount = list.Sum(p => p.CreditAmount)
            };
            return sum;
        }
    }
}
