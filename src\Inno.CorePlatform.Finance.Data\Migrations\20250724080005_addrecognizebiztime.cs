﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class addrecognizebiztime : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "BizTime",
                table: "RecognizeReceiveItem",
                type: "datetime2",
                nullable: true,
                comment: "交易时间");

            migrationBuilder.AddColumn<decimal>(
                name: "RemainingAmount",
                table: "RecognizeReceiveItem",
                type: "decimal(18,4)",
                precision: 18,
                scale: 4,
                nullable: true,
                comment: "剩余认款金额");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BizTime",
                table: "RecognizeReceiveItem");

            migrationBuilder.DropColumn(
                name: "RemainingAmount",
                table: "RecognizeReceiveItem");
        }
    }
}
