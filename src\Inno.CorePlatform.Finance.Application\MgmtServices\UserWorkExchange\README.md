# 用户工作交换功能

## 功能说明
当员工离职或工作调整时，系统会接收基础数据能力中心发送的用户工作交换广播消息，自动将相关财务业务单据的创建人更换为新的接手人员，确保业务流程不中断。

## 消息订阅
- **主题**: `pc-backend-userWorkExchange`
- **消息格式**: `UserWorkExchangeEventDto`

## 涉及的业务单据
1. **批量付款单** - 待提交、审批中状态
2. **收款认领** - 待提交、审批中状态  
3. **退款处理** - 待提交状态
4. **损失确认** - 待提交、待审核、已拒绝状态
5. **进项票** - 临时发票状态
6. **运营制作开票明细** - 待提交、审批中状态
7. **预开票申请** - 待提交状态
8. **提前付款垫资申请** - 待提交、待审核状态
9. **垫资申请单** - 待提交状态

## 使用方式

### 1. 依赖注入配置
```csharp
// 在 Program.cs 或 Startup.cs 中添加
services.AddUserWorkExchangeServices();
```

### 2. 事件处理
系统会自动接收 `pc-backend-userWorkExchange` 主题的消息并处理。

### 3. 手动触发（管理员）
```http
POST /api/UserWorkExchange/ManualExchange
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "userId": "原用户ID",
  "userName": "原用户名",
  "displayName": "原用户显示名",
  "staffId": "原员工ID",
  "targetUserId": "目标用户ID", 
  "targetUserName": "目标用户名",
  "targetDisplayName": "目标用户显示名",
  "targetStaffId": "目标员工ID"
}
```

## 日志记录
系统会详细记录每次工作交换的结果，包括：
- 处理的业务单据类型和数量
- 处理耗时
- 成功/失败状态
- 错误信息（如有）

## 监控指标
- 总处理时间
- 各业务单据类型的更新数量
- 成功/失败统计
- 错误详情

## 注意事项
1. 只处理特定状态的业务单据（如待提交、审批中等）
2. 使用事务确保数据一致性
3. 支持防重复处理机制
4. 失败时会自动加入重试队列
