﻿using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 应付查询，入参
    /// </summary>
    public class DebtQueryInput : BaseQuery
    {
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 业务单元 Id
        /// </summary>
        public Guid? ServicesId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServicesName { get; set; }

        /// <summary>
        /// 付款单位 Id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 收款单位 Id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 应付类型
        /// </summary>
        public DebtTypeEnum? DebtType { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public List<string?>? CreatedBy { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 查询类型 1包含，2不包含
        /// </summary>
        public string? billCodeQueryType { get; set; }


        /// <summary>
        /// 核算部门
        /// </summary>
        public string? department { get; set; }
        public string? businessDeptId { get; set; }

        public Guid userId { get; set; }
        public string? CurrentUserName { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary> 
        public string? purchaseCode { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? producerOrderNo { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? relateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 项目
        /// </summary>
        public string? ProjectNo { get; set; }

        /// <summary>
        /// 应付类型集合
        /// </summary>
        public List<DebtTypeEnum>? DebtTypes { get; set; }

        /// <summary>
        /// 查询负数金额
        /// </summary>
        public bool? IsNegative { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set;}

        /// <summary>
        /// 切换核算部门自动单
        /// </summary>
        public bool? IsChangeDebt { get; set; }
        /// <summary>
        /// 应收单id
        /// </summary>
        public List<Guid>? CreditIds { get; set; }

        /// <summary>
        /// 是否坏账
        /// </summary>
        public bool? IsLossAgent { get; set; }

        /// <summary>
        /// 进行发票号
        /// </summary>
        public string? InvoiceNumber  { get; set; }

        /// <summary>
        /// 是否导出
        /// </summary>
        public bool? IsExport { get; set; } = false;
    }
    /// <summary>
    /// 应收明细查询，入参
    /// </summary>
    public class DebtDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 应收明细ID
        /// </summary>
        public Guid? Id { set; get; }
        /// <summary>
        /// 预计付款计划审核单ID
        /// </summary>
        public Guid? AuditId { set; get; }
        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { set; get; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 公司 Id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string? department { get; set; }
        /// <summary>
        /// 收款单位（供应商）
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 账期类型
        /// </summary>
        public AccountPeriodTypeEnum? AccountPeriodType { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum? Status { get; set; }

        /// <summary>
        /// 数据策略权限
        /// </summary>
        public StrategyQueryInput? StrategyQuery { get; set; }
        public bool? Warnning { get; set; } = false;
        public Guid? CreditId { get; set; }
        /// <summary>
        /// 付款计划单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInItemCode { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>
        public StatusEnum? AuditStatus { get; set; }
        
        /// <summary>
        /// 用户Id
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 当前用户名
        /// </summary>
        public string? CurrentUserName { get; set; }
    }
    /// <summary>
    /// 执行明细查询，入参
    /// </summary>
    public class DebtDetailExcuteQueryInput : BaseQuery
    {
        /// <summary>
        /// 应付执行计划Id(应付明细id) 多选
        /// </summary>
        public List<Guid>? DebtDetailIds { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { set; get; }

    }

    /// <summary>
    /// 获取可冲销的应付，入参
    /// </summary>
    public class AvailableAbatmentsInput : BaseQuery
    {
        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid DebtId { get; set; }
        public string? billCode { get; set; }
        public string? classify { get; set; }
        public string? customer { get; set; }

        /// <summary>
        /// 单据开始日期
        /// </summary>
        public DateTime? billDateStart { get; set; }

        /// <summary>
        /// 单据结束日期
        /// </summary>
        public DateTime? billDateEnd { get; set; }
    }
    /// <summary>
    /// 应付冲销，入参
    /// </summary>
    public class DebtAbatmentInput
    {
        /// <summary>
        /// 起冲销的应付单Id
        /// </summary>
        public Guid DebtId { get; set; }
        /// <summary>
        /// 被冲销的应付单列表
        /// </summary>
        public List<GenerateAbtForDebtInput>? LstInput { get; set; }

        /// <summary>
        /// 应收还是应付
        /// </summary>
        public string? classify { get; set; }
    }
    /// <summary>
    /// 根据项目id查询应付总金额
    /// </summary>
    public class DebtSumAmountQueryInput
    {
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid ProjectId { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public long BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public long EndTime { get; set; }
    }
    /// <summary>
    /// 根据项目id查询应付总金额
    /// </summary>
    public class DebtPurchaseQueryInput
    {
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid ProjectId { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public long BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public long EndTime { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int PageNum { get; set; }
        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }
    }

    public class UpdateDebtDiscount
    {
        public decimal? DistributionDiscount { get; set; }

        public decimal? FinanceDiscount { get; set; }

        public decimal? SpdDiscount { get; set; }

        public decimal? TaxDiscount { get; set; }

        public decimal? CostDiscount { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string PurchaseCode { get; set; }

        public List<UpdateDebtDiscountDetail> UpdateDebtDiscountDetails { get; set; }
    }
    public class UpdateDebtDiscountDetail
    {
        /// <summary>
        /// 账期类型
        /// </summary>
        public int AccountPeriodType { get; set; }
        /// <summary>
        /// 账期金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 账期原始金额
        /// </summary>
        public decimal OriginValue { get; set; }
    }

    /// <summary>
    /// 撤销冲销应付，入参
    /// </summary>
    public class CancelAbatmentInput
    {
        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid DebtId { get; set; }

        /// <summary>
        /// 冲销记录Id集合
        /// </summary>
        public List<Guid> Ids { get; set; }
    }

    /// <summary>
    /// 更改业务部门，入参
    /// </summary>
    public class ChangeBusinessDeptInput
    {
        /// <summary>
        /// 项目id集合
        /// </summary>
        public List<Guid>? ProjectIds { get; set; }
    }
}
