﻿using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Pipelines.Sockets.Unofficial.Arenas;
using YamlDotNet.Core;
using IICApiClient = Inno.CorePlatform.Finance.Application.PortInterfaces.Clients.IICApiClient;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class InvoiceCreditAppService : IInvoiceCreditAppService
    {
        private readonly FinanceDbContext _db;
        private readonly DaprClient _daprClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInvoiceCreditRepository _invoiceCreditRepository;
        private readonly IBaseAllQueryService<CreditPo> _creditQueryService;
        private readonly ICreditQueryService _creditQueryService1;
        private readonly IBaseAllQueryService<InvoiceCreditPo> _invoiceCreditQueryService;
        private readonly IBaseAllQueryService<CustomizeInvoiceItemPo> _customizeInvoiceItemQueryService;
        private readonly IBaseAllQueryService<CustomizeInvoiceCreditPo> _customizeInvoiceCreditQueryService;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IAppServiceContextAccessor? _contextAccessor;
        private readonly IICApiClient _iCApiClient;
        private readonly ISPDApiClient _sPDApiClient;
        private readonly IInvoiceReceiptQueryService _invoiceReceiptItemQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly PortInterfaces.Clients.ISellApiClient _sellApiClient;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly IBaseAppService _baseAppService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<InvoiceCreditAppService> _logger;
        private readonly ISubLogService _subLogService;
        private readonly ICoordinateClient _coordinateclient;
        public InvoiceCreditAppService(
            IInvoiceCreditRepository invoiceCreditRepository,
            DaprClient daprClient,
            IUnitOfWork unitOfWork,
            IBaseAllQueryService<CreditPo> creditQueryService,
            ICreditQueryService creditQueryService1,
            IBaseAllQueryService<CustomizeInvoiceItemPo> customizeInvoiceItemQueryService,
            IBaseAllQueryService<CustomizeInvoiceCreditPo> customizeInvoiceCreditQueryService,
            IEasyCachingProvider easyCaching,
            IDomainEventDispatcher? deDispatcher,
            FinanceDbContext db,
            ISPDApiClient sPDApiClient,
            PortInterfaces.Clients.ISellApiClient sellApiClient,
            IInventoryApiClient inventoryApiClient,
            IBaseAllQueryService<InvoiceCreditPo> invoiceCreditQueryService,
            IICApiClient iCApiClient,
            PortInterfaces.Clients.IBDSApiClient bDSApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IInvoiceReceiptQueryService invoiceReceiptItemQueryService,
            ILogisticsApiClient logisticsApiClient,
            IBaseAppService baseAppService,
            IConfiguration configuration,
            ICoordinateClient coordinateclient,
            ILogger<InvoiceCreditAppService> logger,
            ISubLogService subLogService,
            IAppServiceContextAccessor? contextAccessor)
        {
            this._easyCaching = easyCaching;
            this._daprClient = daprClient;
            this._creditQueryService = creditQueryService;
            this._unitOfWork = unitOfWork;
            this._invoiceCreditRepository = invoiceCreditRepository;
            this._customizeInvoiceItemQueryService = customizeInvoiceItemQueryService;
            this._customizeInvoiceCreditQueryService = customizeInvoiceCreditQueryService;
            this._invoiceCreditQueryService = invoiceCreditQueryService;
            this._contextAccessor = contextAccessor;
            this._db = db;
            this._sPDApiClient = sPDApiClient;
            this._iCApiClient = iCApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._invoiceReceiptItemQueryService = invoiceReceiptItemQueryService;
            this._sellApiClient = sellApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._bDSApiClient = bDSApiClient;
            this._logisticsApiClient = logisticsApiClient;
            this._baseAppService = baseAppService;
            this._configuration = configuration;
            this._coordinateclient = coordinateclient;
            this._logger = logger;
            _subLogService = subLogService;
            _creditQueryService1 = creditQueryService1;
        }
        public async Task<BaseResponseData<int>> CreateInvoiceCredit(InvoiceCreditInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            try
            {
                input.CustomizeInvoiceCode = input.CustomizeInvoiceCode.Trim();
                var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).FirstOrDefaultAsync(p => p.Code == input.CustomizeInvoiceCode);


                var isexistCount = await _db.OutputInvoices.Where(p => p.CustomizeInvoiceCode == input.CustomizeInvoiceCode).CountAsync();
                #region 校验
                if (string.IsNullOrEmpty(input.CustomizeInvoiceCode))
                {
                    ret = BaseResponseData<int>.Failed(500, "操作失败：应收开票申请单不能为空");
                    return ret;
                }
                if (isexistCount > 0)
                {
                    ret = BaseResponseData<int>.Success("操作失败：该申开单已经开过发票");
                    return ret;
                }
                if (customizeInvoiceItem == null)
                {
                    ret = BaseResponseData<int>.Failed(500, $"操作失败：{input.CustomizeInvoiceCode}单号没有找到开票申请单");
                    return ret;
                }
                #endregion
                var creditCodeArr = new List<string>();
                if (input.Credits != null && input.Credits.Any())
                {
                    input.Credits = input.Credits.GroupBy(p => p.CreditNo).Select(p => new CreditInput
                    {
                        CreditNo = p.Key,
                        Amount = p.Sum(p => p.Amount)
                    }).ToList();
                    creditCodeArr = input.Credits.Select(p => p.CreditNo).ToList();
                }
                var credits = await _creditQueryService.GetAllListAsync(p => creditCodeArr.Contains(p.BillCode));//查到应收单
                //发票表集合
                var addInvoices = new List<InvoicePo>();
                //发票配送集合
                var invoicDeliveryOutputs = new List<InvoiceDeliveryOutput>();
                //写入发票表
                foreach (var item in input.Invoices)
                {
                    string agentName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.AgentName)).Select(p => p.AgentName).Distinct());
                    string producerName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.ProducerName)).Select(p => p.ProducerName).Distinct());
                    addInvoices.Add(new InvoicePo
                    {
                        CompanyId = customizeInvoiceItem.CompanyId,
                        CompanyName = customizeInvoiceItem.CompanyName,
                        NameCode = customizeInvoiceItem.NameCode,
                        CustomizeInvoiceCode = customizeInvoiceItem.Code,
                        Id = Guid.NewGuid(),
                        InvoiceAmount = item.InvoiceAmount,
                        InvoiceAmountNoTax = item.InvoiceAmountNoTax,
                        TaxAmount = item.TaxAmount,
                        InvoiceCheckCode = item.InvoiceCheckCode,
                        InvoiceCode = item.InvoiceCode,
                        InvoiceNo = item.InvoiceNo,
                        InvoiceTime = item.InvoiceTime,
                        Type = item.Type,
                        Remark = item.Remark,
                        CreatedBy = input.UserName,
                        CreatedTime = DateTimeOffset.Now,
                        IsCancel = false,
                        CustomerId = customizeInvoiceItem.CustomerId,
                        CustomerName = customizeInvoiceItem.CustomerName,
                        //HospitalId = credits.First().HospitalId,
                        HospitalId = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalId)).Select(p => p.HospitalId).Distinct()),
                        HospitalName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalName)).Select(p => p.HospitalName).Distinct()),
                        AgentName = string.Join(",", agentName.Split(',').Distinct()),
                        ProducerName = string.Join(",", producerName.Split(',').Distinct()),
                    });
                    if (credits.Any())
                    {
                        var servicefeeCount = credits.Where(p => p.CreditType == CreditTypeEnum.servicefee || p.CreditType == CreditTypeEnum.servicefeerevise).Count();
                        invoicDeliveryOutputs.Add(new InvoiceDeliveryOutput
                        {
                            CompanyId = customizeInvoiceItem.CompanyId,
                            CustomizeInvoiceCode = customizeInvoiceItem.Code,
                            InvoiceAmount = item.InvoiceAmount,
                            InvoiceAmountNoTax = item.InvoiceAmountNoTax,
                            TaxAmount = item.TaxAmount,
                            InvoiceCheckCode = item.InvoiceCheckCode,
                            InvoiceCode = item.InvoiceCode,
                            InvoiceNo = item.InvoiceNo,
                            InvoiceTime = item.InvoiceTime.Value,
                            InvoiceType = item.Type,
                            Remark = item.Remark,
                            InvoiceCreatedBy = input.UserName,
                            CustomerId = customizeInvoiceItem.CustomerId,
                            InvoiceStatus = 0,
                            IsNeedDelivery = servicefeeCount == credits.Count() ? false : true,
                            businessUnitId = string.Join(",", credits.Select(p => p.ServiceId).Distinct()),
                            HospitalId = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalId)).Select(p => p.HospitalId).Distinct()),
                            HospitalName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalName)).Select(p => p.HospitalName).Distinct()),
                            ProjectIds = string.Join(",", credits.Where(p =>p.ProjectId.HasValue).Select(p => p.ProjectId).Distinct()),
                        });
                    }
                }
                var blueInvoiceNoStr = string.Empty;
                var blueInvoices = await _db.Invoices.Where(p => p.InvoiceNo == input.BlueInvoiceNo).ToListAsync();
                if (blueInvoices != null && blueInvoices.Any())
                {
                    var redInvoiceNos = input.Invoices.Select(p => p.InvoiceNo);
                    var invoiceAmount = input.Invoices.Sum(p => p.InvoiceAmount);
                    if (blueInvoices != null && blueInvoices.Any())
                    {
                        foreach (var blueInvoice in blueInvoices)
                        {
                            blueInvoice.IsRedOff = true;
                            blueInvoice.RelateInvoiceNo = string.IsNullOrEmpty(blueInvoice.RelateInvoiceNo) ? string.Join(",", redInvoiceNos) : blueInvoice.RelateInvoiceNo + "," + string.Join(",", redInvoiceNos);
                            blueInvoice.RedAmount = blueInvoice.RedAmount.HasValue ? blueInvoice.RedAmount + invoiceAmount : invoiceAmount;
                        }
                    }
                    var blueInvoiceNos = blueInvoices.Select(p => p.InvoiceNo);
                    blueInvoiceNoStr = string.Join(",", blueInvoiceNos);
                    //修改红票
                    foreach (var redInvoice in addInvoices)
                    {
                        redInvoice.RelateInvoiceNo = string.IsNullOrEmpty(redInvoice.RelateInvoiceNo) ? blueInvoiceNoStr : redInvoice.RelateInvoiceNo + "," + blueInvoiceNoStr;
                    }
                }
                var invoiceCredits = new List<InvoiceCredit>();
                var createInvoiceCredit = new List<CreateInvoiceCreditSub>();
                var spd_InoviceOutputs = new List<InoviceSpdInput>();
                var outputInvioces = new List<OutputInvoicePo>();

                var index = 0;
                var creditIds = credits.Select(p => p.Id).ToHashSet();
                var invoiceCreditsOfDB = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value)).ToListAsync();
                //SPD发票
                var spdInvoiceItems = new List<SPDInvoiceitemInput>();
                var spdInvoiceDetails = new List<SPDInvoiceDetailInput>();
                var isSunPurchase = false;

                var createInvoicePushSales = new List<CreateInvoicePushSale>();
                List<string> projectCodes = new List<string>(), projectNames = new List<string>(), projectIds = new List<string>();
                //写入发票应收表
                if (input.Credits != null && input.Credits.Any())
                {
                    foreach (var item in input.Credits) //应收单
                    {
                        item.CreditNo = item.CreditNo.Trim();
                        var credit = credits.First(p => p.BillCode == item.CreditNo);
                        if (credit == null)
                        {
                            ret = BaseResponseData<int>.Failed(500, $"操作失败：应收单号【{item.CreditNo}】没有找到对应单据");
                            return ret;
                        }
                        projectCodes.Add(credit.ProjectCode);
                        projectNames.Add(credit.ProjectName);
                        projectIds.Add(credit.ProjectId.ToString());
                        foreach (var invoice in input.Invoices) //发票
                        {
                            var addInvoice = addInvoices.First(p => p.InvoiceNo == invoice.InvoiceNo);
                            addInvoice.BusinessDeptFullName = credit.BusinessDeptFullName;
                            addInvoice.BusinessDeptFullPath = credit.BusinessDeptFullPath;
                            addInvoice.BusinessDeptId = credit.BusinessDeptId;
                            var invoiceCredit = invoice.Adapt<InvoiceCredit>();
                            invoiceCredit.Id = Guid.NewGuid();
                            invoiceCredit.CreditId = credit.Id;
                            invoiceCredit.CreditAmount = item.Amount;
                            invoiceCredit.CustomizeInvoiceCode = input.CustomizeInvoiceCode;
                            invoiceCredit.CreateBy(input.UserName);
                            invoiceCredits.Add(invoiceCredit);
                            #region 构建pub消息给销售
                            var cics = new CreateInvoiceCreditSub();
                            cics.InvoiceNo = invoice.InvoiceNo;
                            cics.CreditNo = item.CreditNo;
                            cics.OrderNo = credit.SaleSource == SaleSourceEnum.Spd && credit.CreditType == CreditTypeEnum.servicefee ? credit.RelateCode : credit.OrderNo;
                            cics.Amount = item.Amount;
                            cics.CompanyId = customizeInvoiceItem.CompanyId;
                            cics.CompanyName = customizeInvoiceItem.CompanyName;
                            cics.CustomerId = customizeInvoiceItem.CustomerId;
                            cics.CustomerName = customizeInvoiceItem.CustomerName;
                            cics.InvoiceCreatedBy = input.UserName;
                            cics.InvoiceTime = invoice.InvoiceTime;
                            var cicsdList = new List<CreateInvoiceCreditSubDetail>();
                            foreach (var id in invoice.InvoicDetails)
                            {
                                var currentCid = new CustomizeInvoiceDetailPo();
                                if (customizeInvoiceItem.CustomizeInvoiceDetail != null && customizeInvoiceItem.CustomizeInvoiceDetail.Any())
                                {
                                    //金蝶开票序号从0开始
                                    var cids = customizeInvoiceItem.CustomizeInvoiceDetail.OrderBy(x => x.Sort).ToList();
                                    if (id.RowNo.HasValue && cids.Count() > id.RowNo)
                                    {
                                        currentCid = cids[id.RowNo.Value];
                                    }
                                }
                                var cicsd = new CreateInvoiceCreditSubDetail();
                                cicsd.InvoiceNo = invoice.InvoiceNo;
                                cicsd.Specification = id.ProductNo;
                                cicsd.Quantity = id.Quantity;
                                cicsd.UnitPrice = id.UnitPrice;
                                cicsd.CompanyId = customizeInvoiceItem.CompanyId;
                                cicsd.ProductId = currentCid != null ? currentCid.ProductId : null;
                                cicsdList.Add(cicsd);
                            }
                            cics.Details = cicsdList;
                            createInvoiceCredit.Add(cics);
                            #endregion
                            if (credit.SaleSource == Domain.SaleSourceEnum.Spd)
                            {
                                if (credit.CreditType == CreditTypeEnum.servicefee)
                                {
                                    if (!spd_InoviceOutputs.Select(p => p.invoiceNum).Contains(invoice.InvoiceNo))
                                    {
                                        #region spd服务费入参构建
                                        var spd_inovice = new InoviceSpdInput
                                        {
                                            amount = invoice.InvoiceAmount,
                                            billingDate = invoice.InvoiceTime,
                                            invoiceNum = !string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) ? blueInvoiceNoStr : invoice.InvoiceNo,
                                            remark = invoice.Remark,
                                            creditDetails = new List<CreditDetail>(),
                                            is_refund = !string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) ? 1 : 0
                                        };
                                        input.Credits.ForEach(it =>
                                        {
                                            var creditTemp = credits.FirstOrDefault(p => p.BillCode == it.CreditNo);
                                            spd_inovice.creditDetails.Add(new CreditDetail
                                            {
                                                creditAmount = it.Amount,
                                                creditCode = creditTemp.OrderNo
                                            });
                                        });
                                        spd_InoviceOutputs.Add(spd_inovice);
                                        #endregion
                                    }
                                }
                                else
                                {
                                    addInvoice.SPDStatus = SPDStatusEnum.waitSubmit; //待提交 
                                }
                            }
                            if (credit.SaleSource == Domain.SaleSourceEnum.SunPurchase)
                            {
                                addInvoice.SunPurchaseStatus = SunPurchaseStatusEnum.waitSubmit; //待提交
                                isSunPurchase = true;
                            }
                            var sortInvoiceDetail = customizeInvoiceItem?.CustomizeInvoiceDetail?.OrderBy(p => p.Sort).ToList();
                            foreach (var invoiceDetail in invoice.InvoicDetails)
                            {
                                if (index == 0)//只加第一遍
                                {
                                    string creditCode = item.CreditNo;
                                    if (invoiceDetail.RowNo.HasValue && sortInvoiceDetail != null && sortInvoiceDetail.Count > invoiceDetail.RowNo.Value)
                                    {
                                        creditCode = sortInvoiceDetail[invoiceDetail.RowNo.Value].CreditBillCode;
                                    }

                                    outputInvioces.Add(new OutputInvoicePo
                                    {
                                        Id = Guid.NewGuid(),
                                        CreatedBy = input.UserName,
                                        CreatedTime = DateTime.Now,
                                        CreditNo = creditCode,
                                        CustomizeInvoiceCode = input.CustomizeInvoiceCode,
                                        InvoiceCheckCode = invoice.InvoiceCheckCode,
                                        InvoiceCode = invoice.InvoiceCode,
                                        InvoiceNo = invoice.InvoiceNo,
                                        InvoiceTime = invoice.InvoiceTime,
                                        InvoiceAmount = invoice.InvoiceAmount,
                                        Type = invoice.Type,
                                        ProductName = invoiceDetail.ProductName,
                                        ProductNo = invoiceDetail.ProductNo,
                                        Quantity = invoiceDetail.Quantity,
                                        TaxRate = invoiceDetail.TaxRate,
                                        UnitPrice = invoiceDetail.UnitPrice,
                                        Unit = invoiceDetail.Unit,
                                        UnitPriceOfNoTax = invoiceDetail.UnitPriceOfNoTax,
                                        Amount = invoiceDetail.Amount,
                                        AmountOfNoTax = invoiceDetail.AmountOfNoTax,
                                        TaxAmount = invoiceDetail.TaxAmount,
                                        TaxTateCodeId = invoiceDetail.TaxTateCodeId,
                                        RowNo = invoiceDetail.RowNo,
                                    });
                                }
                            }

                            createInvoicePushSales.Add(new CreateInvoicePushSale
                            {
                                BlueInvoiceNo = input.BlueInvoiceNo,
                                CreditBillCode = credit.BillCode,
                                RedInvoiceNo = invoice.InvoiceNo,
                                RedInvoiceAmount = invoice.InvoiceAmount,
                                OrderNo = credit != null ? credit.OrderNo : string.Empty,
                                CreditType = (int)credit.CreditType,
                                RedCreditAmount = item.Amount
                            });
                        }
                        index++;
                    }
                }
                else
                {
                    foreach (var invoice in input.Invoices) //发票
                    {
                        foreach (var invoiceDetail in invoice.InvoicDetails)
                        {

                            outputInvioces.Add(new OutputInvoicePo
                            {
                                Id = Guid.NewGuid(),
                                CreatedBy = input.UserName,
                                CreatedTime = DateTime.Now,
                                CreditNo = "",
                                CustomizeInvoiceCode = input.CustomizeInvoiceCode,
                                InvoiceCheckCode = invoice.InvoiceCheckCode,
                                InvoiceCode = invoice.InvoiceCode,
                                InvoiceNo = invoice.InvoiceNo,
                                InvoiceTime = invoice.InvoiceTime,
                                InvoiceAmount = invoice.InvoiceAmount,
                                Type = invoice.Type,
                                ProductName = invoiceDetail.ProductName,
                                ProductNo = invoiceDetail.ProductNo,
                                Quantity = invoiceDetail.Quantity,
                                TaxRate = invoiceDetail.TaxRate,
                                UnitPrice = invoiceDetail.UnitPrice,
                                Unit = invoiceDetail.Unit,
                                UnitPriceOfNoTax = invoiceDetail.UnitPriceOfNoTax,
                                Amount = invoiceDetail.Amount,
                                AmountOfNoTax = invoiceDetail.AmountOfNoTax,
                                TaxAmount = invoiceDetail.TaxAmount,
                                TaxTateCodeId = invoiceDetail.TaxTateCodeId,
                                RowNo = invoiceDetail.RowNo,
                            });

                        }
                    }
                }

                var preCustomizeCode = string.Empty;
                var customizeInvoiceClassify = await _db.CustomizeInvoiceClassify.FirstAsync(p => p.Id == customizeInvoiceItem.CustomizeInvoiceClassifyId);
                if (customizeInvoiceClassify.Classify == CustomizeInvoiceClassifyEnum.Pre)
                {
                    if (customizeInvoiceClassify.Classify == CustomizeInvoiceClassifyEnum.Pre)
                    {
                        var preCustomize = await _db.PreCustomizeInvoiceItem.FirstOrDefaultAsync(p => p.Code == customizeInvoiceClassify.RelationCode);
                        if (preCustomize != null)
                        {
                            preCustomize.Status = PreCustomizeInvoiceItemStatusEnum.InvoicedNonUnion;
                            preCustomize.LastUnionDate= DateTime.Now.AddDays(30);
                            preCustomizeCode = preCustomize.Code;
                            if (!string.IsNullOrEmpty(preCustomize.ProjectCode))
                            {
                                projectCodes.Add(preCustomize.ProjectCode);
                            }
                            if (!string.IsNullOrEmpty(preCustomize.ProjectName))
                            {
                                projectNames.Add(preCustomize.ProjectName);
                            }
                            foreach (var item in addInvoices)
                            {
                                item.BusinessDeptFullName = preCustomize.BusinessDeptFullName;
                                item.BusinessDeptFullPath = preCustomize.BusinessDeptFullPath;
                                item.BusinessDeptId = preCustomize.BusinessDeptId;
                            }
                        }
                    }
                }
                if (!string.IsNullOrEmpty(customizeInvoiceItem.RelationCode))
                {
                    var temp_customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).Where(p => p.Code == customizeInvoiceItem.RelationCode).FirstOrDefaultAsync();
                    if (temp_customizeInvoiceItem != null)
                    {
                        await UpdateCreditDetail(customizeInvoiceItem.Id, 2, creditCodeArr);
                    }
                }
                else
                {
                    await UpdateCreditDetail(customizeInvoiceItem.Id, 1);
                }

                if (addInvoices.Any())
                {
                    var credits0 = credits.Where(p => p.PriceSource == PriceSourceEnum.CHANGE_APPLY).ToList(); //非集采
                    var credits1 = credits.Where(p => p.PriceSource == PriceSourceEnum.TEMP_APPLY).ToList(); //集采
                    var credits2 = credits.Where(p => p.PriceSource == PriceSourceEnum.OPERATION_APPLY).ToList(); //其它
                    foreach (var item in addInvoices)
                    {
                        if (credits1 != null && credits1.Count > 0)
                        {
                            item.PriceSource = PriceSourceEnum.TEMP_APPLY;
                        }
                        else
                        {
                            if (credits0 != null && credits0.Count > 0)
                            {
                                item.PriceSource = PriceSourceEnum.CHANGE_APPLY;
                            }
                        }
                        if (projectCodes.Any())
                        {
                            item.ProjectCode = string.Join(',', projectCodes.Distinct());
                            item.ProjectName = string.Join(',', projectNames.Distinct());
                            item.PreCustomizeInvoiceCode = preCustomizeCode;

                        }
                    }
                    await _db.Invoices.AddRangeAsync(addInvoices);
                }
                if (invoiceCredits.Any())
                {
                    invoiceCredits = invoiceCredits.Distinct().ToList();
                    await _invoiceCreditRepository.AddManyAsync(invoiceCredits);
                }
                if (outputInvioces.Any())
                {
                    await _db.OutputInvoices.AddRangeAsync(outputInvioces);
                }
                if (isSunPurchase)
                {
                    var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemCode == customizeInvoiceItem.Code).ToListAsync();
                    await AddSunPurchaseDetails(customizeInvoiceItem, addInvoices.First(), outputInvioces, customizeInvoiceCredits);
                }
                customizeInvoiceItem.RedOffsetCode = input.RedOffsetCode;
                await _unitOfWork.CommitAsync();
                if (ret.Code == CodeStatusEnum.Success && createInvoiceCredit.Any() && input.Credits != null && input.Credits.Any())
                {
                    //记录广播日志
                    var jsonStr = JsonConvert.SerializeObject(createInvoiceCredit);
                    _logger.LogInformation("推送给销售[fam-sell-createdinvoicecredit]" + jsonStr);
                    //推给销售
                    await _daprClient.PublishEventAsync<List<CreateInvoiceCreditSub>>(
                       "pubsub-default",
                       "fam-sell-createdinvoicecredit",
                        createInvoiceCredit);
                    //更新旺店通发票信息
                    await _daprClient.PublishEventAsync<List<CreateInvoiceCreditSub>>(
                    "pubsub-default",
                    "fam-ic-updateinvoicecredit",
                     createInvoiceCredit);
                    //蓝票号不为空推送消息给销售
                    if (!string.IsNullOrEmpty(input.BlueInvoiceNo))
                    {
                        var jsonStr2 = JsonConvert.SerializeObject(createInvoicePushSales);
                        _logger.LogInformation("推送给销售[fam-sell-createinvoicecredit]" + jsonStr2);
                        //推给销售
                        await _daprClient.PublishEventAsync<List<CreateInvoicePushSale>>(
                            "pubsub-default",
                            "fam-sell-createinvoicecredit",
                             createInvoicePushSales);
                    }

                    //推给库存发票配送
                    await _daprClient.PublishEventAsync<List<InvoiceDeliveryOutput>>(
                      "pubsub-default",
                      "finance-inventory-physicalInvoice",
                       invoicDeliveryOutputs);
                    var jsonStr3 = JsonConvert.SerializeObject(invoicDeliveryOutputs);
                    _logger.LogInformation("推给库存发票配送[pubsub-inventory-physicalInvoice]" + jsonStr3);
                    //推送给SPD服务费
                    if (spd_InoviceOutputs != null && spd_InoviceOutputs.Any())
                    {
                        foreach (var spd in spd_InoviceOutputs)
                        {
                            await _sPDApiClient.ReceiveInvoice(spd);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            return ret;
        }

        /// <summary>
        /// 销项发票作废【非跨月】
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelCustomizeInvoice(KingdeeCancelCustomizeInvoiceInput input)
        {
            var customize = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).Where(p => p.Code == input.billNo).FirstOrDefaultAsync();
            if (customize == null)
                return BaseResponseData<int>.Failed(500, $"操作失败，原因：未找到单号为{input.billNo}的申开单！");

            if (customize.ChangedStatus.HasValue)
                return BaseResponseData<int>.Failed(500, $"操作失败，原因：{input.billNo}的申开单已被红冲或部分红冲！");

            if (!string.IsNullOrEmpty(customize.RedOffsetCode))
                return BaseResponseData<int>.Failed(500, $"操作失败，原因：{input.billNo}的申开单是红冲单不允许作废！");


            var createInvoiceCredit = new List<CancelInvoicePushSale>();
            customize.Status = CustomizeInvoiceStatusEnum.Cancel;
            customize.UpdatedBy = input.userName;
            customize.UpdatedTime = DateTime.Now;
            var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CustomizeInvoiceCode == input.billNo).ToListAsync();

            foreach (var invoice in invoiceCredits)
            {
                invoice.IsCancel = true;
            }
            var invoices = await _db.Invoices.Where(p => p.CustomizeInvoiceCode == input.billNo).ToListAsync();
            foreach (var invoice in invoices)
            {
                invoice.IsCancel = true;
                //推送给销售
                createInvoiceCredit.Add(new CancelInvoicePushSale
                {
                    InvoiceNo = invoice.InvoiceNo
                });
            }
            var creditIds = invoiceCredits.Select(p => p.CreditId).ToList();
            var credits = await _db.Credits.Where(p => creditIds.Contains(p.Id)).AsNoTracking().ToListAsync();

            var spd_InoviceOutputs = new List<InoviceSpdInput>();
            if (credits != null && credits.Any())
            {
                foreach (var credit in credits)
                {
                    foreach (var invoice in invoices)
                    {
                        if (credit.SaleSource == Domain.SaleSourceEnum.Spd)
                        {
                            if (credit.CreditType == CreditTypeEnum.servicefee)
                            {
                                if (!spd_InoviceOutputs.Select(p => p.invoiceNum).Contains(invoice.InvoiceNo))
                                {
                                    #region spd服务费入参构建
                                    var spd_inovice = new InoviceSpdInput
                                    {
                                        amount = invoice.InvoiceAmount,
                                        billingDate = invoice.InvoiceTime,
                                        invoiceNum = invoice.InvoiceNo,
                                        remark = invoice.Remark,
                                        creditDetails = new List<CreditDetail>(),
                                        is_refund = 1
                                    };
                                    credits.ForEach(it =>
                                    {
                                        spd_inovice.creditDetails.Add(new CreditDetail
                                        {
                                            creditAmount = invoice.InvoiceAmount.Value,
                                            creditCode = it.OrderNo
                                        });
                                    });
                                    spd_InoviceOutputs.Add(spd_inovice);
                                    #endregion
                                }
                            }
                        }
                    }
                    credit.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                }
                _db.Credits.UpdateRange(credits);
                await UpdateCreditDetail(customize.Id, 3, null, false);
            }
            else
            {

                await UpdateCreditDetail(customize.Id, 3, null, true);
            }
            await _db.SaveChangesAsync();
            //推送给SPD服务费
            if (spd_InoviceOutputs != null && spd_InoviceOutputs.Any())
            {
                foreach (var spd in spd_InoviceOutputs)
                {
                    await _sPDApiClient.ReceiveInvoice(spd);
                }
            }
            //推给销售
            await _daprClient.PublishEventAsync<List<CancelInvoicePushSale>>(
                "pubsub-default",
                "fam-sell-cancelcustomizeinvoice",
                 createInvoiceCredit);
            return BaseResponseData<int>.Success("操作成功！");
        }
        /// <summary>
        /// 修改应收明细开票金额
        /// </summary>
        /// <param name="customizeInvoiceItemId"></param>
        /// <param name="opt">1=正常开票，2=红冲，3=作废</param>
        /// <returns></returns>
        private async Task UpdateCreditDetail(Guid customizeInvoiceItemId, int opt, List<string> creditBillCodes = null, bool needUpdateCredit = true)
        {
            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItemId).ToListAsync();
            if (customizeInvoiceCredits.Any())
            {
                if (opt == 1)
                {
                    //财务管理，应收开完票后，不需要更新creditdetail表，也不需要更新credit的invoicestatus
                }
                else if (opt == 2)//红冲
                {
                    var creditDetailIds = customizeInvoiceCredits.Select(p => p.CreditDetailId).Distinct().ToList();
                    var creditDetails = await _db.CreditDetails.Where(p => creditDetailIds.Contains(p.Id)).ToListAsync();
                    var creditIds = creditDetails.Select(p => p.CreditId).Distinct().ToList();
                    foreach (var item in creditDetails)
                    {
                        var invoiceAmount = customizeInvoiceCredits.Where(p => p.CreditDetailId == item.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                        var totalInvoiceAmount = item.InvoiceAmount.HasValue ? Math.Abs(item.InvoiceAmount.Value) - Math.Abs(invoiceAmount) : Math.Abs(invoiceAmount);
                        item.InvoiceAmount = item.Amount.Value > 0 ? totalInvoiceAmount : -totalInvoiceAmount;
                        item.NoInvoiceAmount = item.Amount > 0 ? Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value) : -(Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value));
                    }
                    var credits = await _db.Credits.Where(p => creditIds.Contains(p.Id)).ToListAsync();
                    foreach (var item in credits)
                    {
                        item.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                    }

                }
                else //作废
                {
                    var creditDetailIds = customizeInvoiceCredits.Select(p => p.CreditDetailId).Distinct().ToList();
                    var creditDetails = await _db.CreditDetails.Where(p => creditDetailIds.Contains(p.Id)).ToListAsync();
                    var creditIds = creditDetails.Select(p => p.CreditId).Distinct().ToList();
                    foreach (var item in creditDetails)
                    {
                        var invoiceAmount = customizeInvoiceCredits.Where(p => p.CreditDetailId == item.Id).Sum(p => p.CustomizeInvoiceDetailAmount);
                        var totalInvoiceAmount = item.InvoiceAmount.HasValue ? Math.Abs(item.InvoiceAmount.Value) - Math.Abs(invoiceAmount) : Math.Abs(invoiceAmount);
                        item.InvoiceAmount = item.Amount.Value > 0 ? totalInvoiceAmount : -totalInvoiceAmount;
                        item.NoInvoiceAmount = item.Amount > 0 ? Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value) : -(Math.Abs(item.Amount.Value) - Math.Abs(item.InvoiceAmount.Value));
                    }
                    if (needUpdateCredit)
                    {
                        var credits = await _db.Credits.Where(p => creditIds.Contains(p.Id)).ToListAsync();
                        foreach (var item in credits)
                        {
                            item.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                        }
                    }
                }
            }
            else
            {
                if (creditBillCodes != null && creditBillCodes.Any())
                {
                    var credits = await _db.Credits.Where(p => creditBillCodes.Contains(p.BillCode)).ToListAsync();
                    foreach (var item in credits)
                    {
                        item.InvoiceStatus = 0;
                    }
                }
            }
        }

        /// <summary>
        /// 预开票绑定应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CreatePreInvoiceCredit(PreInvoiceCreditInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            try
            {
                if (string.IsNullOrEmpty(input.InvoiceNo))
                {
                    return BaseResponseData<int>.Failed(500, "请选择发票");
                }
                if (input.Credits == null || !input.Credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, "请选择关联应收");
                }
                if (input.Credits.Any(p => string.IsNullOrEmpty(p.BillCode)))
                {
                    return BaseResponseData<int>.Failed(500, "请选择关联应收");
                }
                var invoiceCredits = await _db.InvoiceCredits.Where(x => x.InvoiceNo == input.InvoiceNo).ToListAsync();
                var invoiceCreditsAmount = invoiceCredits.Sum(x => x.CreditAmount);
                if (invoiceCreditsAmount + input.CreditValue > input.InvoiceAmount)
                {
                    return BaseResponseData<int>.Failed(500, $"应收总金额不能大于所选发票金额,已关联金额{invoiceCreditsAmount}");
                }
                // 应收单号集合
                var creditCodeArr = new List<string?>();
                if (input.Credits != null && input.Credits.Any())
                {
                    var creditsGroup = input.Credits.GroupBy(p => p.BillCode).Select(p => new PartCreditInfoForPreInvoice
                    {
                        BillCode = p.Key,
                        Value = p.Sum(p => p.Value)
                    }).ToList();
                    creditCodeArr = creditsGroup.Where(p => !string.IsNullOrEmpty(p.BillCode)).Select(p => p.BillCode).ToList();
                    if (creditCodeArr.Count < input.Credits.Count)
                    {
                        return BaseResponseData<int>.Failed(500, "请勿关联重复的应收单");
                    }
                }
                // 查询发票
                var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == input.InvoiceNo);
                if (invoice == null)
                {
                    return BaseResponseData<int>.Failed(500, $"{input.InvoiceNo}发票号没有找到发票");
                }

                var customizeInvoiceItem = await _customizeInvoiceItemQueryService.FirstOrDefaultAsync(p =>
                                       p.Code == invoice.CustomizeInvoiceCode);
                if (customizeInvoiceItem == null)
                {
                    ret = BaseResponseData<int>.Failed(500, $"操作失败：{invoice.CustomizeInvoiceCode}单号没有找到开票申请单");
                    return ret;
                }

                var preCustomizeInvoiceItem = await _db.PreCustomizeInvoiceItem.Where(p => p.Code == invoice.PreCustomizeInvoiceCode).FirstOrDefaultAsync();
                if (preCustomizeInvoiceItem == null)
                {
                    ret = BaseResponseData<int>.Failed(500, $"操作失败：{invoice.PreCustomizeInvoiceCode}单号没有找到预开票单");
                    return ret;
                }
                preCustomizeInvoiceItem.Status = PreCustomizeInvoiceItemStatusEnum.Complate;
                // 查到应收单
                var credits = await _creditQueryService.GetAllListAsync(p => creditCodeArr.Contains(p.BillCode));
                if (credits == null || !credits.Any())
                {
                    return BaseResponseData<int>.Failed(500, $"没有找到应收单数据");
                }
                var dbCreditBillCode = credits.Select(p => p.BillCode).Distinct().ToList();
                if (dbCreditBillCode.Count() != creditCodeArr.Count())
                {
                    var exceptCodes = creditCodeArr.Except(dbCreditBillCode).Distinct();
                    return BaseResponseData<int>.Failed(500, $"{string.Join(",", exceptCodes)}没有找到应收单数据");
                }
                var creditsOfinvoiced = credits.Where(p => p.InvoiceStatus == InvoiceStatusEnum.invoiced).ToList();
                string errorStr = "";
                if (creditsOfinvoiced.Any())
                {
                    errorStr += string.Join(",", creditsOfinvoiced.Select(p => p.BillCode).Distinct()) + "应收单已开票；";
                }
                var creditsOfnoNeed = credits.Where(p => p.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed).ToList();
                if (creditsOfnoNeed.Any())
                {
                    errorStr += string.Join(",", creditsOfnoNeed.Select(p => p.BillCode).Distinct()) + "应收单已经设置为无需开票；";
                }

                var creditIds = credits.Select(p => p.Id).ToList();
                var creditDetailsOfInvoice = await _db.CreditDetails.Include(p => p.Credit).Where(p => creditIds.Contains(p.CreditId) && p.InvoiceAmount > 0).ToListAsync();
                if (creditDetailsOfInvoice.Any())
                {
                    errorStr += string.Join(",", creditDetailsOfInvoice.Select(p => p.Credit.BillCode).Distinct()) + "应收单已部分开票；";
                }
                if (!string.IsNullOrEmpty(errorStr))
                {
                    return BaseResponseData<int>.Failed(500, $"操作失败,原因；{errorStr}");
                }
                invoice.BusinessDeptFullName = credits.First().BusinessDeptFullName;
                invoice.BusinessDeptFullPath = credits.First().BusinessDeptFullPath;
                invoice.BusinessDeptId = credits.First().BusinessDeptId;

                var associatedInvoicingInput = new AssociatedInvoicingInput();
                var invoiceAndReceivablesVos = new List<AssociatedInvoicing>();
                foreach (var item in input.Credits)
                {
                    invoiceAndReceivablesVos.Add(new AssociatedInvoicing
                    {
                        amount = item.Value.HasValue ? item.Value.Value : 0,
                        invoiceNumber = input.InvoiceNo,
                        receivablesNumber = item.BillCode ??= string.Empty,
                        Operator = input.UserName
                    });
                }
                associatedInvoicingInput.InvoiceAndReceivablesVos = invoiceAndReceivablesVos;
                // 推送金蝶
                ret = await _kingdeeApiClient.AssociatedInvoicing(associatedInvoicingInput);
                if (ret.Code != CodeStatusEnum.Success)
                {
                    return ret;
                }

                var servicefeeCount = credits.Where(p => p.CreditType == CreditTypeEnum.servicefee || p.CreditType == CreditTypeEnum.servicefeerevise).Count();
                // 发票应收关系表
                var invoiceCreditsDB = new List<InvoiceCredit>();
                // 发票配送集合
                var invoicDeliveryOutputs = new List<InvoiceDeliveryOutput>();
                invoicDeliveryOutputs.Add(new InvoiceDeliveryOutput
                {
                    CompanyId = invoice.CompanyId,
                    CustomizeInvoiceCode = invoice.CustomizeInvoiceCode,
                    InvoiceAmount = invoice.InvoiceAmount,
                    InvoiceAmountNoTax = invoice.InvoiceAmountNoTax,
                    TaxAmount = invoice.TaxAmount,
                    InvoiceCheckCode = invoice.InvoiceCheckCode,
                    InvoiceCode = invoice.InvoiceCode,
                    InvoiceNo = invoice.InvoiceNo,
                    InvoiceTime = invoice.InvoiceTime.Value,
                    InvoiceType = invoice.Type,
                    Remark = invoice.Remark,
                    InvoiceCreatedBy = invoice.CreatedBy,
                    CustomerId = invoice.CustomerId,
                    InvoiceStatus = 0,                     
                    IsNeedDelivery = servicefeeCount == credits.Count() ? false : true,
                    HospitalId = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalId)).Select(p => p.HospitalId).Distinct()),
                    HospitalName = string.Join(",", credits.Where(p => !string.IsNullOrEmpty(p.HospitalName)).Select(p => p.HospitalName).Distinct()),
                    businessUnitId = string.Join(",", credits.Select(p => p.ServiceId).Distinct()),
                    ProjectIds = string.Join(",", credits.Where(p => p.ProjectId.HasValue).Select(p => p.ProjectId).Distinct()),
                });
                // 构建消息
                var createInvoiceCredit = new List<CreateInvoiceCreditSub>();
                var createInvoicePushSales = new List<CreateInvoicePushSale>();
                // spd
                var spd_InoviceOutputs = new List<InoviceSpdInput>();
                if (credits.Any())
                {
                    foreach (var item in input.Credits) //应收单
                    {
                        item.BillCode = item.BillCode.Trim();
                        var credit = credits.First(p => p.BillCode == item.BillCode);
                        if (credit == null)
                        {
                            ret = BaseResponseData<int>.Failed(500, $"操作失败：应收单号【{item.BillCode}】没有找到对应单据");
                            return ret;
                        }
                        var invoiceCredit = new InvoiceCredit();
                        invoiceCredit.InvoiceNo = invoice.InvoiceNo;
                        invoiceCredit.InvoiceCode = invoice.InvoiceCode;
                        invoiceCredit.InvoiceCheckCode = invoice.InvoiceCheckCode;
                        invoiceCredit.InvoiceTime = invoice.InvoiceTime;
                        invoiceCredit.Type = invoice.Type;
                        invoiceCredit.InvoiceAmount = invoice.InvoiceAmount;
                        invoiceCredit.InvoiceAmountNoTax = invoice.InvoiceAmountNoTax;
                        invoiceCredit.TaxAmount = invoice.TaxAmount;
                        invoiceCredit.Id = Guid.NewGuid();
                        invoiceCredit.CreditId = credit.Id;
                        invoiceCredit.CreditAmount = item.Value;
                        invoiceCredit.CustomizeInvoiceCode = input.Code;
                        invoiceCredit.CreateBy(input.UserName);
                        invoiceCreditsDB.Add(invoiceCredit);
                        #region 构建pub消息给销售
                        createInvoiceCredit.Add(new CreateInvoiceCreditSub
                        {
                            InvoiceNo = input.InvoiceNo,
                            CreditNo = item.BillCode,
                            OrderNo = credit.SaleSource == SaleSourceEnum.Spd && credit.CreditType == CreditTypeEnum.servicefee ? credit.RelateCode : credit.OrderNo,
                            Amount = item.Value.HasValue ? item.Value.Value : 0,
                        });
                        #endregion
                        if (credit.SaleSource == Domain.SaleSourceEnum.Spd)
                        {
                            if (credit.CreditType == CreditTypeEnum.servicefee)
                            {
                                if (!spd_InoviceOutputs.Select(p => p.invoiceNum).Contains(invoice.InvoiceNo))
                                {
                                    #region spd服务费入参构建
                                    var spd_inovice = new InoviceSpdInput
                                    {
                                        amount = invoice.InvoiceAmount,
                                        billingDate = invoice.InvoiceTime,
                                        invoiceNum = invoice.InvoiceNo,
                                        remark = invoice.Remark,
                                        creditDetails = new List<CreditDetail>(),
                                        is_refund = !string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) ? 1 : 0
                                    };
                                    input.Credits.ForEach(it =>
                                    {
                                        var creditTemp = credits.FirstOrDefault(p => p.BillCode == it.BillCode);
                                        spd_inovice.creditDetails.Add(new CreditDetail
                                        {
                                            creditAmount = it.Value.HasValue ? it.Value.Value : 0,
                                            creditCode = creditTemp.OrderNo
                                        });
                                    });
                                    spd_InoviceOutputs.Add(spd_inovice);
                                    #endregion
                                }
                            }

                        }

                        createInvoicePushSales.Add(new CreateInvoicePushSale
                        {
                            BlueInvoiceNo = input.BlueInvoiceNo,
                            CreditBillCode = credit.BillCode,
                            RedInvoiceNo = input.InvoiceNo,
                            RedInvoiceAmount = input.InvoiceAmount,
                            OrderNo = credit != null ? credit.OrderNo : string.Empty,
                            CreditType = (int)credit.CreditType,
                            RedCreditAmount = item.Value
                        });
                    }
                    // 应收状态修改为已开票
                    foreach (var item in credits)
                    {
                        item.InvoiceStatus = InvoiceStatusEnum.invoiced;
                    }
                    _db.Credits.UpdateRange(credits);
                }
                if (invoiceCreditsDB.Any())
                {
                    invoiceCredits = invoiceCredits.Distinct().ToList();
                    await _invoiceCreditRepository.AddManyAsync(invoiceCreditsDB);
                }
                await _unitOfWork.CommitAsync();
                if (ret.Code == CodeStatusEnum.Success && createInvoiceCredit.Any() && input.Credits != null && input.Credits.Any())
                {
                    //记录广播日志
                    var jsonStr = JsonConvert.SerializeObject(createInvoiceCredit);
                    _subLogService.LogAzure("CreatePreInvoiceCredit", jsonStr, "推送给销售[fam-sell-createdinvoicecredit]");
                    //推给销售
                    await _daprClient.PublishEventAsync<List<CreateInvoiceCreditSub>>(
                        "pubsub-default",
                        "fam-sell-createdinvoicecredit",
                         createInvoiceCredit);
                    //更新旺店通发票信息
                    await _daprClient.PublishEventAsync<List<CreateInvoiceCreditSub>>(
                    "pubsub-default",
                    "fam-ic-updateinvoicecredit",
                     createInvoiceCredit);
                    //蓝票号不为空推送消息给销售
                    if (!string.IsNullOrEmpty(input.BlueInvoiceNo))
                    {
                        //推给销售
                        await _daprClient.PublishEventAsync<List<CreateInvoicePushSale>>(
                            "pubsub-default",
                            "fam-sell-createinvoicecredit",
                             createInvoicePushSales);
                    }
                    //推给库存发票配送
                    await _daprClient.PublishEventAsync<List<InvoiceDeliveryOutput>>(
                      "pubsub-default",
                      "finance-inventory-physicalInvoice",
                       invoicDeliveryOutputs);

                    //推送给SPD服务费
                    if (spd_InoviceOutputs != null && spd_InoviceOutputs.Any())
                    {
                        foreach (var spd in spd_InoviceOutputs)
                        {
                            await _sPDApiClient.ReceiveInvoice(spd);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            return ret;
        }

        /// <summary>
        /// 重推SPD商务平台（仅限贵州致思蒲达企业管理有限公司的纸质专用发票）
        /// </summary>
        /// <param name="invoiceNos"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RefreshPushSPD(List<string?> invoiceNos)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            try
            {
                var invoices = await _db.Invoices.Where(x => invoiceNos.Contains(x.InvoiceNo)).ToListAsync();
                var ciiCodes = invoices.Select(x => x.CustomizeInvoiceCode).ToList();
                var ciis = await _db.CustomizeInvoiceItem.Where(x => ciiCodes.Contains(x.Code)).ToListAsync();
                var invoiceCredits = await _db.InvoiceCredits.Where(x => invoiceNos.Contains(x.InvoiceNo)).ToListAsync();
                var creditIds = invoiceCredits.Select(x => x.CreditId).ToList();
                var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
                // 组装入参
                var spd_InoviceOutputs = new List<InoviceSpdInput>();
                foreach (var invoice in invoices)
                {
                    if (invoice.CompanyName != "贵州致思蒲达企业管理有限公司" && invoice.Type != "纸质专用发票")
                    {
                        return BaseResponseData<int>.Failed(500, "重推失败，仅可重推贵州致思蒲达企业管理有限公司的纸质专用发票！");
                    }
                    var customizeInvoiceItem = ciis.FirstOrDefault(x => x.Code == invoice.CustomizeInvoiceCode);
                    var spd_inovice = new InoviceSpdInput
                    {
                        amount = invoice.InvoiceAmount,
                        billingDate = invoice.InvoiceTime,
                        invoiceNum = invoice.InvoiceNo,
                        remark = invoice.Remark,
                        creditDetails = new List<CreditDetail>(),
                        is_refund = customizeInvoiceItem != null && !string.IsNullOrEmpty(customizeInvoiceItem.RelationCode) ? 1 : 0
                    };
                    var currentInvocieCredits = invoiceCredits.Where(x => x.InvoiceNo == invoice.InvoiceNo).ToList();
                    foreach (var ic in currentInvocieCredits)
                    {
                        var credit = credits.FirstOrDefault(x => x.Id == ic.CreditId);
                        if (credit != null && credit.SaleSource == Domain.SaleSourceEnum.Spd && credit.CreditType == CreditTypeEnum.servicefee)
                        {
                            spd_inovice.creditDetails.Add(new CreditDetail
                            {
                                creditAmount = credit.Value,
                                creditCode = credit.OrderNo ??= string.Empty
                            });
                        }
                    }
                    // SPD服务费应收加入推送
                    if (spd_inovice.creditDetails != null && spd_inovice.creditDetails.Any())
                    {
                        spd_InoviceOutputs.Add(spd_inovice);
                    }
                }
                //推送给SPD服务费
                if (spd_InoviceOutputs != null && spd_InoviceOutputs.Any())
                {
                    foreach (var spd in spd_InoviceOutputs)
                    {
                        await _sPDApiClient.ReceiveInvoice(spd);
                        await Task.Delay(5000);
                    }
                }
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败：" + ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 获取开票明细
        /// </summary>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> AddSunPurchaseDetails(
            CustomizeInvoiceItemPo CustomizeInvoice,
            InvoicePo invoice,
            List<OutputInvoicePo> outputInvoices,
            List<CustomizeInvoiceCreditPo> customizeInvoiceCredits,
            string sunPurchaseRelateCode = ""
            )
        {
            var ret = BaseResponseData<int>.Success("操作成功");


            var SunPurchaseInvoiceDetails = new List<SunPurchaseInvoiceDetailPo>();

            if (outputInvoices != null && outputInvoices.Any())
            {
                var creditBillCodes = CustomizeInvoice.CustomizeInvoiceDetail.Select(p => p.CreditBillCode).Distinct().ToList();
                var creditcodes = string.Join(",", creditBillCodes).Split(',');
                var credits = await _db.Credits.Where(p => creditcodes.Contains(p.BillCode)).AsNoTracking().ToListAsync();
                var relateCodes = credits.Select(p => p.RelateCode).ToList();

                #region 获取医保代码
                if (string.IsNullOrEmpty(sunPurchaseRelateCode))
                {
                    sunPurchaseRelateCode = credits.First().SunPurchaseRelatecode;
                }
                var logisticDetails = await _logisticsApiClient.getFullDetails(new ShycShipmentDetaillnput
                {
                    purchaseCode = sunPurchaseRelateCode
                });
                #endregion

                int index = 1;
                foreach (var ldetail in logisticDetails.Data)
                {
                    var credit = credits.Where(p => p.OrderNo == ldetail.reportCode || p.RelateCode == ldetail.reportCode).FirstOrDefault();
                    if (credit != null)
                    {
                        string medicalCode = string.Empty;
                        var invoiceDetail = CustomizeInvoice.CustomizeInvoiceDetail.FirstOrDefault(p => p.ProductId == ldetail.productId);
                        if (customizeInvoiceCredits.Any())
                        {
                            var customizeInvoiceDetailIds = customizeInvoiceCredits.Where(p => p.ProductId == ldetail.productId).Select(p => p.CustomizeInvoiceDetailId);
                            invoiceDetail = CustomizeInvoice.CustomizeInvoiceDetail.FirstOrDefault(p => customizeInvoiceDetailIds.Contains(p.Id));
                        }
                        if (invoiceDetail != null)
                        {
                            var sunPurchase = new SunPurchaseInvoiceDetailPo
                            {
                                Id = Guid.NewGuid(),
                                CreatedBy = "none",
                                SXH = index.ToString(),
                                SFWPSFP = "0",
                                XSDDH = ldetail.billCode,
                                SFCH = invoice.InvoiceAmount < 0 ? "1" : "0",
                                HCTBDM = ldetail.sunPurchaseProductNoCode,//耗材统筹代码
                                QYBDDM = ldetail.customerProductNoCode ?? "",//企业本地代码
                                GGXHSM = ldetail.specification,
                                SPSL = ldetail.quantity.Value,
                                WSDJ = invoiceDetail.Price / (invoiceDetail.TaxRate / 100 + 1),
                                HSDJ = invoiceDetail.Price,
                                SL = invoiceDetail.TaxRate,
                                SE = invoiceDetail.TaxAmount,
                                PFJ = 0,
                                LSJ = 0,
                                GLMXBH = ldetail.purchaseDetailCode,
                                ProductId = invoiceDetail.ProductId.Value,
                                ProductNo = ldetail.productNo,
                                PurchaseCode = ldetail.purchaseCode,
                                InvoiceId = invoice.Id
                            };

                            sunPurchase.HSJE = sunPurchase.SPSL * sunPurchase.HSDJ;
                            sunPurchase.SCPH = ldetail.lotNo;
                            sunPurchase.SCRQ = DateTimeHelper.GetDateTime((long)ldetail.produceDate.Value);
                            sunPurchase.YXRQ = DateTimeHelper.GetDateTime((long)ldetail.validDate.Value);
                            sunPurchase.ZCZH = ldetail.registrationNo ?? "";
                            SunPurchaseInvoiceDetails.Add(sunPurchase);
                        }
                        else
                        {

                        }
                        index++;
                    }
                }

            }
            await _db.SunPurchaseInvoiceDetails.AddRangeAsync(SunPurchaseInvoiceDetails);
            return ret;

        }
        /// <summary>
        /// 推送发票给SPD
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> PushInvoiceToSPD(PushSPDInvoicesInput input)
        {
            var ret = BaseResponseData<string>.Success();
            var invoices = await _db.Invoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            if (invoices == null || !invoices.Any())
            {
                return BaseResponseData<string>.Failed(500, "操作失败，没有找到发票数据");
            }
            if (invoices.Select(p => p.CompanyId).Distinct().Count() > 1)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，不是同一公司的发票不能同时提交");
            }
            if (invoices.Where(p => p.IsCancel.HasValue && p.IsCancel.Value).Count() > 0)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，该发票已作废无法操作");
            }
            var customizeInvoiceCodes = invoices.Select(p => p.CustomizeInvoiceCode).ToList();
            var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).
                                        Where(p => customizeInvoiceCodes.Contains(p.Code)).AsNoTracking().ToListAsync();

            if (customizeInvoiceItems == null || !customizeInvoiceItems.Any())
            {
                return BaseResponseData<string>.Failed(500, "操作失败，没有找到申请开票数据");
            }
            if (invoices.Select(p => p.CustomerId).Distinct().Count() > 1)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，不是同一客户的发票不能同时提交");
            }
            var invoiceNos = invoices.Select(p => p.InvoiceNo).ToList();
            switch (invoices.First().CustomerId.ToLower())
            {
                case "77b8a61a-dd56-47e1-bc89-eed92bbfd3c3": //首都医科大学附属北京安贞医院
                case "00df7bcd-58de-46f7-a555-a48a3a50c05d": //首都医科大学附属北京潞河医院 
                    ret = await PushAz(new PushAzInvoicesInput { InvoiceNos = invoiceNos });
                    break;
                case "4f70f969-b3e0-4c1a-bd32-15bd090a66a9"://上海市普陀区利群医院 
                    ret = await PushVanx(new PushAzInvoicesInput { InvoiceNos = invoiceNos });
                    break;
                case "bc975539-e5bd-4f18-b9c8-0c5638517430"://复旦大学附属华东医院 
                    ret = await PushYiDao(new PushAzInvoicesInput { InvoiceNos = invoiceNos });
                    break;
                default:
                    ret = await PushSPD(input);
                    break;
            }
            return ret;
        }

        /// <summary>
        /// 推送给SPD
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<string>> PushSPD(PushSPDInvoicesInput input)
        {
            var ret = BaseResponseData<string>.Success();
            var invoices = await _db.Invoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var customizeInvoiceCodes = invoices.Select(p => p.CustomizeInvoiceCode).ToList();
            var customizeInvoiceItems = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).
                                        Where(p => customizeInvoiceCodes.Contains(p.Code)).AsNoTracking().ToListAsync();
            var customizeInvoiceClassifyIds = customizeInvoiceItems.Select(p => p.CustomizeInvoiceClassifyId).ToList();

            var customizeInvoiceItemIds2 = _db.CustomizeInvoiceItem.Where(p => customizeInvoiceClassifyIds.Contains(p.CustomizeInvoiceClassifyId)).Select(p => p.Id);
            var customizeInvoiceItemIds3 = customizeInvoiceItems.Select(p => p.Id).ToList();
            var customizeInvoiceItemIds = customizeInvoiceItemIds2.Union(customizeInvoiceItemIds3).ToHashSet();

            var subDetails = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceItemIds.Contains(p.CustomizeInvoiceItemId)).Select(p => new
            {
                Quantity = p.Quantity,
                Price = p.Price,
                ProductId = p.ProductId,
                ProductNo = p.ProductNo,
                CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                CreditBillCode = p.CreditBillCode
            }).ToListAsync();
            var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceItemIds.Contains(p.CustomizeInvoiceItemId)).Select(p => new
            {
                Quantity = p.Quantity,
                Price = p.Price,
                ProductId = p.ProductId,
                ProductNo = p.ProductNo,
                CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                CreditBillCode = p.CreditCode
            }).ToListAsync();
            if (customizeInvoiceCredits.Any())
            {
                subDetails = customizeInvoiceCredits;
            }
            var credits = await _db.InvoiceCredits.Include(p => p.Credit).Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).AsNoTracking().Select(p => p.Credit).ToListAsync();
            if (credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).Select(p => p.SaleType).Distinct().Count() > 1)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，销售出库和暂存核销应收不能同时提交");
            }
            if (credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).Select(p => p.ShipmentCode).Distinct().Count() > 1)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，应收三方单号不同不能同时提交");
            }
            var spdInvoiceInput = new SPDInvoiceInput
            {
                CompanyId = customizeInvoiceItems.First().CompanyId.Value,
                CustomerId = customizeInvoiceItems.First().CustomerId,
                invoice_items = new List<SPDInvoiceitemInput>()
            };
            var shipmentCode = credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).First().ShipmentCode;
            var outputInvoices = await _db.OutputInvoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            foreach (var invoice in invoices)
            {
                var tempoutputInvoices = outputInvoices.Where(p => p.InvoiceNo == invoice.InvoiceNo).ToList();
                var item = new SPDInvoiceitemInput
                {
                    create_time = invoice.InvoiceTime.Value,
                    invoice_check_code_price = "",
                    invoice_code = invoice.InvoiceCode,
                    invoice_num = invoice.InvoiceNo ??= string.Empty,
                    price = invoice.InvoiceAmount.HasValue ? invoice.InvoiceAmount.Value : 0,
                    billing_date = invoice.InvoiceTime.Value,
                    details = new List<SPDInvoiceDetailInput>(),
                    remark = invoice.Remark,
                };
                #region  
                // 专用发票时填写开具金额，
                // 普通发票填写检验码后六位,
                // 全电发票(无发票代码)填写价税合计金额  
                //InvoiceTypeEnum
                if (invoice.Type.Contains("数电票"))    // 全电发票(无发票代码)填写价税合计金额 
                {
                    item.invoice_check_code_price = (invoice.InvoiceAmount.HasValue ? invoice.InvoiceAmount.Value : 0).ToString();
                }
                else if (invoice.Type.Contains("普通") && !string.IsNullOrEmpty(invoice.InvoiceCheckCode)) // 普通发票填写检验码后六位,
                {
                    item.invoice_check_code_price = invoice.InvoiceCheckCode.Substring(invoice.InvoiceCheckCode.Length - 6, 6);
                }
                else if (invoice.Type.Contains("专用")) // 普通发票填写检验码后六位,
                {
                    item.invoice_check_code_price = (invoice.InvoiceAmount.HasValue ? invoice.InvoiceAmount.Value : 0).ToString();
                }
                #endregion
                spdInvoiceInput.invoice_items.Add(item);
                var customizeInvoiceItem = customizeInvoiceItems.Where(p => p.Code == invoice.CustomizeInvoiceCode).First();
                var tempsubDetails = subDetails.Where(p => p.CustomizeInvoiceItemId == customizeInvoiceItem.Id).ToList();
                foreach (var subitem in tempsubDetails)
                {
                    if (subitem.Quantity != 0 && subitem.Price != 0)
                    {
                        var credit = credits.FirstOrDefault(p => subitem.CreditBillCode.Equals(p.BillCode));
                        if (credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).First().SaleType == SaleTypeEnum.SaleOut)
                        {
                            var ret_code = string.Empty;
                            if (subitem.Quantity < 0 && credit.OrderNo.ToLower().Contains("rt-"))
                            {
                                ret_code = credit?.OrderNo;
                            }
                            item.details.Add(new SPDInvoiceDetailInput
                            {
                                count = subitem.Quantity,
                                price = subitem.Price,
                                product_id = subitem.ProductId.ToString(),
                                product_no = subitem.ProductNo,
                                ret_code = ret_code,
                                consume_code = credit?.RedReversalConsumNo
                            });
                        }
                        else
                        {
                            item.details.Add(new SPDInvoiceDetailInput
                            {
                                count = subitem.Quantity,
                                price = subitem.Price,
                                product_id = subitem.ProductId.ToString(),
                                product_no = subitem.ProductNo,
                                consume_code = credit?.RedReversalConsumNo
                            });
                        }
                    }
                }
                if (credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).First().SaleType == SaleTypeEnum.SaleOut)
                {
                    invoice.SPDStatus = SPDStatusEnum.Complate;
                }
                else
                {
                    invoice.SPDStatus = SPDStatusEnum.waitAudit;
                }
            }

            var jsonStr = JsonConvert.SerializeObject(spdInvoiceInput);
            if (credits.Where(p => !string.IsNullOrEmpty(p.ShipmentCode)).First().SaleType == SaleTypeEnum.SaleOut)
            {
                spdInvoiceInput.distribute_code = shipmentCode;
                var creditIds = credits.Select(p => p.Id).ToHashSet();
                var invoiceCredits = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value)).ToListAsync();
                if (invoiceCredits.Sum(p => p.CreditAmount) > invoiceCredits.Sum(p => p.InvoiceAmount))
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，第三方单号{shipmentCode},发票没有完全开票");
                }
                _logger.LogInformation($"推送_iCApiClient.ICSPDWithInTemp{spdInvoiceInput.apply_code},报文：{jsonStr}");
                await _subLogService.LogAsync("PushSPD", jsonStr, $"推送_iCApiClient.ICSPDWithInTemp{spdInvoiceInput.apply_code}");
                ret = await _iCApiClient.ICSPDWithInTemp(spdInvoiceInput);
                if (ret.Code == CodeStatusEnum.Success)
                {
                    await _unitOfWork.CommitAsync();
                }
            }
            else
            {
                spdInvoiceInput.apply_code = shipmentCode;
                var queryInput = new SPDQueryAmountInput();
                var codes = new List<string>();
                foreach (var item in credits)
                {
                    codes.Add(item.ShipmentCode);
                }
                queryInput.codes = codes.Distinct().ToList();
                var spdAmountData = await _iCApiClient.ICGetSPDAmount(queryInput);
                if (spdAmountData != null && spdAmountData.Code == CodeStatusEnum.Success && spdAmountData.Data.Count > 0)
                {
                    var spdAmountEntity = spdAmountData.Data.FirstOrDefault();
                    if (spdAmountEntity != null)
                    {
                        var amount = spdAmountEntity.Amount;
                        var customerId = Guid.Parse(spdInvoiceInput.CustomerId);
                        spdAmountEntity = spdAmountData.Data.Where(p => p.CustomerId != null && p.CustomerId.Value == customerId).FirstOrDefault();
                        if (spdAmountEntity != null)
                        {
                            amount = spdAmountEntity.Amount;
                        }
                        if (Math.Abs(amount.Value) > Math.Abs(invoices.Sum(p => p.InvoiceAmount).Value))
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败，第三方单号{shipmentCode},发票没有完全开票");
                        }
                    }
                }
                else
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，未获取到三方单号【{spdInvoiceInput.apply_code}】的金额");
                }


                await _subLogService.LogAsync("集成平台", jsonStr, $"推送集成平台_iCApiClient.ICSPDSupplierFinish{spdInvoiceInput.apply_code}");
                ret = await _iCApiClient.ICSPDSupplierFinish(spdInvoiceInput);
            }

            if (ret.Code == CodeStatusEnum.Success)
            {
                await _unitOfWork.CommitAsync();
            }
            return ret;
        }
        /// <summary>
        /// 推送给利群
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<string>> PushVanx(PushAzInvoicesInput input)
        {
            var invoices = await _db.Invoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var outputInvoices = await _db.OutputInvoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var creditIds = await _db.InvoiceCredits.Where(x => input.InvoiceNos.Contains(x.InvoiceNo)).Select(x => x.CreditId).ToListAsync();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
            // 安贞入参
            var vanxInput = new UploadInvoiceVanxDto
            {
                CompanyId = invoices[0].CompanyId.Value,
                CustomerId = Guid.Parse(invoices[0].CustomerId),
            };
            var codes = new List<string>();
            var vanxDetails = new List<UploadInvoiceVanxData>();
            List<SpdInvoiceApplyDetailOutput> applyDetailsRet = new List<SpdInvoiceApplyDetailOutput>();
            foreach (var credit in credits)
            {
                if (string.IsNullOrEmpty(credit.ShipmentCode))
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}三方单号为空！");
                }
                if (!credit.CustomerId.HasValue)
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}客户id为空！");
                }
                codes.Add(credit.ShipmentCode);
            }
            var spdInput = new SpdInvoiceApplyDetailInput()
            {
                Code = codes.First(),
                CustomerId = credits[0].CustomerId.Value
            };
            var applyDetailsRetTemp = await _iCApiClient.ICSPDInvoiceApplyDetails(spdInput);
            if (applyDetailsRetTemp.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，三方单号{spdInput.Code}获取SPD发票详情失败！");
            }
            if (applyDetailsRetTemp.Data != null && applyDetailsRetTemp.Data.Any())
            {
                applyDetailsRet.AddRange(applyDetailsRetTemp.Data);
            }
            foreach (var invoice in invoices)
            {
                var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).
                                  Where(p => invoice.CustomizeInvoiceCode.Equals(p.Code)).AsNoTracking().FirstOrDefaultAsync();
                var subDetails = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                {
                    Quantity = p.Quantity,
                    Price = p.Price,
                    ProductId = p.ProductId,
                    ProductNo = p.ProductNo,
                    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                    CreditBillCode = p.CreditBillCode
                }).ToListAsync();
                //var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                //{
                //    Quantity = p.Quantity,
                //    Price = p.Price,
                //    ProductId = p.ProductId,
                //    ProductNo = p.ProductNo,
                //    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                //    CreditBillCode = p.CreditCode
                //}).ToListAsync();
                //if (customizeInvoiceCredits.Any())
                //{
                //    subDetails = customizeInvoiceCredits;
                //}


                foreach (var subDetail in subDetails)
                {
                    var applyDetails = applyDetailsRet.Where(p => p.ProductId.Equals(subDetail.ProductId) &&
                                                                 Math.Abs(p.Quantity.Value) - Math.Abs(p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0) > 0 &&
                                                                  p.Price == subDetail.Price)
                                                      .OrderBy(p => p.Quantity - (p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0)).ToList();
                    if (applyDetails == null || !applyDetails.Any())
                    {
                        return BaseResponseData<string>.Failed(500, $"操作失败，开票申请明细中没有找到{subDetail.ProductNo}货号,{subDetail.Price}价格的明细数据！");
                    }
                    decimal? subDetailQuantity = Math.Abs(subDetail.Quantity);
                    foreach (var applyDetail in applyDetails)
                    {
                        var remainQuantity = Math.Abs(applyDetail.Quantity.Value) - Math.Abs(applyDetail.InvoicedQuantity.HasValue ? applyDetail.InvoicedQuantity.Value : 0M);
                        var invoiceYiDaoItems = vanxDetails.Where(p => p.setadcd == applyDetail.ThirdOrderItemId).ToList();
                        if (invoiceYiDaoItems.Any())
                        {
                            var useTotalQuantity = invoiceYiDaoItems.Sum(p => Math.Abs(decimal.Parse(p.invqty)));
                            if (remainQuantity - useTotalQuantity <= 0)
                            {
                                continue;
                            }
                            else
                            {
                                remainQuantity -= useTotalQuantity;
                            }
                        }
                        if (subDetailQuantity <= 0)
                        {
                            break;
                        }
                        decimal? invoiceQuantity = 0M;
                        if (subDetailQuantity >= remainQuantity)
                        {
                            invoiceQuantity = remainQuantity;
                        }
                        else
                        {
                            invoiceQuantity = subDetailQuantity;
                        }
                        // 拼装参数
                        var vanxDetail = new UploadInvoiceVanxData
                        {
                            invno = invoice.InvoiceNo,
                            invcode = invoice.InvoiceCode,
                            invdate = invoice.InvoiceTime.Value.ToString("yyyy-MM-dd"),
                            invamt = invoice.InvoiceAmount.ToString(),
                            invname = "建发致新",
                            supname = invoice.CompanyName,
                            remarks = invoice.Remark,
                            fileid = "",
                            setacd = applyDetail.ThirdOrderId,
                            setadcd = applyDetail.ThirdOrderItemId,
                            invqty = applyDetail.Quantity < 0 ? "-" + invoiceQuantity.ToString() : invoiceQuantity.ToString(),
                            invtaxprice = subDetail.Price.ToString(),
                        };
                        vanxDetail.invtaxamt = (subDetail.Price * decimal.Parse(vanxDetail.invqty)).ToString();
                        vanxDetails.Add(vanxDetail);
                        subDetailQuantity = subDetailQuantity - invoiceQuantity;
                    }
                    if (subDetailQuantity > 0)
                    {
                        return BaseResponseData<string>.Failed(500, $"操作失败，货号【{subDetail.ProductNo}】,价格【{subDetail.Price}】还剩余{double.Parse(subDetailQuantity.ToString())}没有匹配到数据！");
                    }
                }
            }
            if (vanxDetails.Any())
            {
                var vanxInvoiceNos = vanxDetails.Select(p => p.invno).ToList();
                var exceptInvoiceNos = input.InvoiceNos.Except(vanxInvoiceNos).ToList();
                if (exceptInvoiceNos.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票号【{string.Join(",", exceptInvoiceNos)}】没有匹配到明细数据！");
                }
                // 调用利群接口
                vanxInput.detailline = vanxDetails;
                var jsonStr = JsonConvert.SerializeObject(vanxInput);
                _logger.LogWarning($"【利群】请求集成平台，报文：{jsonStr}");
                var azRet = await _iCApiClient.VanxUploadinvoice(vanxInput);
                if (azRet.Code == CodeStatusEnum.Success)
                {
                    // 利群状态直接更改为已完成 
                    foreach (var invoice in invoices)
                    {
                        invoice.SPDStatus = SPDStatusEnum.Complate;
                        _db.Invoices.Update(invoice);
                    }
                    await _unitOfWork.CommitAsync();
                }
                return azRet;
            }
            return BaseResponseData<string>.Failed(500, "参数为空，推送失败");
        }

        /// <summary>
        /// 推送给安贞
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<string>> PushAz(PushAzInvoicesInput input)
        {

            var invoices = await _db.Invoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var outputInvoices = await _db.OutputInvoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var creditIds = await _db.InvoiceCredits.Where(x => input.InvoiceNos.Contains(x.InvoiceNo)).Select(x => x.CreditId).ToListAsync();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
            // 安贞入参
            var azInput = new UploadInvoiceAZDto
            {
                CompanyId = invoices[0].CompanyId.Value,
                CustomerId = Guid.Parse(invoices[0].CustomerId),
            };

            var codes = new List<string>();
            List<SpdInvoiceApplyDetailOutput> applyDetailsRet = new List<SpdInvoiceApplyDetailOutput>();
            var azDetails = new List<UploadInvoiceAZData>();
            foreach (var credit in credits)
            {
                if (string.IsNullOrEmpty(credit.ShipmentCode))
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}三方单号为空！");
                }
                if (!credit.CustomerId.HasValue)
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}客户id为空！");
                }
                codes.Add(credit.ShipmentCode);
            }
            var spdInput = new SpdInvoiceApplyDetailInput()
            {
                Code = codes.First(),
                CustomerId = credits[0].CustomerId.Value
            };
            var applyDetailsRetTemp = await _iCApiClient.ICSPDInvoiceApplyDetails(spdInput);
            if (applyDetailsRetTemp.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，三方单号{spdInput.Code}获取SPD发票详情失败！");
            }
            if (applyDetailsRetTemp.Data != null && applyDetailsRetTemp.Data.Any())
            {
                applyDetailsRet.AddRange(applyDetailsRetTemp.Data);
            }
            foreach (var invoice in invoices)
            {
                var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).
                                  Where(p => invoice.CustomizeInvoiceCode.Equals(p.Code)).AsNoTracking().FirstOrDefaultAsync();
                var subDetails = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                {
                    Quantity = p.Quantity,
                    Price = p.Price,
                    ProductId = p.ProductId,
                    ProductNo = p.ProductNo,
                    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                    CreditBillCode = p.CreditBillCode
                }).ToListAsync();
                //var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                //{
                //    Quantity = p.Quantity,
                //    Price = p.Price,
                //    ProductId = p.ProductId,
                //    ProductNo = p.ProductNo,
                //    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                //    CreditBillCode = p.CreditCode
                //}).ToListAsync();
                //if (customizeInvoiceCredits.Any())
                //{
                //    subDetails = customizeInvoiceCredits;
                //} 
                if (applyDetailsRet != null && applyDetailsRet.Any())
                {
                    foreach (var subDetail in subDetails)
                    {
                        var applyDetails = applyDetailsRet.Where(p => p.ProductId.Equals(subDetail.ProductId) &&
                                                              Math.Abs(p.Quantity.Value) - Math.Abs(p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0) > 0 &&
                                                               p.Price == subDetail.Price)
                                                   .OrderBy(p => p.Quantity - (p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0)).ToList();
                        if (applyDetails == null || !applyDetails.Any())
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败，开票申请明细中没有找到{subDetail.ProductNo}货号,{subDetail.Price}价格的明细数据！");
                        }
                        decimal? subDetailQuantity = Math.Abs(subDetail.Quantity);
                        foreach (var applyDetail in applyDetails)
                        {
                            var remainQuantity = Math.Abs(applyDetail.Quantity.Value) - Math.Abs(applyDetail.InvoicedQuantity.HasValue ? applyDetail.InvoicedQuantity.Value : 0M);
                            var invoiceYiDaoItems = azDetails.Where(p => p.bill_item_id == applyDetail.ThirdOrderItemId).ToList();
                            if (invoiceYiDaoItems.Any())
                            {
                                var useTotalQuantity = invoiceYiDaoItems.Sum(p => Math.Abs(p.quantity.Value));
                                if (remainQuantity - useTotalQuantity <= 0)
                                {
                                    continue;
                                }
                                else
                                {
                                    remainQuantity -= useTotalQuantity;
                                }
                            }
                            if (subDetailQuantity <= 0)
                            {
                                break;
                            }
                            decimal? invoiceQuantity = 0M;
                            if (subDetailQuantity >= remainQuantity)
                            {
                                invoiceQuantity = remainQuantity;
                            }
                            else
                            {
                                invoiceQuantity = subDetailQuantity;
                            }
                            // 拼装参数
                            var azdetail = new UploadInvoiceAZData();
                            azdetail.bill_id = applyDetail.ThirdOrderId;
                            azdetail.bill_item_id = applyDetail.ThirdOrderItemId;
                            azdetail.product_id = subDetail.ProductId.ToString();
                            azdetail.product_no = subDetail.ProductNo;
                            azdetail.invoice_no = invoice.InvoiceNo;
                            azdetail.Invoice_code = invoice.InvoiceCode;
                            azdetail.Invoice_date = invoice.InvoiceTime.HasValue ? invoice.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
                            azdetail.goods_amount = invoice.InvoiceAmount.HasValue ? invoice.InvoiceAmount.Value : 0;
                            azdetail.quantity = applyDetail.Quantity < 0 ? -invoiceQuantity : invoiceQuantity;
                            azdetail.price = subDetail.Price;
                            azdetail.discount = 1; //默认100%
                            azdetail.saler_id = invoice.CompanyId.HasValue ? invoice.CompanyId.Value.ToString() : string.Empty;
                            azdetail.saler_name = invoice.CompanyName;
                            azdetail.buyer_id = invoice.CustomerId;
                            azdetail.buyer_name = invoice.CustomerName;
                            azDetails.Add(azdetail);
                            subDetailQuantity = subDetailQuantity - invoiceQuantity;
                        }
                        if (subDetailQuantity > 0)
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败，货号【{subDetail.ProductNo}】,价格【{subDetail.Price}】还剩余{double.Parse(subDetailQuantity.ToString())}没有匹配到数据！");
                        }
                    }
                }
            }
            if (azDetails.Any())
            {
                // 调用安贞接口
                azInput.invoiceList = azDetails;

                var azInvoiceNos = azDetails.Select(p => p.invoice_no).ToList();
                var exceptInvoiceNos = input.InvoiceNos.Except(azInvoiceNos).ToList();
                if (exceptInvoiceNos.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票号【{string.Join(",", exceptInvoiceNos)}】没有匹配到明细数据！");
                }
                var jsonStr = JsonConvert.SerializeObject(azInput);
                _logger.LogWarning($"【安贞】请求集成平台，报文：{jsonStr}");
                var azRet = await _iCApiClient.AnthenUploadinvoice(azInput);
                if (azRet.Code == CodeStatusEnum.Success)
                {
                    // 安贞状态直接更改为已完成
                    foreach (var invoice in invoices)
                    {
                        invoice.SPDStatus = SPDStatusEnum.Complate;
                        _db.Invoices.Update(invoice);
                    }
                    await _unitOfWork.CommitAsync();
                }
                return azRet;
            }
            return BaseResponseData<string>.Failed(500, "参数为空，推送失败");
        }

        /// <summary>
        /// 推送给华东
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<string>> PushYiDao(PushAzInvoicesInput input)
        {
            var invoices = await _db.Invoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var outputInvoices = await _db.OutputInvoices.Where(p => input.InvoiceNos.Contains(p.InvoiceNo)).ToListAsync();
            var creditIds = await _db.InvoiceCredits.Where(x => input.InvoiceNos.Contains(x.InvoiceNo)).Select(x => x.CreditId).ToListAsync();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
            // 华东入参
            var ydInput = new UploadInvoiceYiDaoDto
            {
                CompanyId = invoices[0].CompanyId.Value,
                CustomerId = Guid.Parse(invoices[0].CustomerId),
            };
            var yidaoDetails = new List<UploadInvoiceYiDaoItem>();
            var codes = new List<string>();
            List<SpdInvoiceApplyDetailOutput> applyDetailsRet = new List<SpdInvoiceApplyDetailOutput>();
            foreach (var credit in credits)
            {
                if (string.IsNullOrEmpty(credit.ShipmentCode))
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}三方单号为空！");
                }
                if (!credit.CustomerId.HasValue)
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票对应应收{credit.BillCode}客户id为空！");
                }
                codes.Add(credit.ShipmentCode);
            }
            var spdInput = new SpdInvoiceApplyDetailInput()
            {
                Code = codes.First(),
                CustomerId = credits[0].CustomerId.Value
            };
            var applyDetailsRetTemp = await _iCApiClient.ICSPDInvoiceApplyDetails(spdInput);
            if (applyDetailsRetTemp.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，三方单号{spdInput.Code}获取SPD发票详情失败！");
            }
            if (applyDetailsRetTemp.Data != null && applyDetailsRetTemp.Data.Any())
            {
                applyDetailsRet.AddRange(applyDetailsRetTemp.Data);
            }
            foreach (var invoice in invoices)
            {
                var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).
                                Where(p => invoice.CustomizeInvoiceCode.Equals(p.Code)).AsNoTracking().FirstOrDefaultAsync();
                var subDetails = await _db.CustomizeInvoiceDetail.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                {
                    Quantity = p.Quantity,
                    Price = p.Price,
                    ProductId = p.ProductId,
                    ProductNo = p.ProductNo,
                    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                    CreditBillCode = p.CreditBillCode
                }).ToListAsync();
                //var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => customizeInvoiceItem.Id.Equals(p.CustomizeInvoiceItemId)).Select(p => new
                //{
                //    Quantity = p.Quantity,
                //    Price = p.Price,
                //    ProductId = p.ProductId,
                //    ProductNo = p.ProductNo,
                //    CustomizeInvoiceItemId = p.CustomizeInvoiceItemId,
                //    CreditBillCode = p.CreditCode
                //}).ToListAsync();
                //if (customizeInvoiceCredits.Any())
                //{
                //    subDetails = customizeInvoiceCredits;
                //}
                var queryInput = new SPDQueryAmountInput();
                queryInput.codes = codes.Distinct().ToList();
                applyDetailsRet = applyDetailsRet.Distinct().ToList();
                if (applyDetailsRet != null && applyDetailsRet.Any())
                {
                    var spdAmountData = await _iCApiClient.ICGetSPDAmount(queryInput);
                    if (spdAmountData != null && spdAmountData.Code == CodeStatusEnum.Success && spdAmountData.Data.Count > 0)
                    {
                        var spdAmountEntity = spdAmountData.Data.FirstOrDefault();
                        if (spdAmountEntity != null)
                        {
                            var amount = spdAmountEntity.Amount;
                            var customerId = ydInput.CustomerId;
                            spdAmountEntity = spdAmountData.Data.Where(p => p.CustomerId != null && p.CustomerId.Value == customerId).FirstOrDefault();
                            if (spdAmountEntity != null)
                            {
                                amount = spdAmountEntity.Amount;
                            }
                            if (Math.Abs(amount.Value) < Math.Abs(invoices.Sum(p => p.InvoiceAmount).Value))
                            {
                                return BaseResponseData<string>.Failed(500, $"操作失败，第三方单号【{codes.First()}】,发票金额超过开票金额");
                            }
                        }
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, $"操作失败，未获取到三方单号【{codes.First()}】的金额");
                    }
                    foreach (var subDetail in subDetails)
                    {
                        var applyDetails = applyDetailsRet.Where(p => p.ProductId.Equals(subDetail.ProductId) &&
                                                              Math.Abs(p.Quantity.Value) - Math.Abs(p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0) > 0 &&
                                                               p.Price == subDetail.Price)
                                                   .OrderBy(p => p.Quantity - (p.InvoicedQuantity.HasValue ? p.InvoicedQuantity.Value : 0)).ToList();
                        if (applyDetails == null || !applyDetails.Any())
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败，开票申请明细中没有找到{subDetail.ProductNo}货号,{subDetail.Price}价格的明细数据！");
                        }
                        decimal? subDetailQuantity = Math.Abs(subDetail.Quantity);
                        foreach (var applyDetail in applyDetails)
                        {
                            var remainQuantity = Math.Abs(applyDetail.Quantity.Value) - Math.Abs(applyDetail.InvoicedQuantity.HasValue ? applyDetail.InvoicedQuantity.Value : 0M);
                            var invoiceYiDaoItems = yidaoDetails.Where(p => p.saleOrderno == applyDetail.ThirdOrderItemId).ToList();
                            if (invoiceYiDaoItems.Any())
                            {
                                var useTotalQuantity = invoiceYiDaoItems.Sum(p => Math.Abs(p.invNum));
                                if (remainQuantity - useTotalQuantity <= 0)
                                {
                                    continue;
                                }
                                else
                                {
                                    remainQuantity -= useTotalQuantity;
                                }
                            }
                            if (subDetailQuantity <= 0)
                            {
                                break;
                            }
                            decimal? invoiceQuantity = 0M;

                            if (subDetailQuantity >= remainQuantity)
                            {
                                invoiceQuantity = remainQuantity;
                            }
                            else
                            {
                                invoiceQuantity = subDetailQuantity;
                            }
                            // 拼装参数
                            var azdetail = new UploadInvoiceYiDaoItem
                            {
                                deliveryDetailNo = "",
                                deliveryNo = "",
                                invoiceTaxType = "",
                                invoiceStatus = "00",
                                invoiceType = invoice.InvoiceAmount > 0 ? "1" : "2",
                                invNum = applyDetail.Quantity < 0 ? -invoiceQuantity.Value : invoiceQuantity.Value,
                                invoiceAmount = invoice.InvoiceAmount.ToString(),
                                invoiceCode = invoice.InvoiceCode,
                                invoiceNo = invoice.InvoiceNo,
                                invoiceDate = invoice.InvoiceTime.HasValue ? invoice.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty,
                                memo1 = invoice.Remark,
                                saleOrderno = applyDetail.ThirdOrderItemId,
                                goodsCode = applyDetail.ProductNo,
                                ProductId = applyDetail.ProductId,
                                ProductNo = applyDetail.ProductNo
                            };
                            yidaoDetails.Add(azdetail);
                            subDetailQuantity = subDetailQuantity - invoiceQuantity;
                        }
                        if (subDetailQuantity > 0)
                        {
                            return BaseResponseData<string>.Failed(500, $"操作失败，货号【{subDetail.ProductNo}】,价格【{subDetail.Price}】还剩余{double.Parse(subDetailQuantity.ToString())}没有匹配到数据！");
                        }
                    }
                }
            }

            if (yidaoDetails.Any())
            {
                // 调用安贞接口
                ydInput.list = yidaoDetails;
                var yidaoInvoiceNos = yidaoDetails.Select(p => p.invoiceNo).ToList();
                var exceptInvoiceNos = input.InvoiceNos.Except(yidaoInvoiceNos).ToList();
                if (exceptInvoiceNos.Any())
                {
                    return BaseResponseData<string>.Failed(500, $"操作失败，发票号【{string.Join(",", exceptInvoiceNos)}】没有匹配到明细数据！");
                }
                var jsonStr = JsonConvert.SerializeObject(ydInput);
                _logger.LogWarning($"【华东医院】请求集成平台，报文：{jsonStr}");
                var azRet = await _iCApiClient.YiDaoUploadinvoice(ydInput);
                if (azRet.Code == CodeStatusEnum.Success)
                {
                    // 安贞状态直接更改为已完成
                    foreach (var invoice in invoices)
                    {
                        invoice.SPDStatus = SPDStatusEnum.Complate;
                        _db.Invoices.Update(invoice);
                    }
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<string>.Success("操作成功！");
                }
                else
                {
                    return BaseResponseData<string>.Failed(500, "操作成功,失败" + azRet.Message);
                }
            }
            return BaseResponseData<string>.Failed(500, "参数为空，推送失败");
        }

        #region FUNCTIONAL MODULE BY #66403更换发票应收关系  UPDATE BY #100541发票调整应收，需要支持部分销售调回的应收更换
        /// <summary>
        /// 更换发票应收关系
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> ChangeRelationship(ChangeRelationshipInput input)
        {
            if (string.IsNullOrEmpty(input.InvoiceNo))
            {
                return BaseResponseData<string>.Failed(500, $"未获取到发票");
            }
            var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.InvoiceNo == input.InvoiceNo);
            if (invoice == null)
            {
                return BaseResponseData<string>.Failed(500, $"发票不存在或已被删除");
            }
            if (invoice.InvoiceAmount < 0)
            {
                return BaseResponseData<string>.Failed(500, $"负数发票不能调整应收");
            }
            if (input.NewCreditBillCodes == null || !input.NewCreditBillCodes.Any())
            {
                return BaseResponseData<string>.Failed(500, $"未获取到新应收单");
            }
            var existInvoiceCredits = await _db.InvoiceCredits.Include(x => x.Credit).Where(x => x.InvoiceNo == input.InvoiceNo).AsNoTracking().ToListAsync();
            if (existInvoiceCredits == null || !existInvoiceCredits.Any())
            {
                return BaseResponseData<string>.Failed(500, $"发票需至少关联一张应收");
            }
            var relationshipInputOfSale = new ChangeRelationshipInputOfSale();
            var newCredits = await _db.Credits.Where(x => input.NewCreditBillCodes.ToHashSet().Contains(x.BillCode)).ToListAsync();
            if (newCredits == null || !newCredits.Any())
            {
                return BaseResponseData<string>.Failed(500, $"所勾选的新应收单不存在或已被删除");
            }
            if (newCredits.Sum(x => x.Value) != 0)
            {
                return BaseResponseData<string>.Failed(500, $"所勾选的新应收单合计金额不为0");
            }
            var checkedIds = await _creditQueryService1.GetUninvoicedReturnIds(invoice.CompanyId, Guid.Parse(invoice.CustomerId), invoice.InvoiceNo);
            if (checkedIds == null || !checkedIds.Any())
            {
                return BaseResponseData<string>.Failed(500, $"当前发票已无符合调整条件的应收");
            }
            var newCreditIds = newCredits.Select(x => x.Id).ToList();
            var checkedSet = new HashSet<Guid>(checkedIds);
            var missing = newCreditIds.Where(id => !checkedSet.Contains(id)).ToList();
            if (missing.Any())
            {
                var missCreditBillNos = newCredits.Where(x => missing.Contains(x.Id)).Select(x => x.BillCode).ToList();
                return BaseResponseData<string>.Failed(500, $"所勾选的新应收单{string.Join(",", missCreditBillNos)}已被操作");
            }
            //获取新应收是否被冲销
            var abas = await _db.Abatements.Where(x => input.NewCreditBillCodes.ToHashSet().Contains(x.DebtBillCode) || input.NewCreditBillCodes.ToHashSet().Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
            if (abas != null && abas.Any())
            {
                return BaseResponseData<string>.Failed(500, $"勾选的应收单存在冲销记录");
            }
            // 查询被更换的发票是否已入账
            var invoiceReceiptIntransitDetails = await (from ird in _db.InvoiceReceiptDetail join iri in _db.InvoiceReceiptItem on ird.InvoiceReceiptItemId equals iri.Id where (iri.Status == StatusEnum.waitSubmit || iri.Status == StatusEnum.waitAudit) && ird.InvoiceNo == input.InvoiceNo select ird).ToListAsync();
            if (invoiceReceiptIntransitDetails.Count > 0)
            {
                return BaseResponseData<string>.Failed(500, $"发票{input.InvoiceNo}入账在“待提交”“审核中”的发票，不允许换应收");
            }
            var invoiceReceiptSuccessDetails = await (from ird in _db.InvoiceReceiptDetail join iri in _db.InvoiceReceiptItem on ird.InvoiceReceiptItemId equals iri.Id where iri.Status == StatusEnum.Complate && ird.InvoiceNo == input.InvoiceNo select ird).ToListAsync();
            if (invoiceReceiptSuccessDetails.Count > 0)
            {
                // 标记发票入账
                await _invoiceReceiptItemQueryService.MarkDebtDetailsByCreditBillCodes(input.NewCreditBillCodes);
            }

            // 需要新增发票应收的数据
            var invoiceCredits = new List<InvoiceCredit>();
            // 封装金蝶入参
            var kingdeeInput = new ChangeRelationshipKingdeeInput();
            kingdeeInput.invoiceNo = input.InvoiceNo;
            kingdeeInput.user = input.UserName;
            kingdeeInput.oldArBillNo = existInvoiceCredits.Select(x => x.Credit.BillCode).ToList();
            // 封装销售入参
            relationshipInputOfSale.InvoiceNo = input.InvoiceNo;
            relationshipInputOfSale.OldCreditCodes = existInvoiceCredits.Select(x => x.Credit.BillCode).ToList();
            relationshipInputOfSale.UserName = input.UserName;
            var newBillnos = new List<NewCreditInfo>();
            //添加原应收信息
            foreach (var old in existInvoiceCredits)
            {
                if (old.Credit != null)
                {
                    newBillnos.Add(new NewCreditInfo
                    {
                        arBillNum = old.Credit.BillCode,
                        amount = old.CreditAmount.HasValue ? old.CreditAmount.Value : 0,
                    });
                }
            }
            var newBillnoBySales = new List<NewCreditSaleInfo>();
            foreach (var item in newCredits)
            {
                newBillnos.Add(new NewCreditInfo
                {
                    arBillNum = item.BillCode,
                    amount = item.Value,
                });

                newBillnoBySales.Add(new NewCreditSaleInfo
                {
                    NewCreditCode = item.BillCode,
                    NewOrderNo = item.OrderNo,
                    Amount = item.Value
                });

                // 新加应收设置无需开票
                item.IsNoNeedInvoice = IsNoNeedInvoiceEnum.NoNeed;
                item.InvoiceStatus = InvoiceStatusEnum.invoiced;

                // 发票应收关系表
                var invoiceCredit = new InvoiceCredit();
                invoiceCredit.InvoiceNo = invoice.InvoiceNo;
                invoiceCredit.InvoiceCode = invoice.InvoiceCode;
                invoiceCredit.InvoiceCheckCode = invoice.InvoiceCheckCode;
                invoiceCredit.InvoiceTime = invoice.InvoiceTime;
                invoiceCredit.Type = invoice.Type;
                invoiceCredit.InvoiceAmount = invoice.InvoiceAmount;
                invoiceCredit.InvoiceAmountNoTax = invoice.InvoiceAmountNoTax;
                invoiceCredit.TaxAmount = invoice.TaxAmount;
                invoiceCredit.Id = Guid.NewGuid();
                invoiceCredit.CreditId = item.Id;
                invoiceCredit.CreditAmount = item.Value;
                invoiceCredit.CustomizeInvoiceCode = invoice.CustomizeInvoiceCode;
                invoiceCredit.Mark = InvoiceCreditMarkEnum.Adjust; //标记为更换调整应收的应收
                invoiceCredit.CreateBy(input.UserName);
                invoiceCredits.Add(invoiceCredit);
            }
            if (invoiceCredits.Any())
            {
                invoiceCredits = invoiceCredits.Distinct().ToList();
                await _invoiceCreditRepository.AddManyAsync(invoiceCredits);
            }

            kingdeeInput.arBillEntry = newBillnos;
            relationshipInputOfSale.NewCreditInfo = newBillnoBySales;
            //记录日志
            await WriteLog(relationshipInputOfSale);
            //推送金蝶
            var p = new { data = kingdeeInput };
            var jsonStr = JsonConvert.SerializeObject(p);
            await _subLogService.LogAsync("ChangeRelationship", jsonStr, "发票更换应收推送金蝶");
            var kdRet = await _kingdeeApiClient.ChangeInvoiceCreditRelationship(kingdeeInput);
            if (kdRet.Code == CodeStatusEnum.Success)
            {
                await _db.SaveChangesAsync();
                //发送发票应收关系发给销售
                await _daprClient.PublishEventAsync<ChangeRelationshipInputOfSale>(
                 "pubsub-default",
                 "finance-sale-ChangeRelationInvoiceCredit",
                  relationshipInputOfSale);
                return BaseResponseData<string>.Success("更改成功");
            }
            else
            {
                return BaseResponseData<string>.Failed(500, string.Concat("【金蝶】", kdRet.Message));
            }
        }

        /// <summary>
        /// 获取应收是否被冲销（有则返回单据，无则返回空）
        /// </summary>
        /// <param name="billCode"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<CreditPo>?>> GetWriteOffCreditBillCode(string billCode)
        {
            var abatements = await _db.Abatements.Where(x => x.CreditBillCode == billCode || x.DebtBillCode == billCode).ToListAsync();
            if (abatements == null || abatements.Count == 0)
            {
                return BaseResponseData<List<CreditPo>?>.Failed(500, null);
            }
            // 原应收单
            var credit = await _db.Credits.FirstAsync(x => x.BillCode == billCode);
            if (credit.CreditType != CreditTypeEnum.sale)
            {
                return BaseResponseData<List<CreditPo>?>.Failed(500, null);
            }
            var abillCodes = abatements.Select(x => x.CreditBillCode).ToList();
            var bbillCodes = abatements.Select(x => x.DebtBillCode).ToList();
            // 获取原应收对应字段
            if (billCode == abillCodes[0])
            {
                // 查询相同订单
                var bCredits = await _db.Credits.Where(x => !string.IsNullOrEmpty(x.BillCode) && x.OrderNo == credit.OrderNo && bbillCodes.ToHashSet().Contains(x.BillCode)).ToListAsync();
                var bbills = bCredits.Select(x => x.BillCode).ToList();
                var bAbatement = await _db.Abatements.Where(x => bbills.Contains(x.CreditBillCode)).ToListAsync();
                // 且金额相互对冲为0
                if (Math.Abs(bAbatement.Sum(x => x.Value)) == credit.Value)
                {
                    return BaseResponseData<List<CreditPo>?>.Success(bCredits);
                }
            }
            else
            {
                // 查询相同订单
                var aCredits = await _db.Credits.Where(x => !string.IsNullOrEmpty(x.BillCode) && x.OrderNo == credit.OrderNo && abillCodes.ToHashSet().Contains(x.BillCode)).ToListAsync();
                var abills = aCredits.Select(x => x.BillCode).ToList();
                var aAbatement = await _db.Abatements.Where(x => abills.Contains(x.CreditBillCode)).ToListAsync();
                // 且金额相互对冲为0
                if (Math.Abs(aAbatement.Sum(x => x.Value)) == credit.Value)
                {
                    return BaseResponseData<List<CreditPo>?>.Success(aCredits);
                }
            }
            return BaseResponseData<List<CreditPo>?>.Failed(500, null);
        }

        /// <summary>
        /// 根据发票号查询是否存在认款记录且状态为“待提交”（有则返回发票号，无则返回空字符串）
        /// </summary>
        /// <param name="invoiceNos"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> GetRecognizeReceiveDetailByInvoiceNos(List<string> invoiceNos)
        {
            var list = await (from rri in _db.RecognizeReceiveItems
                              join rrd in _db.RecognizeReceiveDetails on rri.Id equals rrd.RecognizeReceiveItemId
                              where rri.Status == RecognizeReceiveItemStatusEnum.WaitSubmit && rrd.Type == 1 && invoiceNos.Contains(rrd.Code)
                              select rrd.Code).ToListAsync();
            string str = list != null ? string.Join(",", list) : string.Empty;
            return BaseResponseData<string>.Success(str);
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        private async Task WriteLog(ChangeRelationshipInputOfSale input)
        {
            var jsonStr = JsonConvert.SerializeObject(input);
            await _subLogService.LogAsync("ChangeRelationship", jsonStr, "发票更换应收通知销售");
        }
        #endregion

        /// <summary>
        /// 设置阳采发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SetSunPurchase(SetSunPurchaseInput input)
        {
            if (input.OrderNos == null || !input.OrderNos.Any())
            {
                return BaseResponseData<int>.Success("无数据，不作设置");
            }
            // 根据订单号查询未标记阳采的发票
            var invoices = await (from c in _db.Credits
                                  join ic in _db.InvoiceCredits on c.Id equals ic.CreditId
                                  join i in _db.Invoices on ic.InvoiceNo equals i.InvoiceNo
                                  where !string.IsNullOrEmpty(c.OrderNo) && input.OrderNos.ToHashSet().Contains(c.OrderNo) //&& !i.SunPurchaseStatus.HasValue
                                  select i).AsNoTracking().Distinct().ToListAsync();

            foreach (var invoice in invoices)
            {
                if (!invoice.SunPurchaseStatus.HasValue || invoice.SunPurchaseStatus == SunPurchaseStatusEnum.waitSubmit)
                {

                    var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).Where(p => p.Code == invoice.CustomizeInvoiceCode).FirstOrDefaultAsync();
                    var customizeInvoiceCredits = await _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemCode == invoice.CustomizeInvoiceCode).ToListAsync();
                    invoice.SunPurchaseStatus = SunPurchaseStatusEnum.waitSubmit;
                    var sunPurchaseInvoiceDetails = await _db.SunPurchaseInvoiceDetails.Where(p => p.InvoiceId == invoice.Id && p.PurchaseCode == input.SunPurchaseRelateCode).AsNoTracking().ToListAsync();
                    _db.SunPurchaseInvoiceDetails.RemoveRange(sunPurchaseInvoiceDetails);
                    var outputInvoice = await _db.OutputInvoices.Where(p => p.InvoiceNo == invoice.InvoiceNo).AsNoTracking().ToListAsync();
                    await AddSunPurchaseDetails(customizeInvoiceItem, invoice, outputInvoice, customizeInvoiceCredits, input.SunPurchaseRelateCode);
                }
            }
            _db.Invoices.UpdateRange(invoices);
            await _unitOfWork.CommitAsync();
            return BaseResponseData<int>.Success("操作成功");
        }

        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoicesByCoordinate(InvoicesQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_invoiceDownLoadExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("发票清单导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }
    }
}
