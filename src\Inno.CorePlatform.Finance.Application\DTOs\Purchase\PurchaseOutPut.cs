﻿using Inno.CorePlatform.Common.Clients.ApiServices.Bds.Outputs;
using Inno.CorePlatform.Common.CompetenceCenter.Enums;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class PurchaseDataInput
    {
        public List<string> Codes { get; set; }
    }
    public class PurchaseDataOutput
    {
        public List<PurchaseData> List { get; set; }
    }
    public class PurchaseData
    {
        public string Code { get; set; }
        public int Status { get; set; }
        public OAInfo? OAInfo { get; set; }
    }

    public class PurchaseOutPut : BusinessDepartDTO
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 原始采购订单号
        /// </summary>
        public string? OriginCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTimeOffset? BillDate { get; set; }
        /// <summary>
        /// 关联项目
        /// </summary>
        public Project? Project { get; set; }
        /// <summary>
        /// 采购明细
        /// </summary>
        public List<PurchaseOrderDetailOutput>? PurchaseOrderDetails { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }
        /// <summary>
        /// 备注  remark = "暂存出库转购货";
        /// </summary>
        public string Remark { get; set; } = "";
        /// <summary>
        /// 销购类型
        /// </summary>
        public int PurSaleType { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; } = "";
        /// <summary>
        /// 关联单号类型
        /// </summary>
        public RelateCodeTypeEnums? RelateCodeType { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public SaleOrder? SaleOrder { get; set; }
        /// <summary>
        /// 是否远期订单
        /// </summary>
        public bool IsLong { get; set; } = false;

        /// <summary>
        /// 采购付款计划
        /// </summary>
        public List<PaymentPlanOutPut> PaymentPlans { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public IdNameClass? Agent { get; set; }

        /// <summary>
        /// 厂家
        /// </summary>
        public IdNameClass? Producer { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public Hospital? Hospital { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public IdNameClass? Service { get; set; }

        /// <summary>
        /// 发货方
        /// </summary>
        public IdNameClass? Shipper { get; set; }

        /// <summary>
        /// 收货方（公司）
        /// </summary>
        public IdNameCodeClass? Consignee { get; set; }
        /// <summary>
        /// 关联采购单
        /// </summary>
        public ForwardOrder? ForwardOrder { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 预付信息
        /// </summary>
        public List<AdvancePayOutput>? AdvancePays { get; set; }

        /// <summary>
        /// 贸易类型
        /// </summary>
        public TradeTypeEnums? TradeType { get; set; }

        /// <summary>
        /// 进口信息
        /// </summary>
        public ExternalTradeInfo? ExternalTradeInfo { get; set; }

        /// <summary>
        /// 是否直放
        /// </summary>
        public bool? IsDirect { get; set; }

        /// <summary>
        /// 采购合同
        /// </summary>
        public ContractOutPut? Contract { get; set; }

        /// <summary>
        /// 返利金额调整总额
        /// </summary> 
        public decimal? TotalRebateAmountModify { get; set; }

        /// <summary>
        /// 返利类型
        /// </summary>
        public RebateTypeEnums? RebateType { get; set; }
        /// <summary>
        /// 参加的返利活动id
        /// </summary>
        public Guid? RebateActivityId { get; set; }
        /// <summary>
        /// 返利活动名称
        /// </summary>
        public string? RebateActivityName { get; set; }

        /// <summary>
        /// 服务采购修订
        /// </summary> 
        public int? ServicePurchaseRevise { get; set; } 
    }
    public class Hospital : IdNameClass
    {
        /// <summary>
        /// 医院部门Id
        /// </summary>
        public string? hospitalDeptId { get; set; }
        /// <summary>
        /// 客户部门名称
        /// </summary>
        public string? hospitalDeptName { get; set; }
        /// <summary>
        /// 部门负责人Id
        /// </summary>
        public string HospitalPersonId { get; set; }
        /// <summary>
        /// 部门负责人名称
        /// </summary>
        public string HospitalPersonName { get; set; }
        /// <summary>
        /// 联系人（实际收货地址）
        /// </summary>
        public ContactAddress? ContactAddress { get; set; }
        /// <summary>
        /// 备案地址
        /// </summary>
        public ContactAddress? RecordAddresses { get; set; }

    }
    public class ContractOutPut
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 合同编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 是否使用模板
        /// </summary>
        public bool IsUserTemplate { get; set; }

        public string BuyerName { get; set; }

        public string SellerName { get; set; }
        /// <summary>
        /// 签约日期
        /// </summary>
        public DateTime SignDate { get; set; }
        /// <summary>
        /// 签约地点
        /// </summary>
        public string? SignAddress { get; set; }
        ///// <summary>
        ///// 合同附件
        ///// </summary>
        //public List<AttachFiles>? ContractAttachFile { get; set; } = new List<AttachFiles>();
        ///// <summary>
        ///// 合同信息
        ///// </summary>
        //public ContractInfo? ContractInfo { get; set; }

        /// <summary>
        /// OA 返回的合同流程RequestId
        /// </summary>
        public string? RequestId { get; set; }
        /// <summary>
        /// 采购合同附件id
        /// </summary>
        public string? AttachmentFileIds { get; set; }
        /// <summary>
        /// 采购合同Ac附件id
        /// </summary>
        public string? AcAttachmentFileIds { get; set; }
        /// <summary>
        /// 已回签附件集合
        /// </summary>
        public string? SignedBackAttachmentFileIds { get; set; }
    }
    /// <summary>
    /// 进口信息
    /// </summary>
    public class ExternalTradeInfo : ValueObject
    {
        /// <summary>
        /// 贸易国代码
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// 贸易国名称
        /// </summary>
        public string CountryName { get; set; }

        /// <summary>
        /// 币种代码(数据字典代码)
        /// </summary>
        public string CoinCode { get; set; }
        /// <summary>
        /// 币种代码
        /// </summary>
        public string CoinAttribute { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string CoinName { get; set; }


        /// <summary>
        /// 贸易术语代码
        /// </summary>
        public string TradeTermCode { get; set; }

        /// <summary>
        /// 贸易术语名称集合
        /// </summary>
        public string TradeTermName { get; set; }

        /// <summary>
        /// 人民币汇率
        /// 支持小数点4
        /// </summary>
        public decimal RmbRate { get; set; }

        /// <summary>
        /// 装货港口代码
        /// </summary>
        public string LoadingPortCode { get; set; }

        /// <summary>
        /// 装货港口名称
        /// </summary>
        public string LoadingPortName { get; set; }

        /// <summary>
        /// 装货港口描述
        /// </summary>
        public string? LoadingPortDesc { get; set; }

        /// <summary>
        /// 卸货港口代码
        /// </summary>
        public string DischargingPortCode { get; set; }

        /// <summary>
        /// 卸货港口名称
        /// </summary>
        public string DischargingPortName { get; set; }

        /// <summary>
        /// 卸货港口描述
        /// </summary>
        public string? DischargingPortDesc { get; set; }

        /// <summary>
        /// 报关口岸代码
        /// </summary>
        public string DeclarePortCode { get; set; }

        /// <summary>
        /// 报关口岸名称
        /// </summary>
        public string DeclarePortName { get; set; }

        /// <summary>
        /// 报关行
        /// </summary>
        public Agent CustomBroker { get; set; }
    }
    /// <summary>
    /// 贸易类型
    /// </summary>
    public enum TradeTypeEnums
    {
        /// <summary>
        /// 国内
        /// </summary>
        [Description("国内")]
        Internal = 1,

        /// <summary>
        /// 进口
        /// </summary>
        [Description("进口")]
        External = 2
    }
    public class IdNameClass
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
    }

    public class IdNameCodeClass : IdNameClass
    {
        public string? NameCode { get; set; }
    }
    /// <summary>
    /// 远期订单
    /// </summary>
    public class ForwardOrder : ValueObject
    {
        public Guid? Id { get; set; }
        public string? Code { get; set; }
    }
    public class PurchaseOrderDetailOutput
    {
        /// <summary>
        /// 主键
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 产品信息
        /// </summary>
        public Product? Product { get; set; }

        public decimal Cost { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 已采购数量
        /// </summary>
        public int AlreadyStoreIn { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardCost { get; set; }
        /// <summary>
        /// .基准成本
        /// </summary>
        public decimal? BaselineCost { get; set; }
        /// <summary>
        /// 活动优惠
        /// </summary>
        public decimal? ActivityCost { get; set; }
        /// <summary>
        /// 最终成本
        /// </summary>
        public decimal? FinalCost { get; set; }
        /// <summary>
        /// 采购金额
        /// </summary>
        public decimal Amount
        {
            get
            {
                return Cost * Quantity;
            }
        }
        public int CanStoreInQuantity
        {
            get
            {
                return Quantity - AlreadyStoreIn;
            }
        }
        /// <summary>
        /// 修订明细
        /// </summary>
        public List<ReviseProductDetail>? ReviseProductDetails { get; set; }
        public string? jfzx_revisiontype { get; set; }
        public Guid? RelateId { get; set; }

        public PurchaseOrderQueryOutput PurchaseOrder { get; set; }
        #region 进口信息
        /// <summary>
        /// 原币单价
        /// </summary>
        public decimal? OriginCost { get; set; }
        /// <summary>
        /// 原币基准成本
        /// </summary>
        public decimal? OriginBaseLineCost { get; set; }

        /// <summary>
        /// 原币优惠金额
        /// </summary>
        public decimal? OriginDiscountAmount { get; set; }

        /// <summary>
        /// 原币返利金额
        /// </summary>
        public decimal? OriginRebateAmount { get; set; }


        /// <summary>
        /// 关税税率
        /// </summary>
        public decimal? TariffRate { get; set; }

        /// <summary>
        /// 关税单价
        /// </summary>
        public decimal? TariffUnitPrice { get; set; }

        /// <summary>
        /// 关税金额
        /// </summary>
        public decimal? TariffAmount { get; set; }

        /// <summary>
        /// 进口增值税率
        /// </summary>
        public decimal? ImportAddRate { get; set; }

        /// <summary>
        /// 进口增值税额
        /// </summary>
        public decimal? ImportAddAmount { get; set; }

        /// <summary>
        /// 原厂国
        /// </summary>
        public string? OriginCountry { get; set; }

        /// <summary>
        /// 币种代码
        /// （冗余币种信息，便于后续功能实现）
        /// </summary>
        public string? CoinCode { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string? CoinName { get; set; }
        /// <summary>
        /// 数据字典属性
        /// </summary>
        public string? CoinAttribute { get; set; }
        /// <summary>
        /// 海关编码
        /// </summary>
        public string? CustomsCode { get; set; }
        /// <summary>
        /// 海关编码名称
        /// </summary>
        public string? CustomsCodeName { get; set; }
        #endregion
        /// <summary>
        /// 是否拆分数据
        /// </summary>
        public bool? IsSplit { get; set; }
        #region 折扣信息
        /// <summary>
        /// 基础折扣
        /// </summary>
        public decimal? DistributionDiscount { get; set; }
        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        public decimal? FinanceDiscount { get; set; }
        /// <summary>
        /// SPD折扣
        /// </summary>
        public decimal? SpdDiscount { get; set; }
        /// <summary>
        /// 税率折扣
        /// </summary>
        public decimal? TaxDiscount { get; set; }
        /// <summary>
        /// 厂家折扣
        /// </summary>
        public decimal? CostDiscount { get; set; }
        #endregion
        /// <summary>
        /// b类采购明细
        /// </summary>
        public BDetail? BDetail { get; set; }
        /// <summary>
        /// 追踪码
        /// </summary>
        public string? TraceCode { get; set; }
        public int? Mark { get; set; }
    }
    public class BDetail
    {
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 价格
        /// </summary>
        public decimal? Price { get; set; }

    }
    public class ReviseDetailToKD
    {
        public Guid? ProductId { get; set; }
        public Guid? ProductNameId { get; set; }
        public decimal Cost { get; set; }
        public decimal TaxRate { get; set; }
        public string jfzx_revisiontype { get; set; }
        public string jfzx_rebatecustomerid { get; set; }
        public Guid? RelateId { get; set; }
        public decimal? OriginCost { get; set; }
        public int Quantity { get; internal set; }
    }

    public class PurchaseOrderQueryOutput
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }
    }
    /// <summary>
    /// 修订货品明细类型
    /// </summary>
    public class ReviseProductDetail : ValueObject
    {
        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string? LotNo { get; set; }
        /// <summary>
        /// 条码
        /// </summary>
        public string? BarCode { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public LocationTypeEnum Type { get; set; }
        /// <summary>
        /// 修订货品具体的唯一键Id
        /// </summary>
        public string ObjectId { get; set; }
        /// <summary>
        /// 修订货品具体位置
        /// </summary>
        public string ObjectLocation { get; set; }
        /// <summary>
        /// 购货修订明细类型
        /// </summary>

        public string RevisedTypeName
        {
            get
            {
                return Type.GetDescription();
            }
        }

        /// <summary>
        /// 客户Id（因为财务要客户id做凭证，才加的这个字段）
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户名称（因为财务要客户id做凭证，才加的这个字段）
        /// </summary>
        public string? CustomerName { get; set; }
    }

    /// <summary>
    /// 修订货品明细类型
    /// </summary>
    public enum LocationTypeEnum
    {
        /// <summary>
        /// 库存
        /// </summary>
        [Description("库存")]
        Inventory = 1,
        /// <summary>
        /// 暂存
        /// </summary>
        [Description("暂存")]
        Temp = 2,
        /// <summary>
        /// 第三方库存
        /// </summary>
        [Description("第三方库存")]
        ThirdStore = 3,
        /// <summary>
        /// 暂存核销
        /// </summary>
        [Description("暂存核销")]
        TempSale = 4,
        /// <summary>
        /// 销售出库
        /// </summary>
        [Description("销售出库")]
        Sale = 5,
        /// <summary>
        /// 跟台
        /// </summary>
        [Description("跟台")]
        Operation = 6,
        /// <summary>
        /// 换货
        /// </summary>
        [Description("换货")]
        Change = 7,
        /// <summary>
        /// 未找货
        /// </summary>
        [Description("未找货")]
        UnFind = 8,

        /// <summary>
        /// 租借出库
        /// </summary>
        [Description("租借出库")]
        Rentaloutbound = 10,
    }
    public class Project
    {
        public string Id { get; set; }
        public string Name { get; set; }
        /// <summary>
        /// 项目编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 项目子类型，
        /// </summary>
        public string? SubType { get; set; }
    }

    public class SaleOrder
    {
        public string? code { get; set; }
    }

    public class Product
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public Guid? NameId { get; set; }
        public bool? HaveFirstApprovelRecord { get; set; }
        public string? ProductNo { get; set; }
        public string? Spec { get; set; }
    }

    /// <summary>
    /// 采购订单详细
    /// </summary>
    public class AdvancePayOutput
    {
        /// <summary>
        /// 结算方式编码  
        /// JSFS02 现金支票
        /// JSFS03 转账支票
        /// JSFS04 电汇
        /// JSFS05 信汇
        /// JSFS06 商业承兑汇票
        /// JSFS07 银行承兑汇票
        /// JSFS08 信用证
        /// JSFS09 应收票据背书
        /// JSFS10 内部利息结算
        /// JSFS11 集中结算
        /// JSFS12 票据退票
        /// JSFS13 银企支付
        /// JSFS-YXFY-01	营销费用账户 
        /// </summary>
        public string? SettlementModel { get; set; }
        /// <summary>
        /// 附言
        /// </summary>
        public string? TransferDiscourse { get; set; } = "";

        /// <summary>
        /// 费用承担方
        /// </summary>
        public string? ChargesBorneBy { get; set; }
        /// <summary>
        /// 海关报关商品
        /// </summary>
        public string? CustomsDeclarationGoods { get; set; }
        /// <summary>
        /// 本笔款项是否为保税货物项下付款
        /// </summary>
        public bool? IsBonded { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        public string? ContractNumber { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNumber { get; set; }
        /// <summary>
        /// 财务备注
        /// </summary>
        public string? FinanceRemark { get; set; } = "";
        public Guid Id { get; set; }
        public Guid PurchaseOrderId { get; set; }
        /// <summary>
        /// 单据编号（时间戳）
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 本订单预付金额
        /// </summary>

        public decimal PlanPayAmount { get; set; }
        /// <summary>
        /// 已预付金额（取财务接口）查询条件 项目+公司+供应商
        /// </summary>

        public decimal FinancePaidAmount { get; set; }
        /// <summary>
        /// 可预付金额
        /// </summary>

        public decimal CanPayAmount { get; set; }
        /// <summary>
        /// 额度总额（项目接口）
        /// </summary>

        public decimal AllQuota { get; set; }
        /// <summary>
        /// 剩余额度（财务接口）
        /// </summary>

        public decimal LeftQuota { get; set; }
        /// <summary>
        /// 本次预付金额 
        /// </summary>

        public decimal ActualPayAmount { get; set; }
        /// <summary>
        /// 预付RequestId
        /// </summary>
        public string? RequestId { get; set; }
        /// <summary>
        /// 预付形式
        /// </summary>
        public AdvancePayModeEnum AdvancePayMode { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        public List<PaymentCode>? PaymentCodes { get; set; }
        /// <summary>
        /// 供应商账号
        /// </summary>
        public BankInfo? AgentBank { get; set; }

        /// <summary>
        /// 信用证信息
        /// </summary>
        public CreditInfo? CreditInfo { get; set; }
    }
    public class CreditInfo : ValueObject
    {
        /// <summary>
        /// 信用证号
        /// </summary>
        public string? CreditNumber { get; set; }
        /// <summary>
        /// 到单单号
        /// </summary>
        public string? ArrivalNumber { get; set; }
    }
    /// <summary>
    /// 付款单
    /// </summary>
    public class PaymentCode
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string? TypeName { get; set; }
    }
    /// <summary>
    /// 供应商银行信息
    /// </summary>
    public class BankInfo
    {
        public string? account { get; set; }
        public string? bank { get; set; }
        public string? bankNo { get; set; }
        public string? bankCode { get; set; }
        /// <summary>
        /// 类型(1-境内银行 2-境外银行)
        /// </summary>
        public int? type { get; set; }
        /// <summary>
        ///  类型名称
        /// </summary>
        public string? typeName { get; set; }
    }

    /// <summary>
    /// 返利类型
    /// </summary>
    public enum RebateTypeEnums
    {
        /// <summary>
        /// 任务达标
        /// </summary>
        [Description("任务达标返利")]
        TaskQualified = 1,
        /// <summary>
        /// 短期活动
        /// </summary>
        [Description("短期活动返利")]
        ShortActivity = 2,
        /// <summary>
        /// 单笔订单
        /// </summary>
        [Description("单笔订单返利")]
        SingleOrder = 3,
        /// <summary>
        /// 平移返利
        /// </summary>
        [Description("平移返利")]
        Translate = 4,
        /// <summary>
        /// 补偿返利
        /// </summary>
        [Description("补偿返利")]
        Compensate = 5,

    }

    /// <summary>
    /// 采购订单明细出参
    /// </summary>
    public class PurchaseOrderOutput
    {
        /// <summary>
        /// 列表
        /// </summary>
        public List<PurchaseOrderDetail?>? list { get; set; }

        /// <summary>
        /// 总数
        /// </summary>
        public int? total { get; set; }

        public object? data { get; set; }

        public object? footer { get; set; }
    }

    /// <summary>
    /// 采购订单明细详情
    /// </summary>
    public class PurchaseOrderDetail
    {
        public string? Id { get; set; }
        public ProductInfo? Product { get; set; }
        public string? Specification { get; set; }
        public string? PackDes { get; set; }
        public decimal? Cost { get; set; }
        public int? Quantity { get; set; }
        public decimal? RebateAmount { get; set; }
        public decimal? TaxRate { get; set; }
        public int? AlreadyStoreIn { get; set; }
        public decimal? StandardCost { get; set; }
        public decimal? BaselineCost { get; set; }
        public decimal? ActivityCost { get; set; }
        public decimal? FinalCost { get; set; }
        public string? ReviseProductDetails { get; set; }
        public string? RemarkRecords { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal? ExcludingTaxCost { get; set; }
        public decimal? Amount { get; set; }
        public int? CanStoreInQuantity { get; set; }
        public string? RelateId { get; set; }
        public int? PurchaseUnit { get; set; }
        public string? RebateActivityId { get; set; }
        public decimal? ProjectBaselineCost { get; set; }
        public PurchaseOrderItem? PurchaseOrder { get; set; }
        public bool? IsDirect { get; set; }
        public decimal? OriginCost { get; set; }
        public decimal? OriginBaseLineCost { get; set; }
        public decimal? OriginDiscountAmount { get; set; }
        public decimal? OriginRebateAmount { get; set; }
        public string? TariffMethod { get; set; }
        public decimal? TariffRate { get; set; }
        public decimal? TariffUnitPrice { get; set; }
        public decimal? TariffAmount { get; set; }
        public decimal? ImportAddRate { get; set; }
        public decimal? ImportAddAmount { get; set; }
        public string? OriginCountry { get; set; }
        public string? CoinCode { get; set; }
        public string? CoinName { get; set; }
        public string? CoinAttribute { get; set; }
        public string? CustomsCode { get; set; }
        public string? CustomsCodeName { get; set; }
        public bool? IsSplit { get; set; }
        public BDetail? BDetail { get; set; }
        public string? ContractCode { get; set; }
        public string? ProducerArea { get; set; }
        public string? RelateDetailsId { get; set; }
        public string? ReturnSaleCode { get; set; }
        public int? ReturnSaleQuantity { get; set; }
        public string? Remark { get; set; }
        public string? FreezeCustomerId { get; set; }
        public string? FreezeCustomerName { get; set; }
    }

    public class ProductInfo
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? NameId { get; set; }
        public bool? HaveFirstApprovelRecord { get; set; }
        public string? ProductNo { get; set; }
        public string? Spec { get; set; }
        public string? Model { get; set; }
        public string? ProductDes { get; set; }
        public string? RegistrationNo { get; set; }
        public string? RegistrationID { get; set; }
        public string? RegisterName { get; set; }
        public int? ManageLevel { get; set; }
        public string? PackUnit { get; set; }
        public string? PackUnitDesc { get; set; }
        public string? PackDes { get; set; }
        public Producer? Producer { get; set; }
        public string? ProductTypeName { get; set; }
        public string? MarkProducerName { get; set; }
        public string? TransportInfo { get; set; }
    }

    public class PurchaseOrderItem
    {
        public string? Code { get; set; }
        public DateTimeOffset? BillDate { get; set; }
        public long? BillDateFormate { get; set; }
        public long? UpdatedTimeFormate { get; set; }
        public long? CreatedTimeFormate { get; set; }
        public int? PurSaleType { get; set; }
        public Consignee? Consignee { get; set; }
        public Project? Project { get; set; }
        public Agent? Agent { get; set; }
        public Hospital? Hospital { get; set; }
        public string? BillFlowDirection { get; set; }
        public Shipper? Shipper { get; set; }
        public Service? Service { get; set; }
        public Producer? Producer { get; set; }
        public int? Type { get; set; }
        public string? PruchaseOrderTypeName { get; set; }
        public string? Contract { get; set; }
        public string? BusinessDeptId { get; set; }
        public string? BusinessDeptFullName { get; set; }
        public string? BusinessDeptFullPath { get; set; }
        public string? BusinessDeptShortName { get; set; }
        public string? DisplayPurSaleType { get; set; }
        public OaInfo? OaInfo { get; set; }
        public string? Rebate { get; set; }
        public string? RebateActivityId { get; set; }
        public string? RebateActivityName { get; set; }
        public string? PaymentPlans { get; set; }
        public string? AdvancePays { get; set; }
        public string? Remark { get; set; }
        public string? AuditRemark { get; set; }
        public bool? IsLong { get; set; }
        public string? ForwardOrder { get; set; }
        public string? ProducerOrderNo { get; set; }
        public List<StoreHouse>? StoreHouses { get; set; }
        public int? TradeType { get; set; }
        public string? ExternalTradeInfo { get; set; }
        public bool? IsDirect { get; set; }
        public string? TradeTypeName { get; set; }
        public string? RelateCode { get; set; }
        public string? RelateCodeType { get; set; }
        public int? FinishTag { get; set; }
        public int? PurchaseContractType { get; set; }
        public string? PurchaseContractTypeName { get; set; }
        public int? DeliveryStatus { get; set; }
        public string? DeliveryStatusName { get; set; }
        public string? ForceEndOAInfo { get; set; }
        public string? ProducerArea { get; set; }
        public TerminalCustomer? TerminalCustomer { get; set; }
        public string? IsHasDispute { get; set; }
        public string? SupplementaryContractStatus { get; set; }
        public string? SupplementaryContractStatusName { get; set; }
        public string? SupplementaryContractOAInfo { get; set; }
        public string? StatusName { get; set; }
        public string? Id { get; set; }
        public DateTimeOffset? CreatedTime { get; set; }
        public DateTimeOffset? UpdatedTime { get; set; }
        public DateTimeOffset? DeletedTime { get; set; }
        public string? CreatedBy { get; set; }
        public string? DisplayCreateBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? DisplayUpdatedBy { get; set; }
        public string? DeletedBy { get; set; }
        public bool? IsDeleted { get; set; }
        public int? Status { get; set; }
    }

    public class Shipper
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
    }

    public class OaInfo
    {
        public string? RequestId { get; set; }
        public string? Auditor { get; set; }
        public DateTimeOffset? AuditTime { get; set; }
        public string? Remark { get; set; }
        public string? CurrentOAStepName { get; set; }
    }

    public class StoreHouse
    {
        public string? Address { get; set; }
        public string? StoreHouseType { get; set; }
        public string? DockingSystem { get; set; }
        public string? Id { get; set; }
        public string? Name { get; set; }
    }
}
