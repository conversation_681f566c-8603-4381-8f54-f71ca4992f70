namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    
    /// <summary>
    /// 调整业务部门输出数据模型
    /// 用于承载和传递调整业务部门操作后的结果数据
    /// </summary>
    public class AdjustBusinessDeptOutput
    {
        
    }
    
    /// <summary>
    /// 校验在途单据输出数据模型
    /// 用于承载和传递校验在途单据操作后的结果数据
    /// </summary>
    public class InTransitOrderOutput
    {
        /// <summary>
        /// 在途单据号
        /// </summary>
        public string OrderNo { get; set; }

        /// <summary>
        /// 状态(0:草稿 1:已提交 2:已审核 3:已作废)
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 类型（1：批量付款单 2：认款单）
        /// </summary>
        public int Type { get; set; }
        
        /// <summary>
        /// 项目ID
        /// </summary>
        public Guid ProjectId { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }
    }
}