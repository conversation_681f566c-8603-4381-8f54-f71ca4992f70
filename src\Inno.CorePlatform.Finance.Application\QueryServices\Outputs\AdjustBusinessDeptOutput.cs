namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    
    /// <summary>
    /// 调整业务部门输出数据模型
    /// 用于承载和传递调整业务部门操作后的结果数据
    /// </summary>
    public class AdjustBusinessDeptOutput
    {
        
    }
    
    /// <summary>
    /// 校验在途单据输出数据模型
    /// 用于承载和传递校验在途单据操作后的结果数据
    /// </summary>
    public class InTransitOrderOutput
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string StatusName { get; set; }
        
        /// <summary>
        /// 类型（1：批量付款单 2：认款单）
        /// </summary>
        public int Type { get; set; }
        
        /// <summary>
        /// 项目ID
        /// </summary>
        public Guid ProjectId { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }
    }
}