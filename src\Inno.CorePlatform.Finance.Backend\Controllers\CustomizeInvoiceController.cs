﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Enums;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 运营制作开票管理
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CustomizeInvoiceController : BaseController
    {
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly ICustomizeInvoiceAppService _customizeInvoiceAppService;
        private readonly ILogger<CustomizeInvoiceController> _logger;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IStoreOutApplyApiClient _storeOutApplyApiClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly ICreditQueryService _creditQueryService;
        /// <summary>
        /// 运营制作开票管理
        /// </summary>
        public CustomizeInvoiceController(
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            ICustomizeInvoiceAppService customizeInvoiceAppService,
            IFileGatewayClient fileGatewayClient,
            IPCApiClient pCApiClient,
            IEasyCachingProvider easyCaching,
            IStoreOutApplyApiClient storeOutApplyApiClient,
            IBDSApiClient bDSApiClient, 
            ICreditQueryService creditQueryService,
            ILogger<CustomizeInvoiceController> logger, ISubLogService subLog) : base(subLog)
        {
            this._fileGatewayClient = fileGatewayClient;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _customizeInvoiceAppService = customizeInvoiceAppService;
            _logger = logger;
            _pCApiClient = pCApiClient;
            _storeOutApplyApiClient = storeOutApplyApiClient;
            _easyCaching = easyCaching;
            _bDSApiClient = bDSApiClient;
            _creditQueryService= creditQueryService;
        }

        /// <summary>
        /// 获取原始开票明细查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetOriginDetail")]
        public async Task<ResponseData<OriginDetailOutput>> GetOriginDetail([FromBody] OriginDetailQueryInput input)
        { 
            try
            {
                var (list, count) = await _customizeInvoiceQueryService.GetOriginDetailAsync(input);
                var result = new ResponseData<OriginDetailOutput>
                {
                    Code = 200,
                    Data = new Data<OriginDetailOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
                return result;
            }
            catch (Exception ex)
            {
                return new ResponseData<OriginDetailOutput>
                {
                    Code = 500,
                    Msg = ex.Message,
                };
            }
        }

        /// <summary>
        /// 手动获取原始开票明细查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetOriginDetailOfHand")]
        public async Task<ResponseData<OriginDetailOutput>> GetOriginDetailOfHand([FromBody] OriginDetailQueryInputHand input)
        {
            try
            {
                var (list, count) = await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                {
                    CreditBillCodes = input.CreditBillCodes,
                    RelateCodes = input.RelateCodes,
                });
                return new ResponseData<OriginDetailOutput>
                {
                    Code = 200,
                    Data = new Data<OriginDetailOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                return new ResponseData<OriginDetailOutput>
                {
                    Code = 500,
                    Msg = ex.Message,
                };
            }
        }
        /// <summary>
        /// 新增折扣行
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("adddiscount")]
        public async Task<BaseResponseData<string>> Adddiscount(CustomizeInvoiceDetailBatchInput input)
        {
            foreach (var item in input.CustomizeInvoiceDetailIds)
            {
                var ret = await _customizeInvoiceAppService.Adddiscount(new CustomizeInvoiceDetailInput { CustomizeInvoiceDetailId = item });
                if (ret.Code != CodeStatusEnum.Success)
                {
                    return ret;
                }
            }
            return BaseResponseData<string>.Success("操作成功！");
        }

        /// <summary>
        /// 保存开票明细（合并后）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveCustomizeInvoice")]
        public async Task<ResponseData<int>> SaveCustomizeInvoice([FromBody] SaveCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                input.Source = 0;
                var (count, msg) = await _customizeInvoiceAppService.SaveCustomizeInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count
                        },
                        Msg = msg
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 校验旺店通合并开票逻辑
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CheckWDTMerge")]
        public async Task<BaseResponseData<string>> CheckWDTMerge([FromBody] CheckWDTMergeInput input)
        {
            return await _customizeInvoiceAppService.CheckWDTMerge(input);
        }

        /// <summary>
        /// 获取开票单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetCustomizeInvoiceItem")]
        public async Task<ResponseData<CustomizeInvoiceItemOutput>> GetCustomizeInvoiceItem([FromBody] CustomizeInvoiceItemQueryInput input)
        {
            try
            {
                input.UserName = CurrentUser.UserName;
                var (list, count) = await _customizeInvoiceQueryService.GetCustomizeInvoiceItem(input);
                return new ResponseData<CustomizeInvoiceItemOutput>
                {
                    Code = 200,
                    Data = new Data<CustomizeInvoiceItemOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 按状态获取分页总数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<CustomizeInvoiceItemTabOutput>> GetTabCountAsync([FromBody] CustomizeInvoiceItemQueryInput input)
        {
            input.UserName = CurrentUser.UserName;
            var model = await _customizeInvoiceQueryService.GetTabCountAsync(input);
            return new BaseResponseData<CustomizeInvoiceItemTabOutput>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }

        /// <summary>
        /// 获取客户邮箱
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetCustomerEmail")]
        public async Task<BaseResponseData<string>> GetCustomerEmail([FromBody] CustomizeInvoiceItemQueryInput input)
        {
            input.UserName = CurrentUser.UserName;
            var model = await _customizeInvoiceQueryService.GetCustomerEmail(input);
            return new BaseResponseData<string>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }

        /// <summary>
        /// 获取开票明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetCustomizeInvoiceDetail")]
        public async Task<ResponseData<CustomizeInvoiceDetailOutput>> GetCustomizeInvoiceDetail([FromBody] CustomizeInvoiceDetailQueryInput input)
        {
            try
            {
                var (list, count) = await _customizeInvoiceQueryService.GetCustomizeInvoiceDetail(input);
                return new ResponseData<CustomizeInvoiceDetailOutput>
                {
                    Code = 200,
                    Data = new Data<CustomizeInvoiceDetailOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 删除开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteCustomizeInvoice")]
        public async Task<ResponseData<int>> DeleteCustomizeInvoice([FromBody] DeleteCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.DeleteCustomizeInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 编辑开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("EditCustomizeInvoice")]
        public async Task<ResponseData<int>> EditCustomizeInvoice([FromBody] EditCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.EditCustomizeInvoice(input);
                if (count >= 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 批量编辑备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("EditBatchCustomizeInvoice")]
        public async Task<BaseResponseData<string>> EditBatchCustomizeInvoice([FromBody] EditBatchCustomizeInvoiceInput input)
        {
            var ret = await _customizeInvoiceAppService.EditBatchCustomizeInvoice(input);
            return ret;
        }
        /// <summary>
        /// 提交开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitCustomizeInvoice")]
        public async Task<ResponseData<int>> SubmitCustomizeInvoice([FromBody] SubmitCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.SubmitCustomizeInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 提交开票单-OA
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitCustomizeInvoiceToOA")]
        public async Task<BaseResponseData<string>> SubmitCustomizeInvoiceToOA([FromBody] SubmitCustomizeInvoiceInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _customizeInvoiceAppService.SubmitCustomizeInvoiceToOA(input);
            return ret;
        }

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AttachFileIds")]
        public async Task<BaseResponseData<int>> AttachFileIds(CustomizeInvoiceAttachFileInput input)
        {
            return await _customizeInvoiceAppService.AttachFileIds(input);
        }
        /// <summary>
        /// 上传结算清单附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AttachFileIds_jsqd")]
        public async Task<BaseResponseData<int>> AttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input)
        {
            return await _customizeInvoiceAppService.AttachFileIds_jsqd(input);
        }

        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            var customizeInvoiceItem = await _customizeInvoiceQueryService.GetById(input.CustomizeInvoiceItemId);
            if (!string.IsNullOrEmpty(customizeInvoiceItem.AttachFileIds))
            {
                var fileIds = customizeInvoiceItem.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var bizFiles = new List<BizFileUploadOutput>();
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
#if DEBUG
                bizFiles.Add(new BizFileUploadOutput { Id = Guid.Parse(fileIds[0]), Length = 1000, Name = "测试" });
#endif
                ret.Data = bizFiles;

            }
            return ret;
        }
        /// <summary>
        /// 查看结算清单附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile_jsqd")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile_jsqd(CustomizeInvoiceAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            var (list, count) = await _customizeInvoiceQueryService.GetCustomizeInvoiceClassify(new CustomizeInvoiceClassifyQueryInput()
            {
                Id = input.CustomizeInvoiceItemId,
                UserId = CurrentUser.Id,
                UserName = CurrentUser.UserName,
            });
            var customizeInvoiceClassify = list.FirstOrDefault();
            if (customizeInvoiceClassify != null && !string.IsNullOrEmpty(customizeInvoiceClassify.AttachFileIds))
            {
                var fileIds = customizeInvoiceClassify.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var bizFiles = new List<BizFileUploadOutput>();
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                var user = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput
                {
                    Names = bizFiles.Select(p => p.UploadedBy).Distinct().ToList()
                });
                if (user.Code == 200)
                {
                    foreach (var file in bizFiles) //赋值显示的名字
                    {
                        if (user.Data.List.Count() > 0 && user.Data.List.Select(p => p.Name).ToList().Contains(file.UploadedBy))
                        {
                            file.UploadedBy = user.Data.List.Where(p => p.Name == file.UploadedBy).FirstOrDefault().DisplayName;
                        }
                    }
                }
                ret.Data = bizFiles;

            }
            return ret;
        }
        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds")]
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(CustomizeInvoiceAttachFileInput input)
        {
            return await _customizeInvoiceAppService.DeleteAttachFileIds(input);
        }
        /// <summary>
        /// 删除结算清单附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds_jsqd")]
        public async Task<BaseResponseData<string>> DeleteAttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input)
        {
            return await _customizeInvoiceAppService.DeleteAttachFileIds_jsqd(input);
        }

        /// <summary>
        /// 撤回开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RecallCustomizeInvoice")]
        public async Task<ResponseData<int>> RecallCustomizeInvoice([FromBody] RecallCustomizeInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.RecallCustomizeInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 保存开票单明细 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveCustomizeInvoiceDetail")]
        public async Task<ResponseData<int>> SaveCustomizeInvoiceDetail([FromBody] SaveCustomizeInvoiceDetailInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.SaveCustomizeInvoiceDetail(input);
                if (count >= 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 设置为另一个开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetAsAnotherInvoice")]
        public async Task<ResponseData<int>> SetAsAnotherInvoice([FromBody] SetAsAnotherInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.SetAsAnotherInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 批量设置为另一个开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("BatchSetAsAnotherInvoice")]
        public async Task<ResponseData<int>> BatchSetAsAnotherInvoice([FromBody] SetAsAnotherInvoiceInput input)
        {
            try
            {
                input.CreateBy = CurrentUser.UserName;
                var (count, msg) = await _customizeInvoiceAppService.BatchSetAsAnotherInvoice(input);
                if (count > 0)
                {
                    return new ResponseData<int>
                    {
                        Code = 200,
                        Data = new Data<int>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<int>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 开红冲发票 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("createOffsetInvoice")]
        public async Task<BaseResponseData<string>> CreateOffsetInvoice(CreateOffsetInvoiceInput input)
        {
            var cachekey = "CreateOffsetInvoice_" + input.Id;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                var jsonStr = JsonConvert.SerializeObject(input);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    input.CreatedBy = CurrentUser.UserName;
                    var ret = await _customizeInvoiceAppService.CreateOffsetInvoice(input);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        _easyCaching.Remove(cachekey);
                    }
                    return ret;
                }
                return BaseResponseData<string>.Failed(500, "操作是失败,原因：5秒内请勿重复提交，请稍后操作！");
            }
            catch (Exception)
            {
                _easyCaching.Remove(cachekey);
                throw;
            }

        }

        /// <summary>
        /// 导出申开单数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DownLoad")]
        public async Task<IActionResult> DownLoadAsync([FromBody] CustomizeInvoiceClassifyQueryInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id;
                var ret = await _customizeInvoiceQueryService.DownLoadAsync(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "单号";
                    worksheet.Cells[1, 2].Value = "收款单位";
                    worksheet.Cells[1, 3].Value = "付款单位";
                    worksheet.Cells[1, 4].Value = "开票总金额";
                    worksheet.Cells[1, 5].Value = "开票类型";
                    worksheet.Cells[1, 6].Value = "开票状态";
                    worksheet.Cells[1, 7].Value = "红冲状态";
                    worksheet.Cells[1, 8].Value = "红冲单号";
                    worksheet.Cells[1, 9].Value = "申请方";
                    worksheet.Cells[1, 10].Value = "红字信息表编号";
                    worksheet.Cells[1, 11].Value = "蓝字发票号";
                    worksheet.Cells[1, 12].Value = "蓝字发票代码";
                    worksheet.Cells[1, 13].Value = "蓝字红冲金额";
                    worksheet.Cells[1, 14].Value = "红冲原因";
                    worksheet.Cells[1, 15].Value = "发票备注";
                    worksheet.Cells[1, 16].Value = "审批备注";
                    //worksheet.Cells[1, 17].Value = "创建人";
                    worksheet.Cells[1, 17].Value = "创建时间";
                    worksheet.Cells[1, 18].Value = "原始品名";
                    worksheet.Cells[1, 19].Value = "货物或应税劳务服务名称(开票名称)";
                    worksheet.Cells[1, 20].Value = "原始单位";
                    worksheet.Cells[1, 21].Value = "计量单位";
                    worksheet.Cells[1, 22].Value = "数量";
                    worksheet.Cells[1, 23].Value = "规格型号";
                    worksheet.Cells[1, 24].Value = "原始规格";
                    worksheet.Cells[1, 25].Value = "单价";
                    worksheet.Cells[1, 26].Value = "金额";
                    worksheet.Cells[1, 27].Value = "税率";
                    worksheet.Cells[1, 28].Value = "税收分类编码";
                    worksheet.Cells[1, 29].Value = "税额";
                    worksheet.Cells[1, 30].Value = "不含税金额";
                    worksheet.Cells[1, 31].Value = "应收单号";
                    worksheet.Cells[1, 32].Value = "关联单号";
                    worksheet.Cells[1, 33].Value = "订单号";
                    worksheet.Cells[1, 34].Value = "行性质";
                    worksheet.Cells[1, 35].Value = "出库申请单号";
                    #endregion

                    #region 调用接口获取出库申请单号集合
                    var soaList = new List<StoreOutApplyListOutput>();
                    var relateCodes = new List<string>();
                    if (ret.Data != null && ret.Data.Any())
                    {
                        foreach (var item in ret.Data)
                        {
                            relateCodes.AddRange(item.CustomizeInvoiceDetail.Where(x => !string.IsNullOrEmpty(x.OrderNo)).Select(x => x.OrderNo).ToList());
                        }
                    }
                    var soaInput = new StoreOutApplyInput();
                    soaInput.UserId = CurrentUser.Id;
                    soaInput.PageSize = 1000;
                    soaInput.PageIndex = 1;
                    soaInput.RelateCodes = relateCodes;
                    // 以下为固定值
                    soaInput.ReturnApplyStatus = new List<int>() { 99 };
                    soaInput.OutboundStatus = new List<int>() { 0, 1, 2 };
                    var soaRet = await _storeOutApplyApiClient.GetStoreOutApplyList(soaInput);
                    if (soaRet.Code == CodeStatusEnum.Success)
                    {
                        if (soaRet.Data != null)
                        {
                            soaList = soaRet.Data.List;
                        }
                    }
                    #endregion

                    #region 数据
                    if (ret.Data != null && ret.Data.Any())
                    {
                        int row = 2;
                        foreach (var item in ret.Data)
                        {
                            foreach (var subitem in item.CustomizeInvoiceDetail)
                            {
                                worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 13].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 23].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 24].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 27].Style.Numberformat.Format = "#,##0.00";
                                worksheet.Cells[row, 28].Style.Numberformat.Format = "#,##0.00";

                                worksheet.Cells[row, 1].Value = item.Code;
                                worksheet.Cells[row, 2].Value = item.CompanyName;
                                worksheet.Cells[row, 3].Value = item.CustomerName;
                                worksheet.Cells[row, 4].Value = item.InvoiceTotalAmount;
                                worksheet.Cells[row, 5].Value = item.InvoiceTypeStr;
                                worksheet.Cells[row, 6].Value = item.StatusStr;
                                worksheet.Cells[row, 7].Value = item.ChangedStatus == CustomizeInvoiceChangedStatusEnum.RedOffset ? "已红冲" : "未红冲";
                                worksheet.Cells[row, 8].Value = item.RelationCode;
                                worksheet.Cells[row, 9].Value = item.RedOffsetOptStr;
                                worksheet.Cells[row, 10].Value = item.RedOffsetCode;
                                worksheet.Cells[row, 11].Value = item.InvoiceNo;
                                worksheet.Cells[row, 12].Value = item.InvoiceCode;
                                worksheet.Cells[row, 13].Value = item.BlueRedInvoiceAmount.HasValue ? item.BlueRedInvoiceAmount.Value : 0;
                                worksheet.Cells[row, 14].Value = item.RedOffsetReasonStr;
                                worksheet.Cells[row, 15].Value = item.Remark;
                                worksheet.Cells[row, 16].Value = item.ApproveRemark;
                                //worksheet.Cells[row, 17].Value = item.CreatedBy;
                                worksheet.Cells[row, 17].Value = item.CreatedTime.AddHours(8).ToString("yyyy-MM-dd");
                                worksheet.Cells[row, 18].Value = subitem.OriginProductName;
                                worksheet.Cells[row, 19].Value = subitem.ProductName;
                                worksheet.Cells[row, 20].Value = subitem.OriginPackUnit;
                                worksheet.Cells[row, 21].Value = subitem.PackUnit;
                                worksheet.Cells[row, 22].Value = subitem.Quantity.ToString();
                                worksheet.Cells[row, 23].Value = subitem.Specification;
                                worksheet.Cells[row, 24].Value = subitem.ProductNo;
                                worksheet.Cells[row, 25].Value = subitem.Price;
                                worksheet.Cells[row, 26].Value = subitem.Value;
                                worksheet.Cells[row, 27].Value = subitem.TaxRate;
                                worksheet.Cells[row, 28].Value = subitem.TaxTypeNo;
                                worksheet.Cells[row, 29].Value = subitem.TaxAmount;
                                worksheet.Cells[row, 30].Value = subitem.Value - subitem.TaxAmount;
                                worksheet.Cells[row, 31].Value = subitem.CreditBillCode;
                                worksheet.Cells[row, 32].Value = subitem.RelateCode;
                                worksheet.Cells[row, 33].Value = subitem.OrderNo;
                                worksheet.Cells[row, 34].Value = subitem.Tag == "折扣行" && subitem.Price < 0 ? "折扣行" : "商品行";

                                var storeOutApply = soaList.FirstOrDefault(x => x.RelateCode == subitem.OrderNo);
                                worksheet.Cells[row, 35].Value = storeOutApply != null ? storeOutApply.Code : string.Empty;

                                row++;
                            }
                        }
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 拆分
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SplitSubmit")]
        public async Task<BaseResponseData<int>> SplitSubmit(SplitSubmitInput input)
        {
            var ret = await _customizeInvoiceAppService.SplitSubmit(input);
            return ret;
        }
        #region 合并操作 
        /// <summary>
        /// 合并明细(开票名称，单价，原价合并)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("MargeByProductNamePriceOriginPrice")]
        public async Task<BaseResponseData<int>> MargeByProductNamePriceOriginPrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = await _customizeInvoiceAppService.MargeByProductNamePriceOriginPrice(input);
            return ret;
        }
        /// <summary>
        /// 折扣行合并
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("MergeByDiscount")]
        public async Task<BaseResponseData<int>> MergeByDiscount(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = await _customizeInvoiceAppService.MergeByDiscount(input);
            return ret;
        }
        [HttpPost("MargeByProductNamePriceSpecification")]
        public async Task<BaseResponseData<int>> MargeByProductNamePriceSpecification(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = await _customizeInvoiceAppService.MargeByProductNamePriceSpecification(input);
            return ret;
        }
        [HttpPost("MargeByProductNamePrice")]
        public async Task<BaseResponseData<int>> MargeByProductNamePrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = await _customizeInvoiceAppService.MargeByProductNamePrice(input);
            return ret;
        }
        [HttpPost("MergeByInputQuantityPrice")]
        public async Task<BaseResponseData<int>> MergeByInputQuantityPrice(MergeByInputQuantityPriceInput input)
        {
            var ret = await _customizeInvoiceAppService.MergeByInputQuantityPrice(input.customizeInvoiceDetails, input.quantity, input.price);
            return ret;
        }
        [HttpPost("MargeByProductNoPriceOriginPrice")]
        public async Task<BaseResponseData<int>> MargeByProductNoPriceOriginPrice(List<CustomizeInvoiceDetailOutput> input)
        {
            var ret = await _customizeInvoiceAppService.MargeByOriginSpecificationPriceOriginPrice(input);
            return ret;
        }
        [HttpPost("UpdateDetailQuantityPrice")]
        public async Task<BaseResponseData<int>> UpdateDetailQuantityPrice(MergeByInputQuantityPriceInput input)
        {
            var ret = await _customizeInvoiceAppService.UpdateDetailQuantityPrice(input);
            return ret;
        }
        #endregion
        #region 分类
        /// <summary>
        /// 获取开票分类列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetCustomizeInvoiceClassfiy")]
        public async Task<ResponseData<CustomizeInvoiceClassifyOutput>> GetCustomizeInvoiceClassfiy([FromBody] CustomizeInvoiceClassifyQueryInput input)
        {
            try
            {
                var strategyInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
                var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        return new ResponseData<CustomizeInvoiceClassifyOutput>
                        {
                            Data = new Data<CustomizeInvoiceClassifyOutput>
                            {
                                List = new List<CustomizeInvoiceClassifyOutput> { },
                                Total = 0
                            }
                        };
                    }
                }
                input.UserId = CurrentUser.Id;
                input.UserName = CurrentUser.UserName;
                var (list, count) = await _customizeInvoiceQueryService.GetCustomizeInvoiceClassify(input);
                return new ResponseData<CustomizeInvoiceClassifyOutput>
                {
                    Code = 200,
                    Data = new Data<CustomizeInvoiceClassifyOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 删除分类
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteCustomizeInvoiceClassify")]
        public async Task<BaseResponseData<int>> DeleteCustomizeInvoiceClassify(CustomizeInvoiceClassifyInput input)
        {
            var ret = await _customizeInvoiceAppService.DeleteCustomizeInvoiceClassify(input);
            return ret;
        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitByClassfiy")]
        public async Task<BaseResponseData<string>> SubmitByClassfiy(CustomizeInvoiceClassifyInput input)
        {
            var cachekey = "SubmitByClassfiy_" + input.classifyOutput.Id;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                var jsonStr = JsonConvert.SerializeObject(input);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(10));
                    var ret = await _customizeInvoiceAppService.SubmitByClassfiy(input);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        _easyCaching.Remove(cachekey);
                    }
                    return ret;
                }
                return BaseResponseData<string>.Failed(500, "操作是失败,原因：10秒内请勿重复提交，请稍后操作！");
            }
            catch (Exception)
            {
                _easyCaching.Remove(cachekey);
                throw;
            }
       
        }

        /// <summary>
        /// 按状态获取分页总数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetClassifyTabCount")]
        public async Task<BaseResponseData<CustomizeInvoiceItemTabOutput>> GetClassifyTabCountAsync([FromBody] CustomizeInvoiceClassifyQueryInput input)
        {
            input.UserName = CurrentUser.UserName;

            input.UserId = CurrentUser.Id;
            var model = await _customizeInvoiceQueryService.GetClassifyTabCountAsync(input);
            return new BaseResponseData<CustomizeInvoiceItemTabOutput>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }

        /// <summary>
        /// 提交开票单 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitClassfiy")]
        public async Task<BaseResponseData<string>> SubmitClassfiy([FromBody] CustomizeInvoiceClassifyInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            return await _customizeInvoiceAppService.SubmitByClassfiy(input);
        }

        /// <summary>
        /// 设置为另一个开票分类 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetAnotherCic")]
        public async Task<BaseResponseData<string>> SetAnotherCic([FromBody] SetAnotherCicInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            return await _customizeInvoiceAppService.SetAnotherCic(input);
        }

        /// <summary>
        /// 合并开票单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("MergeCii")]
        public async Task<BaseResponseData<string>> MergeCii([FromBody] SetAnotherCicInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            return await _customizeInvoiceAppService.MergeCii(input);
        }

        /// <summary>
        /// 提交开票前提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("BeforeSubmitClassfiy")]
        public async Task<BaseResponseData<List<customerInvoice>>> BeforeSubmitClassfiy([FromBody] CustomizeInvoiceClassifyInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            return await _customizeInvoiceAppService.BeforeSubmitClassfiy(input);
        }
        #endregion
        /// <summary>
        /// 一键生成服务费
        /// </summary>
        /// <returns></returns> 
        [HttpPost("generateServiceCustomizeInvoice")]
        public async Task<BaseResponseData<int>> GenerateServiceCustomizeInvoice([FromBody] GenerateServiceCustomizeInvoiceInput input)
        {
            var cachekey = "GenerateServiceCustomizeInvoice_" + input.CompanyId;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                var jsonStr = JsonConvert.SerializeObject(input);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(10));
                    var ret = await _customizeInvoiceAppService.GenerateServiceCustomizeInvoice(input, CurrentUser.UserName);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        _easyCaching.Remove(cachekey);
                    }
                    return ret;
                }
                return BaseResponseData<int>.Failed(500, "操作是失败,原因：10秒内请勿重复提交，请稍后操作！");
            }
            catch (Exception)
            {
                _easyCaching.Remove(cachekey);
                throw;
            }
        }

        /// <summary>
        /// 得到文件Ids
        /// </summary>
        /// <returns></returns> 
        [HttpPost("GetAttachFileIds")]
        public async Task<BaseResponseData<List<string>>> GetAttachFileIds([FromBody] CustomizeInvoiceClassifyQueryInput input)
        {
            var strategyInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null && strategry.RowStrategies.Count > 0)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    return new BaseResponseData<List<string>>();
                }
            }
            input.UserId = CurrentUser.Id;
            input.UserName = CurrentUser.UserName;
            return await _customizeInvoiceQueryService.GetAttachFileIds(input);
        }

        /// <summary>
        /// 预开票列表查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetPreCustomizeInvoiceList")]
        public async Task<ResponseData<PreCustomizeInvoiceListOutput>> GetPreCustomizeInvoiceList([FromBody] PreCustomizeInvoiceListInput input)
        {
            try
            {
                input.UserName = CurrentUser.UserName;
                input.UserId = CurrentUser.Id.Value;
                var (list, count) = await _customizeInvoiceQueryService.GetPreCustomizeInvoiceList(input);
                return new ResponseData<PreCustomizeInvoiceListOutput>
                {
                    Code = 200,
                    Data = new Data<PreCustomizeInvoiceListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                return new ResponseData<PreCustomizeInvoiceListOutput>
                {
                    Code = 500,
                    Msg = ex.Message,
                };
            }
        }

        /// <summary>
        /// 获取SPD发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPreCustomizeInvoiceTabCount")]
        public async Task<BaseResponseData<PreCustomizeInvoiceListTabOutput>> GetPreCustomizeInvoiceTabCount([FromBody] PreCustomizeInvoiceListInput query)
        {
            try
            {
                query.UserName = CurrentUser.UserName;
                query.UserId = CurrentUser.Id.Value;
                return await _customizeInvoiceQueryService.GetPreCustomizeInvoiceTabCount(query);
            }
            catch (Exception ex)
            {
                return new BaseResponseData<PreCustomizeInvoiceListTabOutput>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 预开票列表明细查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetPreCustomizeInvoiceDetails")]
        public async Task<ResponseData<PreCustomizeInvoiceDetailsOutput>> GetPreCustomizeInvoiceDetails([FromBody] PreCustomizeInvoiceDetailsInput input)
        {
            try
            {
                var (list, count) = await _customizeInvoiceQueryService.GetPreCustomizeInvoiceDetails(input);
                return new ResponseData<PreCustomizeInvoiceDetailsOutput>
                {
                    Code = 200,
                    Data = new Data<PreCustomizeInvoiceDetailsOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                return new ResponseData<PreCustomizeInvoiceDetailsOutput>
                {
                    Code = 500,
                    Msg = ex.Message,
                };
            }
        }

        /// <summary>
        /// 创建预开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreatePreCustomizeInvoice")]
        public async Task<BaseResponseData<string>> CreatePreCustomizeInvoice([FromBody] SubmitPreCustomizeInvoiceInput input)
        {
            try
            {
                input.createdBy = CurrentUser.UserName;
                return await _customizeInvoiceQueryService.CreatePreCustomizeInvoice(input);
            }
            catch (Exception ex)
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 删除预开票申请
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("DeletePreCustomizeInvoice")]
        public async Task<BaseResponseData<string>> DeletePreCustomizeInvoice(Guid id)
        {
            try
            {
                return await _customizeInvoiceQueryService.DeletePreCustomizeInvoice(id);
            }
            catch (Exception ex)
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 导入预开票明细
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("ExportPreCustomizeInvoiceDetails")]
        public async Task<BaseResponseData<int>> ExportPreCustomizeInvoiceDetails(IFormFile file, [FromQuery] Guid? id)
        {
            return await _customizeInvoiceQueryService.ExportPreCustomizeInvoiceDetails(file, id);
        }

        /// <summary>
        /// 提交预开票（创建申开单和明细）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("SubmitPreCustomizeInvoice")]
        public async Task<BaseResponseData<int>> SubmitPreCustomizeInvoice([FromQuery] Guid? id)
        {
            return await _customizeInvoiceQueryService.SubmitPreCustomizeInvoice(id);
        }

        /// <summary>
        /// 导入初始应收开票
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("ExportInitCredit")]
        public async Task<BaseResponseData<int>> ExportInitCredit(IFormFile file)
        {
            var userName = CurrentUser.UserName;
            return await _customizeInvoiceQueryService.ExportInitCredit(file, userName);
        }

        /// <summary>
        /// 同步税收分类编码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SyncTaxTypeNo")]
        public async Task<BaseResponseData<int>> SyncTaxTypeNo([FromBody] SyncTaxTypeNoInput input)
        {
            var ret = await _customizeInvoiceAppService.SyncTaxTypeNo(input);
            return ret;
        }
        /// <summary>
        /// 导出主单和明细数据ExportCustomizeInvoiceItem
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ExportCustomizeInvoiceItemAndDetail")]
        public async Task<IActionResult> ExportCustomizeInvoiceItemAndDetail([FromBody] OriginDetailQueryInput query)
        {
            query.page = 1;
            query.limit = int.MaxValue;
            var stream = await _customizeInvoiceQueryService.ExportCustomizeInvoiceItemAndDetail(query);
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }
        /// <summary>
        /// 导出主单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ExportCustomizeInvoiceItem")]
        public async Task<IActionResult> ExportCustomizeInvoiceItem([FromBody] CreditQueryInput query)
        {
            query.page = 1;
            query.limit = int.MaxValue;
            if (!query.IsNoNeedInvoice.HasValue)
            {
                query.IsNoNeedInvoice = Domain.IsNoNeedInvoiceEnum.Need;
            }
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;
            var stream = await _creditQueryService.ExportCustomizeInvoiceItem(query);
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }
        [HttpPost("exportCustomizeInvoiceTask")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportCustomizeInvoiceTask([FromBody] CreditQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            if (!query.IsNoNeedInvoice.HasValue)
            {
                query.IsNoNeedInvoice = Domain.IsNoNeedInvoiceEnum.Need;
            }
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _customizeInvoiceQueryService.ExportCustomizeInvoiceTask(query);
        }
        /// <summary>
        /// 运营提交开票导入明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExportDetails")]
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportCustomizeInvoiceDetail([FromBody] CustomizeInvoiceDetailExcelInput input)
        {
            return await _customizeInvoiceAppService.ExportCustomizeInvoiceDetail(input.FileId, input.CustomizeInvoiceItemId, CurrentUser.UserName);
        }
        /// <summary>
        /// 导出页面选中的主单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ExportCustomizeInvoiceItemByIds")]
        public async Task<IActionResult> ExportCustomizeInvoiceItemByIds([FromBody] OriginDetailQueryInput query)
        {
            query.page = 1;
            query.limit = int.MaxValue;
            var stream = await _customizeInvoiceQueryService.ExportCustomizeInvoiceItemByIds(query);
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        /// <summary>
        /// 批量开票（制作开票导入）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExportBatchInvoicing")]
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoicing([FromBody] CustomizeInvoiceDetailExcelInput input)
        {
            return await _customizeInvoiceAppService.ExportBatchInvoicing(input.FileId, CurrentUser.UserName, CurrentUser.Id);
        }
        /// <summary>
        /// 批量开票(导入开票明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExportBatchInvoiceDetail")]
        public async Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoiceDetail([FromBody] CustomizeInvoiceDetailExcelInput input)
        {
            return await _customizeInvoiceAppService.ExportBatchInvoiceDetail(input.FileId, CurrentUser.UserName, CurrentUser.Id);
        }
        /// <summary>
        /// 合并校验
        /// </summary>
        /// <param name="details"></param> 
        /// <returns></returns>
        [HttpPost("MargeCheck")]
        public BaseResponseData<string> MargeCheck(CheckDetailOutput input)
        {
            return _customizeInvoiceAppService.MargeCheck(input.Details, input.Source);
        }
    }
}
