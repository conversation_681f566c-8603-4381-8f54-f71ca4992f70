﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class BulkPaymentQueryService : QueryAppService, IBulkPaymentQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bdsClient;
        private readonly IWeaverApiClient weaverApiClient;
        private readonly IInputBillQueryService _inputBillQueryService;
        /// <summary>
        /// 外部能力中心实例
        /// </summary>
        protected readonly IPCApiClient _pcApiClient;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        public BulkPaymentQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db, IUnitOfWork unitOfWork, IBDSApiClient bdsClient, IPCApiClient pcApiClient, IWeaverApiClient weaverApiClient, IInputBillQueryService inputBillQueryService)
            : base(contextAccessor)
        {
            this._db = db;
            this._unitOfWork = unitOfWork;
            _bdsClient = bdsClient;
            _pcApiClient = pcApiClient;
            this.weaverApiClient = weaverApiClient;
            this._inputBillQueryService = inputBillQueryService;
        }
        /// <summary>
        /// 获取批量付款清单
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<PaymnetItemOutput>> GetPaymentItems(BulkPaymentQueryInput input)
        {
            //获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategys = await _pcApiClient.GetStrategyAsync(strategryquery);

            if (strategys != null && !input.Id.HasValue)
            {
                var rowStrategies = strategys.RowStrategies;
                if (!rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("accountingDept"))
                {
                    return new PageResponse<PaymnetItemOutput>() { List = new List<PaymnetItemOutput> { }, Total = 0 };
                }
            }


            var query = _db.PaymentAutoItems
                           .WhereIf(input.Id.HasValue, t => t.Id == input.Id)
                           .WhereIf(!string.IsNullOrEmpty(input.Code), t => EF.Functions.Like(t.Code, $"%{input.Code}%"))
                           .WhereIf(!string.IsNullOrEmpty(input.searchKey), t => EF.Functions.Like(t.Code, $"%{input.searchKey}%") || EF.Functions.Like(t.CompanyName, $"%{input.searchKey}%"))
                           .WhereIf(input.CompanyId.HasValue, t => t.CompanyId == input.CompanyId)
                           .WhereIf(input.Status.HasValue && input.Status > -1 && input.Status != 5000, t => (int)t.Status == input.Status)
                           .WhereIf(input.Status == 5000, t => t.Status == PaymentAutoItemStatusEnum.Auditing)
                           .WhereIf(input.Status.HasValue && input.Status == 0, t => t.CreatedBy == input.UserName)
                           .WhereIf(input.BillDateStart.HasValue, t => t.BillDate >= Utility.ConvertDate((long)input.BillDateStart))
                           .WhereIf(input.BillDateEnd.HasValue, t => t.BillDate <= Utility.ConvertDate((long)input.BillDateEnd))
                           .WhereIf(input.ProbablyPayTimeStart.HasValue, t => t.PaymentAutoDetails.OrderBy(o => o.DebtDetail.ProbablyPayTime).FirstOrDefault().DebtDetail.ProbablyPayTime >= Utility.ConvertDate((long)input.ProbablyPayTimeStart!.Value))
                           .WhereIf(input.ProbablyPayTimeEnd.HasValue, t => t.PaymentAutoDetails.OrderBy(o => o.DebtDetail.ProbablyPayTime).FirstOrDefault().DebtDetail.ProbablyPayTime <= Utility.ConvertDate((long)input.ProbablyPayTimeEnd!.Value))
                           .WhereIf(input.CreatedBy != null && input.CreatedBy.Count > 0, t => input.CreatedBy.Contains(t.CreatedBy))
                           .AsNoTracking();
            if (input.DetailCode != null)
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.BillCode == input.DetailCode
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));

            }
            if (!string.IsNullOrEmpty(input.PurchaseCode))
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.PurchaseCode == input.PurchaseCode
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));

            }
            if (!string.IsNullOrEmpty(input.PurchaseContactNo))
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.PurchaseContactNo == input.PurchaseContactNo
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));

            }
            if (input.AgentId.HasValue)
            {
                var itemIds = (from d in _db.PaymentAutoDetails
                               join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                               from t in tempdetail.DefaultIfEmpty()
                               join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                               from e in tempdebt.DefaultIfEmpty()
                               where e.AgentId == input.AgentId
                               select d.PaymentAutoItemId)?.Distinct();
                if (itemIds == null || itemIds.Count() == 0)
                {
                    return new PageResponse<PaymnetItemOutput>() { List = new List<PaymnetItemOutput> { }, Total = 0 };
                }

                query = query.Where(t => itemIds.Contains(t.Id));
            }
            if (input.Status != 5000 && !input.Id.HasValue)
            {
                query = AddStrategyQueryAsync(strategys, query);
            }
            if (input.Status == 5000)
            {
                var oaResultPre = await weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.BatchPaymentForm.GetDescription()));
                var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                query = query.Where(t => t.OARequestId != null && oARequestIds.Contains(t.OARequestId));
            }
            //总条数
            var count = await query.CountAsync();
            //分页
            var list = await query.OrderByDefault(input.sort).Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<PaymnetItemOutput>()).ToListAsync();
            var ids = list.Select(t => t.Id).ToList();
            var details = await _db.PaymentAutoDetails.Include(p => p.DebtDetail).Where(t => ids.Contains(t.PaymentAutoItemId)).ToListAsync();
            foreach (var item in list)
            {
                item.SumValue = details.Where(t => t.PaymentAutoItemId == item.Id).Sum(q => q.DebtDetail.Value);
                var thisDetail = details.Where(t => t.PaymentAutoItemId == item.Id).OrderBy(p => p.DebtDetail.ProbablyPayTime).FirstOrDefault();
                if (thisDetail != null)
                {
                    item.ProbablyPayTime = thisDetail.DebtDetail.ProbablyPayTime?.ToString("yyyy-MM-dd") ?? "";
                }
                else
                {
                    item.ProbablyPayTime = "";
                }
            }
            return new PageResponse<PaymnetItemOutput>() { List = list, Total = count };
        }
        /// <summary>
        /// 获取批量付款详情
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentDetailOutput>> GetPaymentDetails(QueryById input)
        {
            var query = from d in _db.PaymentAutoDetails
                        join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                        from t in tempdetail.DefaultIfEmpty()
                        join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                        from e in tempdebt.DefaultIfEmpty()
                        join crt in _db.Credits on t.CreditId equals crt.Id into crtdebtdetailtemp
                        from crtdebtdetail in crtdebtdetailtemp.DefaultIfEmpty()
                        where d.PaymentAutoItemId == input.Id
                        select new PaymentDetailOutput
                        {
                            Id = d.Id,
                            DebtDetailId = t.Id,
                            AccountPeriodType = t.AccountPeriodType,
                            ServiceName = e.ServiceName,
                            ServiceId = e.ServiceId,
                            AgentId = e.AgentId,
                            AgentName = e.AgentName,
                            CreditBillCode = crtdebtdetail == null ? "" : crtdebtdetail.BillCode,
                            CreditAbatedValue = crtdebtdetail == null ? 0 : crtdebtdetail.Value, //应收金额——应收冲销金额
                            DebtBillCode = e.BillCode,
                            RelateCode = e.RelateCode,
                            DebtAbatedValue = 0,//应付冲销金额——计算
                            DebtDetailValue = t.Value,
                            DebtValue = e.Value,
                            DebtBalance = e.Value - t.Value,
                            CoinName = e.CoinName,
                            Discount = t.CostDiscount,
                            HospitalName = crtdebtdetail == null ? "" : crtdebtdetail.HospitalName,
                            OriginValue = t.OriginValue,
                            ReceiveCode = t.ReceiveCode,
                            CustomerName = crtdebtdetail == null ? "" : crtdebtdetail.CustomerName,
                            CustomerId = crtdebtdetail == null ? Guid.Empty : crtdebtdetail.CustomerId,
                            ProducerOrderNo = e.ProducerOrderNo,
                            PurchaseContactNo = e.PurchaseContactNo,
                            PaymentCode = d.PaymentCode,
                            OrderNo = t.OrderNo,
                            ProbablyPayTime = t.ProbablyPayTime,
                            CreditId = crtdebtdetail == null ? Guid.Empty : crtdebtdetail.Id,
                            PurchaseCode = e.PurchaseCode,
                            LimitedDiscount = d.LimitedDiscount,
                            //InvoiceNo = (from it in _db.InvoiceCredits where it.CreditId == crtdebtdetail.Id select it.InvoiceNo).First(),
                        };
            if (!string.IsNullOrEmpty(input.DebtBillCode))
            {
                query = query.Where(p => p.DebtBillCode != null && p.DebtBillCode.Contains(input.DebtBillCode));
            }
            if (!string.IsNullOrEmpty(input.PurchaseContactNo))
            {
                query = query.Where(p => p.PurchaseContactNo != null && p.PurchaseContactNo.Contains(input.PurchaseContactNo));
            }
            if (!string.IsNullOrEmpty(input.ProducerOrderNo))
            {
                query = query.Where(p => p.ProducerOrderNo != null && p.ProducerOrderNo.Contains(input.ProducerOrderNo));
            }
            if (input.ServicesId.HasValue)
            {
                query = query.Where(p => p.ServiceId.HasValue && p.ServiceId == input.ServicesId);
            }
            if (!string.IsNullOrEmpty(input.PurchaseCode))
            {
                query = query.Where(p => p.PurchaseCode != null && p.PurchaseCode.Contains(input.PurchaseCode));
            }
            if (input.AgentId.HasValue)
            {
                query = query.Where(p => p.AgentId.HasValue && p.AgentId == input.AgentId);
            }
            if (input.CustomerId.HasValue)
            {
                query = query.Where(p => p.CustomerId.HasValue && p.CustomerId == input.CustomerId);
            }
            if (!string.IsNullOrEmpty(input.PaymentCode))
            {
                query = query.Where(p => p.PaymentCode != null && p.PaymentCode == input.PaymentCode);
            }
            if (input.AccountPeriod.HasValue)
            {
                query = query.Where(p => p.AccountPeriodType.HasValue && p.AccountPeriodType == input.AccountPeriod);
            }
            if (!string.IsNullOrEmpty(input.CreditBillCode))
            {
                query = query.Where(p => p.CreditBillCode != null && p.CreditBillCode.Contains(input.CreditBillCode));
            }
            if (!string.IsNullOrEmpty(input.ReceiveCode))
            {
                query = query.Where(p => p.ReceiveCode != null && p.ReceiveCode.Contains(input.ReceiveCode));
            }
            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                var invoiceCreaditIds = await _db.InvoiceCredits.Where(p => p.InvoiceNo != null && p.InvoiceNo.Contains(input.InvoiceNo)).Select(o => o.CreditId).ToListAsync();
                if (invoiceCreaditIds.Count == 0)
                {
                    return new List<PaymentDetailOutput>();
                }
                query = query.Where(p => invoiceCreaditIds.Contains(p.CreditId));
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                query = query.Where(p => p.OrderNo != null && p.OrderNo.Contains(input.OrderNo));
            }
            if (input.Discount.HasValue)
            {
                if (input.Discount == 0)
                {
                    query = query.Where(p => p.Discount == null || p.Discount == input.Discount);
                }
                else
                {
                    query = query.Where(p => p.Discount != null && p.Discount == input.Discount);
                }
            }
            if (!string.IsNullOrEmpty(input.HospitalName))
            {
                query = query.Where(p => p.HospitalName != null && p.HospitalName.Contains(input.HospitalName));
            }
            if (!string.IsNullOrEmpty(input.CoinName))
            {
                query = query.Where(p => p.CoinName != null && p.CoinName.Contains(input.CoinName));
            }

            var result = await query.AsNoTracking().ToListAsync();
            //获取已冲销金额
            var debtCodes = result.Select(p => p.DebtBillCode).ToList();
            var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            var abatments2 = await _db.Abatements.Where(p => debtCodes.Contains(p.CreditBillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            abatments.AddRange(abatments2);
            //获取进项票金额
            //var relateCodes = result.Select(p => p.RelateCode).ToList();
            //var noTaxAmountList = await _db.InputBillSubmitDetails.Where(p => debtCodes.Contains(p.StoreInItemCode) && p.InputBill.Status == 2).Include(x => x.InputBill).AsNoTracking().ToListAsync();
            //var noTaxAmountList2 = await _db.InputBillSubmitDetails.Where(p => relateCodes.Contains(p.StoreInItemCode) && p.InputBill.Status == 2).Include(x => x.InputBill).AsNoTracking().ToListAsync();
            //noTaxAmountList.AddRange(noTaxAmountList2);

            var noTaxAmountList = new List<InputBillQueryByDebtCodesOutput>();
            noTaxAmountList = await _inputBillQueryService.GetListInputBillByDebtCodesAsync(debtCodes);

            var mergeInputList = await (from md in _db.MergeInputBillDebts
                                        join mr in _db.MergeInputBillRelations on md.MergeInputBillId equals mr.MergeInputBillId
                                        join ib in _db.InputBills on mr.InputBillId equals ib.Id
                                        where debtCodes.Contains(md.DebtCode) && ib.Status == (int)InputBillStatusEnum.Submitted
                                        select new InputBillQueryByDebtCodesOutput()
                                        {
                                            BillCode = md.DebtCode,
                                            InvoiceNumber = ib.InvoiceNumber,
                                            NoTaxAmount = md.DebtAmount,
                                            Status = ib.Status,
                                            Value = md.DebtAmount,
                                            Id = md.Id,
                                        }).ToListAsync();
            if (mergeInputList.Any())
            {
                noTaxAmountList.AddRange(mergeInputList);
            }


            var creditIds = result.Select(p => p.CreditId).ToList();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
            var creditBillCodes = credits.Select(x => x.BillCode).ToList();
            var invoices = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value)).AsNoTracking().ToListAsync();
            //查询发票相关信息
            var invoiceNos = invoices.Select(x => x.InvoiceNo).ToHashSet();
            var invoiceInfos = await _db.InvoiceCredits.Where(x => invoiceNos.Contains(x.InvoiceNo)).AsNoTracking().ToListAsync();
            //查询发票对应应收冲销信息 
            var abas = await _db.Abatements.Where(p => creditBillCodes.Contains(p.DebtBillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            var abas2 = await _db.Abatements.Where(p => creditBillCodes.Contains(p.CreditBillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            abas.AddRange(abas2);
            //查询收款信息
            var receiveCodes = result.Select(p => p.ReceiveCode).ToList();
            var rris = await _db.RecognizeReceiveItems.Where(x => receiveCodes.Contains(x.ReceiveCode)).ToListAsync();
            var rrds = await _db.RecognizeReceiveDetails.Where(x => invoiceNos.Contains(x.Code)).ToListAsync();
            var orderNos = result.Select(p => p.OrderNo).ToList();
            var rrdByOrders = await _db.RecognizeReceiveDetails.Where(x => orderNos.Contains(x.Code)).ToListAsync();
            foreach (var item in result)
            {
                var recognizeDate = string.Empty;  //收款认领时间 
                var rri = rris.Where(x => x.ReceiveCode == item.ReceiveCode).ToList();
                if (rri != null && rri.Any())
                {
                    item.ReceiveDate = rri[0].ReceiveDate;
                    item.ReceiveAmount = rri[0].ReceiveValue;
                    item.Payer = rri[0].CustomerNme;
                }
                var invoice = invoices.FirstOrDefault(p => p.CreditId == item.CreditId);
                item.InvoiceNo = invoice == null ? "" : string.Join(",", invoices.Where(x => x.CreditId == item.CreditId).Select(x => x.InvoiceNo).ToList());
                item.DebtAbatedValue = abatments.Where(t => t.DebtBillCode == item.DebtBillCode || t.CreditBillCode == item.DebtBillCode).Sum(t => t.Value);
                item.DebtBalance = Math.Abs(item.DebtValue ?? 0) - item.DebtAbatedValue;
                item.InvoiceTime = invoice == null ? "" : invoice.InvoiceTime.Value.ToString("yyyy-MM-dd");
                item.NoTaxAmount = noTaxAmountList.Where(p => p.BillCode == item.DebtBillCode).GroupBy(g=>g.Id).Sum(p => p.FirstOrDefault()?.NoTaxAmount);
                item.InvoiceCodes = string.Join(',', noTaxAmountList.Where(p => p.BillCode == item.DebtBillCode)?.Select(p => p.InvoiceNumber)?.Distinct() ?? []);

                var list = invoices.Where(x => x.CreditId == item.CreditId).ToList();
                var invoiceInfoLst = new List<InvoiceInfo>();
                foreach (var j in list)
                {
                    var currentInvoice = invoiceInfos.FirstOrDefault(x => x.InvoiceNo == j.InvoiceNo);
                    var currentCreditIds = invoiceInfos.Where(x => x.InvoiceNo == j.InvoiceNo).Select(x => x.CreditId).ToList();
                    var currentCredits = credits.Where(x => currentCreditIds.Contains(x.Id)).ToList();
                    var currentCreditBillCodes = currentCredits.Select(x => x.BillCode).ToList();
                    var currentAbas = abas.Where(p => currentCreditBillCodes.Contains(p.DebtBillCode) || currentCreditBillCodes.Contains(p.CreditBillCode)).ToList();
                    var currentRrds = rrds.Where(x => x.Code == j.InvoiceNo).ToList();
                    if (!string.IsNullOrEmpty(item.InvoiceNo))
                    {
                        System.DateTime? time = currentRrds != null && currentRrds.Any() ? currentRrds[0].RecognizeDate : null;
                        if (time.HasValue)
                        {
                            recognizeDate = time.Value.ToString("yyyy-MM-dd");
                        }
                    }
                    else
                    {
                        var currentRrdByOrders = rrdByOrders.Where(x => x.Code == item.OrderNo).ToList();
                        System.DateTime? time = currentRrdByOrders != null && currentRrdByOrders.Any() ? currentRrdByOrders[0].RecognizeDate : null;
                        if (time.HasValue)
                        {
                            recognizeDate = time.Value.ToString("yyyy-MM-dd");
                        }
                    }
                    invoiceInfoLst.Add(new InvoiceInfo
                    {
                        InvoiceNo = j.InvoiceNo,
                        InvoiceAmount = currentInvoice != null ? currentInvoice.InvoiceAmount : 0,
                        InvoiceTime = j.InvoiceTime,
                        WriteOffAmount = currentRrds.Sum(x => x.Value),
                        RecognizeDate = recognizeDate,
                        Abtdate = currentAbas != null && currentAbas.Any() ? currentAbas[0].Abtdate : null,
                        CreditValueLst = currentCredits.Select(x => x.Value).ToList()
                    });
                }
                item.InvoiceInfoLst = invoiceInfoLst;
            }
            return result;
        }

        /// <summary>
        /// 获取批量付款详情
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentDetailOutput>> GetPaymentDetailsOfAmount(Guid? id)
        {
            var result = await (from d in _db.PaymentAutoDetails.Where(x => x.PaymentAutoItemId == id)
                                join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                                from t in tempdetail.DefaultIfEmpty()
                                join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                                from e in tempdebt.DefaultIfEmpty()
                                select new PaymentDetailOutput
                                {

                                    AgentId = e.AgentId,
                                    AgentName = e.AgentName,
                                    DebtDetailValue = t.Value,

                                }).AsNoTracking().ToListAsync();
            return result;
        }

        /// <summary>
        /// 获取批量付款详情——按供应商聚合
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentAggratByAgent>> GetPaymentDetailsAggregation(Guid? id)
        {
            try
            {
                var paymentAuto = _db.PaymentAutoItems.FirstOrDefault(p => p.Id == id);
                if (paymentAuto != null)
                {
                    var list = await GetPaymentDetailsOfAmount(id);
                    var agentIds = list.Select(p => p.AgentId.Value).Distinct().ToList();
                    var agents = await _bdsClient.GetAgentBankInfoByAgentIds(agentIds);
                    var paymentAutoAgents = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == id).ToListAsync();
                    var bankInfoext = new List<AgentBankInfo>();
                    foreach (var agentId in agentIds)
                    {
                        var agent = agents.FirstOrDefault(p => p.agentId == agentId);
                        if (agent == null || agent.receiptList == null || !agent.receiptList.Any())
                        {
                            var paymentAutoAgent = paymentAutoAgents.FirstOrDefault(p => p.AgentId == agentId);
                            if (paymentAutoAgent != null)
                            {
                                bankInfoext.Add(new AgentBankInfo
                                {
                                    account = paymentAutoAgent.AccountName,
                                    bank = paymentAutoAgent.BankName,
                                    bankCode = paymentAutoAgent.BankCode,
                                    bankNo = paymentAutoAgent.Account,
                                    agentId = agentId,
                                    registerAddress = string.Empty,
                                    registerAddressName = string.Empty,
                                    type = paymentAutoAgent.Paymentabroad,
                                });
                            }
                        }
                        else
                        {
                            foreach (var subitem in agent.receiptList)
                            {
                                bankInfoext.Add(new AgentBankInfo
                                {
                                    account = subitem.account,
                                    bank = subitem.bank,
                                    bankCode = subitem.bankCode,
                                    bankNo = subitem.bankNo,
                                    agentId = agent.agentId,
                                    registerAddress = agent.registerAddress,
                                    registerAddressName = agent.registerAddressName,
                                    type = subitem.type,
                                });
                            }
                        }

                    }
                    var exsistBanks = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == id).AsNoTracking().ToListAsync();
                    var result = list.GroupBy(t => new { t.AgentId })
                        .Select(t => new PaymentAggratByAgent
                        {
                            Id = exsistBanks.FirstOrDefault(w => w.AgentId == t.Key.AgentId)?.Id,
                            AgentId = t.Key.AgentId,
                            AgentName = agents.Where(q => q.agentId == t.Key.AgentId).Select(p => p.agentName).First(),
                            Sum = t.Sum(s => s.DebtDetailValue),
                            PaymentAutoItemId = id.Value,
                            AttachFileIds = exsistBanks.FirstOrDefault(w => w.AgentId == t.Key.AgentId)?.AttachFileIds,
                            BankInfo = bankInfoext.Where(q => q.agentId == t.Key.AgentId
                            && !string.IsNullOrEmpty(q.account)
                            && !string.IsNullOrEmpty(q.bankCode)
                            && !string.IsNullOrEmpty(q.bankNo)
                            && !string.IsNullOrEmpty(q.bank)
                            ).Select(a => new AgentBank
                            {
                                AgentId = t.Key.AgentId.Value,
                                Account = a.account,
                                BankCode = a.bankCode,
                                BankNo = a.bankNo,
                                Bank = a.bank,
                                type = a.type,
                                RegisterAddressName = a.registerAddressName,

                                TransferDiscourse = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId)?.TransferDiscourse,
                                PayClassify = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId)?.PayClassify,
                                IsSelected = exsistBanks.Any(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo)) ? 1 : 0,
                                importGoods = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.importGoods,
                                CostBearingParty = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.CostBearingParty,
                                Ynpush = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.Ynpush,
                                Postscript = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.Postscript,
                                Transactioncoding = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.Transactioncoding,
                                Paymentabroad = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.Paymentabroad,
                                Contractno = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.Contractno,
                                InvoiceNo = exsistBanks.FirstOrDefault(c => c.AgentId == t.Key.AgentId && (c.Account == a.bankNo || paymentAuto.Status != PaymentAutoItemStatusEnum.WaitSubmit))?.InvoiceNo,
                            }).
                            OrderByDescending(p => p.IsSelected).ToList()
                        }).ToList();

                    return result;
                }
                else
                {
                    throw new ApplicationException("没有找到批量付款单号");
                }
            }
            catch (Exception ex)
            {

                throw new ApplicationException("按供应商聚合获取批量付款详情错误" + ex.Message);
            }
        }


        /// <summary>
        /// 获取tab数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BulkPaymentTabOutput> GetTabCountAsync(BulkPaymentQueryInput input)
        {
            var query = _db.PaymentAutoItems
               .WhereIf(!string.IsNullOrEmpty(input.Code), t => EF.Functions.Like(t.Code, $"%{input.Code}%"))
               .WhereIf(!string.IsNullOrEmpty(input.searchKey), t => EF.Functions.Like(t.Code, $"%{input.searchKey}%") || EF.Functions.Like(t.CompanyName, $"%{input.searchKey}%"))
               .WhereIf(input.CompanyId.HasValue, t => t.CompanyId == input.CompanyId)
               .WhereIf(input.BillDateStart.HasValue, t => t.BillDate >= Utility.ConvertDate((long)input.BillDateStart))
               .WhereIf(input.BillDateEnd.HasValue, t => t.BillDate <= Utility.ConvertDate((long)input.BillDateEnd))
               .WhereIf(input.ProbablyPayTimeStart.HasValue, t => t.PaymentAutoDetails.OrderBy(o => o.DebtDetail.ProbablyPayTime).FirstOrDefault().DebtDetail.ProbablyPayTime >= Utility.ConvertDate((long)input.ProbablyPayTimeStart!.Value))
               .WhereIf(input.ProbablyPayTimeEnd.HasValue, t => t.PaymentAutoDetails.OrderBy(o => o.DebtDetail.ProbablyPayTime).FirstOrDefault().DebtDetail.ProbablyPayTime <= Utility.ConvertDate((long)input.ProbablyPayTimeEnd!.Value))
               .WhereIf(input.CreatedBy != null && input.CreatedBy.Count > 0, t => input.CreatedBy.Contains(t.CreatedBy))
               .AsNoTracking();
            if (input.DetailCode != null)
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.BillCode == input.DetailCode
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));
            }
            if (input.AgentId.HasValue)
            {
                var itemIds = (from d in _db.PaymentAutoDetails
                               join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                               from t in tempdetail.DefaultIfEmpty()
                               join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                               from e in tempdebt.DefaultIfEmpty()
                               where e.AgentId == input.AgentId
                               select d.PaymentAutoItemId)?.Distinct();
                if (itemIds == null || itemIds.Count() == 0)
                {
                    return new BulkPaymentTabOutput();
                }

                query = query.Where(t => itemIds.Contains(t.Id));
            }

            if (!string.IsNullOrEmpty(input.PurchaseCode))
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.PurchaseCode == input.PurchaseCode
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));

            }
            if (!string.IsNullOrEmpty(input.PurchaseContactNo))
            {
                var iids = from d in _db.PaymentAutoDetails
                           join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                           from t in tempdetail.DefaultIfEmpty()
                           join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                           from e in tempdebt.DefaultIfEmpty()
                           where e.PurchaseContactNo == input.PurchaseContactNo
                           select d.PaymentAutoItemId;

                query = query.Where(t => iids.Contains(t.Id));

            }

            var myAuditList = await query.ToListAsync();

            //获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategys = await _pcApiClient.GetStrategyAsync(strategryquery);

            if (strategys != null)
            {
                var rowStrategies = strategys.RowStrategies;
                if (!rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("accountingDept"))
                {
                    return new BulkPaymentTabOutput();
                }
            }
            query = AddStrategyQueryAsync(strategys, query);
            var oaResultPre = await weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.BatchPaymentForm.GetDescription()));
            var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
            var res = await query.AsNoTracking().ToListAsync();
            var result = new BulkPaymentTabOutput
            {
                AllCount = res.Count(),
                AuditingCount = res.Where(t => t.Status == PaymentAutoItemStatusEnum.Auditing).Count(),
                WaitSubmitCount = res.Where(t => t.Status == PaymentAutoItemStatusEnum.WaitSubmit && t.CreatedBy == input.UserName).Count(),
                MyAuditCount = myAuditList.Where(t => t.Status == PaymentAutoItemStatusEnum.Auditing &&
                                              t.OARequestId != null &&
                                              oARequestIds.Contains(t.OARequestId)).Count(),
            };
            result.CompletedCount = res.Where(t => t.Status == PaymentAutoItemStatusEnum.Completed).Count();
            result.WaitExecuteCount = res.Where(t => t.Status == PaymentAutoItemStatusEnum.WaitExecute).Count();
            return result;
        }

        /// <summary>
        /// 数据策略权限增加
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        private IQueryable<PaymentAutoItemPo> AddStrategyQueryAsync(StrategyQueryOutput strategys, IQueryable<PaymentAutoItemPo> query)
        {
            if (strategys != null && strategys.RowStrategies.Count > 0)
            {
                query = AnalysisStrategy(strategys.RowStrategies, query);
            }
            return query;
        }

        /// <summary>
        /// 增加数据策略权限
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected IQueryable<PaymentAutoItemPo> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<PaymentAutoItemPo> query)
        {
            foreach (var key in rowStrategies.Keys)
            {
                switch (key)
                {
                    case "company":
                        if (!rowStrategies[key].Any(s => s == "@all"))
                        {
                            var strategList = rowStrategies[key].Select(s => Guid.Parse(s.ToLower())).ToList();
                            query = query.Where(z => z.CompanyId != null && strategList.Contains(z.CompanyId.Value));
                        }
                        break;
                    case "accountingDept":
                        if (!rowStrategies[key].Any(s => s == "@all"))
                        {
                            var strategList = rowStrategies[key].Select(s => s.ToLower()).ToList();
                            query = query.Where(z => z.BusinessDeptId != null && strategList.Contains(z.BusinessDeptId));
                        }
                        break;
                    default:
                        break;
                }
            }
            return query;
        }

        /// <summary>
        /// 根据公司id获取收款单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<SelectAssemblyOutput>> GetReceiveCodesByCompanyId(BulkPaymentQueryInput input)
        {
            return await (from dd in _db.DebtDetails
                          join d in _db.Debts on dd.DebtId equals d.Id
                          join rri in _db.RecognizeReceiveItems on dd.ReceiveCode equals rri.ReceiveCode
                          where dd.AccountPeriodType == 0 && !string.IsNullOrEmpty(dd.ReceiveCode) && dd.Status == DebtDetailStatusEnum.WaitExecute && d.CompanyId == input.CompanyId
                          select new SelectAssemblyOutput
                          {
                              id = dd.ReceiveCode,
                              name = string.Concat(dd.ReceiveCode, " | ", rri.ReceiveDate.ToString("yyyy-MM-dd hh:mm:ss")),
                          }).Distinct().ToListAsync();
        }

        /// <summary>
        /// 采购 提供接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<PaymentAutoDetailOfPurchaseOutput>> GetPaymentDetailsByCodes(PaymentAutoDetailOfPurchaseInput input)
        {
            var query = from d in _db.PaymentAutoDetails
                        join item in _db.PaymentAutoItems
                        on d.PaymentAutoItemId equals item.Id
                        join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                        from t in tempdetail.DefaultIfEmpty()
                        join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                        from e in tempdebt.DefaultIfEmpty()
                        join crt in _db.Credits on t.CreditId equals crt.Id into crtdebtdetailtemp
                        from crtdebtdetail in crtdebtdetailtemp.DefaultIfEmpty()
                        select new PaymentAutoDetailOfPurchaseOutput
                        {
                            AccountPeriodType = t.AccountPeriodType,
                            ServiceName = e.ServiceName,
                            AgentName = e.AgentName,
                            DebtBillCode = e.BillCode,
                            DebtAbatedValue = 0,//应付冲销金额——计算
                            DebtDetailValue = t.Value,
                            DebtValue = e.Value,
                            DebtBalance = e.Value - t.Value,
                            CoinName = e.CoinName,
                            Discount = t.CostDiscount,
                            OriginValue = t.OriginValue,
                            ProducerOrderNo = e.ProducerOrderNo,
                            PaymentCode = item.Code,
                            PurchaseCode = e.PurchaseCode,
                            BillDate = item.BillDate,
                            BusinessDeptFullName = item.BusinessDeptFullName,
                            CompanyName = item.CompanyName,
                            Status = item.Status,
                        };
            var result = await query.Where(p => input.Codes.Contains(p.PurchaseCode)).OrderBy(p => p.Status).ThenBy(p => p.AgentName).AsNoTracking().ToListAsync();
            //获取已冲销金额
            var debtCodes = result.Select(p => p.DebtBillCode).ToList();
            var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode))
                                     .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            foreach (var item in result)
            {
                item.DebtAbatedValue = abatments.Where(t => t.DebtBillCode == item.DebtBillCode || t.CreditBillCode == item.DebtBillCode).Sum(t => t.Value);
                item.DebtBalance = item.DebtValue - item.DebtAbatedValue;
            }
            return result;
        }
    }
}
