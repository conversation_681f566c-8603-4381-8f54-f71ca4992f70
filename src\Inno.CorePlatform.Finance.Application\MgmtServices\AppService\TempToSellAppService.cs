using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Npoi.Mapper;
using System.Text;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class TempToSellAppService : BaseAppService, ITempToSellAppService
    {
        public IBDSApiClient _bDSApiClient;
        public ISellApiClient _sellApiClient;
        private IInventoryApiClient _inventoryApiClient;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IBaseAllQueryService<DebtDetailExcutePo> _debtDetailExcuteQueryService;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly DaprClient _daprClient;
        private readonly FinanceDbContext _db;
        private readonly IEasyCachingProvider _easyCaching;
        public TempToSellAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogService,
            IUnitOfWork _unitOfWork,
            IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
            IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
            IBaseAllQueryService<DebtDetailExcutePo> debtDetailExcuteQueryService,
            IDebtDetailRepository debtDetailRepository,
            IKingdeeApiClient kingdeeApiClient,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            ISellApiClient sellApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            DaprClient daprClient,
            FinanceDbContext db,
            IEasyCachingProvider easyCaching,
            IAppServiceContextAccessor? contextAccessor) :
            base(creditItemRepository, debtRepository, subLogService, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._sellApiClient = sellApiClient;
            this._bDSApiClient = bDSApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._debtDetailQueryService = debtDetailQueryService;
            this._purchasePayPlanQueryService = purchasePayPlanQueryService;
            this._debtDetailExcuteQueryService = debtDetailExcuteQueryService;
            this._debtDetailRepository = debtDetailRepository;
            this._projectMgntApiClient = projectMgntApiClient;
            this._customizeInvoiceQueryService = customizeInvoiceQueryService;
            this._daprClient = daprClient;
            this._db = db;
            this._easyCaching = easyCaching;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            StringBuilder stringBuilder = new StringBuilder();
            try
            {
                var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.BusinessCode);
                if (saleOut == null || !saleOut.TempInventoryDetails.Any())
                {
                    throw new Exception("订阅暂存核销事件出错，原因：查询上游单据时未获取到相关数据");
                }
                var requestBody = JsonConvert.SerializeObject(input);
                var check = await base.IsCreatedCreditForBill(input.BusinessCode);
                if (check)
                {
                    //return BaseResponseData<int>.Failed(500, "该单据已生成过应收");
                    stringBuilder.AppendLine($"该单据已生成过应收,{input.BusinessCode},{DateTime.Now}");
                    throw new ApplicationException("该单据已生成过应收");
                }
                //如果来源是旺店通 Source = 160,需要根据消费者单价（ConsumerPrice）和积分红包单价（TmallCouponPrice）拆分成两条应收记录
                if (saleOut.Source == SaleSourceEnum.Wangdian)
                {
                    stringBuilder.AppendLine($"1.[旺店通-销售应收]开始处理消费者金额,{input.BusinessCode},{DateTime.Now}");
                    var consumerPriceSaleOut = CreditSaleInputDto.FromSaleOutputWithConsumerPrice(saleOut);
                    var ret1 = await CreateCreditForTempSale(consumerPriceSaleOut, input.BusinessSubType, requestBody, input.BusinessSubType);

                    stringBuilder.AppendLine($"2.[旺店通-销售应收]开始处理平台金额,{DateTime.Now}");
                    var platformCouponPriceSaleOut = CreditSaleInputDto.FromSaleOutputWithPlatformCouponPrice(saleOut);
                    if (platformCouponPriceSaleOut.IsNeedCreateReceivable)
                    {
                        await Task.Delay(9000);
                        var index = ret1.Data;
                        var ret2 = await CreateCreditForTempSale(platformCouponPriceSaleOut, input.BusinessSubType, requestBody, input.BusinessSubType, index);
                        stringBuilder.AppendLine($"2.1[旺店通-销售应收]红包生成应收完成,{DateTime.Now}");
                    }
                    else
                    {
                        stringBuilder.AppendLine($"2.1[旺店通-销售应收]红包金额为0，无需生成应收,{DateTime.Now}");
                    }
                    //发送个人原始订单号给集成中心，处理待开票明细内容 consumerPriceSaleOut.RelateCode
                    stringBuilder.AppendLine($"3.[旺店通-销售应收]生成平台金额成功,{DateTime.Now}");
                    var creditsaleInput = CreditSaleInvoiceToICInputDto.FromSaleOut(consumerPriceSaleOut);
                    await _daprClient.PublishEventAsync(
                   "pubsub-default",
                   "fam-ic-createdinvoicecredit", creditsaleInput
                   );
                    stringBuilder.AppendLine($"4.[旺店通-销售应收]发布事件成功,{creditsaleInput.ToJson()},{DateTime.Now}");


                    await _db.SaveChangesAsync();
                    await _subLogService.LogAsync("TempToSellAppService/PullIn", creditsaleInput.ToJson(), "推送集成平台-旺店通-销售应收-成功");
                    return ret1;
                }
                else
                {
                    var originalPriceSaleOut = CreditSaleInputDto.FromSaleOutputWithOriginalPrice(saleOut);
                    originalPriceSaleOut.PriceSourceType = saleOut.PriceSourceType;
                    var ret = await CreateCreditForTempSale(originalPriceSaleOut, input.BusinessSubType, requestBody, input.BusinessSubType);
                    return ret;
                }

            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine(ex.Message);
                await _subLogService.LogAsync("TempToSellAppService/PullIn", stringBuilder.ToString(), "推送集成平台-销售应收-失败");
                throw;// new Exception("订阅暂存核销事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }

        #region 暂存核销应收应付
        /// <summary>
        /// 应收
        /// </summary>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateCreditForTempSale(CreditSaleInputDto creditSaleInput, string classify, string preRequestBody, string subType, int index = 1)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            if (creditSaleInput != null)
            {

                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { creditSaleInput.CompanyId.ToString() }
                })).FirstOrDefault();
                if (companyInfo != null)
                {
                    var serviceIds = creditSaleInput.TempInventoryDetails.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
                    var services = new List<ServiceMetaOutput>();
                    if (serviceIds.Any())
                    {
                        services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                        {
                            ids = serviceIds.Select(p => p.ToString()).ToList()
                        });
                    }
                    var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                    var projectIds = creditSaleInput.TempInventoryDetails.Select(p => p.projectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    var productNameIds = creditSaleInput.TempInventoryDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                    var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                    var groupDetail = creditSaleInput.TempInventoryDetails.GroupBy(p => new { p.mark, p.businessUnitId, p.projectId });
                    #region 取追溯信息、经销货的应付信息
                    var traceInfos = new List<TraceCodeOutput>();
                    var traceCodes = creditSaleInput.TempInventoryDetails.Where(p => p.mark == 0).Select(p => p.traceCode).Distinct().ToList();
                    if (traceCodes.Any())
                    {
                        traceInfos = await _inventoryApiClient.QueryTraceInfoByCodes(traceCodes);
                    }
                    var debtsDetails = new List<DebtDetailPo>();
                    var purchasePayPlans = new List<PurchasePayPlanPo>();
                    if (traceInfos.Any())
                    {
                        var storeInItemCodes = traceInfos.Where(p => p.StoreInType == 1).Select(p => p.StoreInCode).Distinct().ToList();
                        debtsDetails = await _db.DebtDetails.Include(p => p.Debt)
                                                            .Where(p => p.Status == DebtDetailStatusEnum.WaitExecute &&
                                                                       ((p.AccountPeriodType == 0 && !p.CreditId.HasValue) || p.AccountPeriodType == 2) &&
                                                                        storeInItemCodes.ToHashSet().Contains(p.Debt.RelateCode) &&
                                                                        !p.ProbablyPayTime.HasValue).OrderBy(p => p.DebtId)
                                                                        .ThenBy(p => p.AccountPeriodType).Distinct().AsNoTracking().ToListAsync();
                        var purchaseCodes = traceInfos.Select(p => p.PurchaseOrderCode).Distinct().ToList();
                        purchasePayPlans = await _purchasePayPlanQueryService.GetAllListAsync(p => purchaseCodes.ToHashSet().Contains(p.PurchaseCode));
                    }
                    var debtDetailIds = debtsDetails.Select(p => p.Id).Distinct().ToList();
                    foreach (var debtDetail in debtsDetails)
                    {
                        string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                        var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                        if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                        {
                            return BaseResponseData<int>.Failed(500, $"应付单{debtDetail.Debt.BillCode}正在被拆分中,请稍后重试！");
                        }
                        await _easyCaching.SetAsync<string>(cachekey, debtDetail.Id.ToString(), TimeSpan.FromSeconds(120));
                    }
                    //如果来源是旺店通 Source = 160,需要根据消费者单价（ConsumerPrice）和积分红包单价（TmallCouponPrice）拆分成两条应收记录
                    #endregion
                    var kingdeeCredits = new List<KingdeeCredit>();
                    var Ids = new List<Guid>();
                    foreach (var g in groupDetail)
                    {
                        var thisProjectInfo = projectInfo.FirstOrDefault(p => p.Id == g.Key.projectId);
                        var credit = new CreditDto
                        {
                            PurchaseCost = creditSaleInput.CreditSaleSubType.HasValue && creditSaleInput.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform ? 0 : g.Sum(p => p.actualCost * p.quantity),
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            NameCode = companyInfo.nameCode,
                            AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                            Value = g.ToList().Sum(p => p.quantity * p.price),
                            BillCode = $"{creditSaleInput.BillCode}-{index.ToString().PadLeft(3, '0')}",
                            BillDate = billDate,//input.BillDate,
                            CreatedBy = creditSaleInput.CreatedBy ?? "none",
                            CreatedTime = DateTime.Now,
                            Mark = g.Key.mark,
                            CreditType = g.Key.mark == 0 || g.Key.mark == 3 ? CreditTypeEnum.selforder.ToString() : CreditTypeEnum.sale.ToString(),
                            CustomerId = creditSaleInput.CustomerId,
                            CustomerName = creditSaleInput.CustomerName,
                            Id = Guid.NewGuid(),
                            InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                            ServiceId = g.Key.businessUnitId,
                            RelateCode = creditSaleInput.BillCode,
                            IsSureIncome = 1,
                            IsSureIncomeDate = billDate,
                            OrderNo = creditSaleInput.BillCode,
                            ShipmentCode = creditSaleInput.Source == SaleSourceEnum.Spd ? creditSaleInput.RelateCode : "",
                            BusinessDeptFullName = creditSaleInput.businessDeptFullName,
                            BusinessDeptFullPath = creditSaleInput.businessDeptFullPath,
                            BusinessDeptId = creditSaleInput.businessDeptId.ToString(),
                            SaleSystemId = creditSaleInput.SaleSystemId,
                            SaleSystemName = creditSaleInput.SaleSystemName,
                            SaleSource = creditSaleInput.Source,
                            HospitalId = creditSaleInput.HospitalId,
                            HospitalName = creditSaleInput.HospitalName,
                            SaleType = creditSaleInput.SaleType,
                            Note = creditSaleInput.Description,
                            ProjectName = thisProjectInfo?.Name,
                            ProjectId = g.Key.projectId,
                            ProjectCode = thisProjectInfo?.Code,
                            CustomerOrderCode = creditSaleInput.CustomerOrderCode,
                            CustomerPersonName = creditSaleInput.CustomerPersonName,
                            SunPurchaseRelatecode = creditSaleInput.SunPurchaseRelatecode,
                            AgentName = string.Join(",", g.Select(p => p.agentName).Distinct().ToList()),
                            ProducerName = string.Join(",", g.Select(p => p.producerName).Distinct().ToList()),
                            CreditSaleSubType = creditSaleInput.CreditSaleSubType,
                            PriceSource = creditSaleInput.PriceSourceType == PriceSourceEnum.OPERATION_APPLY ? null : creditSaleInput.PriceSourceType,
                            //IsInternalTransactions = creditSaleInput.Source == SaleSourceEnum.GroupDistribution ? true : false
                        };
                        credit.DeptName = creditSaleInput.DeptName;
                        if (creditSaleInput.TempInventoryDetails != null && creditSaleInput.TempInventoryDetails.Any())
                        {
                            credit.DeptName = string.Join(",", creditSaleInput.TempInventoryDetails.Select(p => p.deptName).Distinct());
                        }
                        credit.OriginOrderNo = creditSaleInput.RelateCode;

                        Ids.Add(credit.Id);
                        if (g.Key.businessUnitId.HasValue)
                        {
                            credit.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.ToString().ToLower())?.name;
                        }
                        var amount = 0m;//不含税总额
                        var jfzx_alltotalcost = 0m;
                        #region 包装金蝶应收参数
                        var kingdeeCredit = new KingdeeCredit()
                        {
                            asstact_number1 = creditSaleInput.CustomerId,
                            billno = credit.BillCode,
                            billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                            bizdate = credit.BillDate.Value,
                            org_number = credit.NameCode,
                            jfzx_businessnumber = creditSaleInput.businessDeptId.ToString(),
                            jfzx_ordernumber = creditSaleInput.BillCode,
                            jfzx_iscofirm = true,
                            jfzx_creator = credit.CreatedBy ?? "none",
                            jfzx_serviceid = credit.ServiceName,
                        };
                        if (subType == "跟台核销")
                        {
                            if (g.Key.mark == 0 || g.Key.mark == 3)
                            {
                                kingdeeCredit.billtype_number = "ar_finarbill_BT16";
                            }
                            else
                            {
                                kingdeeCredit.billtype_number = "ar_finarbill_BT12";
                            }
                        }
                        kingdeeCredit.jfzx_rebate = creditSaleInput.RebateType.HasValue;
                        var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                        if (g.Where(p => !p.salesTaxRate.HasValue).Count() > 0)
                        {
                            return BaseResponseData<int>.Failed(500, $"销售明细中salesTaxRate销售税率为空");
                        }
                        g.ToList().GroupBy(a => new { a.productId, a.price, a.projectId, a.salesTaxRate, a.agentId, a.actualCost, a.standardUnitCost, a.taxRate }).ForEach(b =>
                        {
                            var d = new KingdeeCreditDetail();
                            d.e_taxunitprice = b.Key.price;
                            d.e_unitprice = d.e_taxunitprice / (1 + b.Key.salesTaxRate.Value / 100.00M);
                            d.e_quantity = b.Sum(c => c.quantity);
                            d.salestaxrate = b.Key.salesTaxRate.Value;
                            var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == b.First().productNameId);
                            if (thisProductInfo.classificationNewGuid.HasValue)
                            {
                                d.e_material_number1 = thisProductInfo.classificationNewGuid.ToString();
                            }
                            else
                            {
                                d.e_material_number1 = thisProductInfo.classificationGuid.ToString();
                            }
                            d.jfzx_outbound_type = "B";
                            var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.Key.projectId);
                            d.jfzx_projectnumber = thisProject?.Code;
                            //d.jfzx_unitcost = b.Key.unitCost;
                            // 成本相关字段统一处理：如果应收子类型为平台，成本相关字段设置为 0
                            var isPlatformType = creditSaleInput.CreditSaleSubType.HasValue && creditSaleInput.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform;
                            var unitCostExcludingTax = isPlatformType ? 0 : Math.Round(b.Key.actualCost.Value / (1 + b.Key.taxRate.Value / 100.00M), 2);

                            d.jfzx_unitcost = unitCostExcludingTax;
                            d.jfzx_supplier = b.Key.agentId.ToString().ToUpper();
                            if (g.Key.mark != 0 && g.Key.mark != 3)//寄售货需要将标准成本传入金蝶
                            {
                                if (b.Key.standardUnitCost.HasValue)
                                {
                                    d.jfzx_standardtotal = b.Key.standardUnitCost.Value;
                                }
                                else
                                {
                                    throw new Exception("销售明细mark=1,但是standardUnitCost 为空");
                                }
                            }
                            if (creditSaleInput.RebateType.HasValue)
                            {
                                d.jfzx_rebateType = (int)creditSaleInput.RebateType;
                            }
                            kingdeeCreditDetails.Add(d);

                            ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                            //明细总成本：如果是平台类型，总成本也为 0
                            d.jfzx_totalcostMany = isPlatformType ? 0 : (b.Key.actualCost / (1 + b.Key.taxRate / 100.00M)) * d.e_quantity;
                            //d.jfzx_totalcostMany = Math.Round(d.jfzx_totalcostMany ?? 0, 2);
                            //计算总税额 
                            jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                            amount += d.e_unitprice * d.e_quantity;
                        });

                        //应收不含税总额
                        kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                        //应收不含税总额
                        kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                        //总成本
                        kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                        kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                        kingdeeCredits.Add(kingdeeCredit);
                        #endregion
                        var insertRes = await base.CreateCredit(credit);
                        if (insertRes.Code == CodeStatusEnum.Success)
                        {
                            if (creditSaleInput.NeedDebt)
                            {
                                #region 经销货处理应付明细的拆分
                                var thisDetails = g.Where(p => p.mark == 0 || p.mark == 3).ToList();
                                var updateDebtDetailList = new List<DebtDetailUpdateDto>();
                                if (thisDetails.Any())
                                {
                                    foreach (var d in thisDetails)
                                    {
                                        var thisTrance = traceInfos.FirstOrDefault(q => q.TraceCode == d.traceCode);
                                        if (thisTrance != null)
                                        {
                                            //分别找出销售账期和回款账期的付款计划
                                            var thisPlanForSales = purchasePayPlans.Where(q => q.AccountPeriodType == AccountPeriodTypeEnum.Sale &&
                                                                                               q.PurchaseCode == thisTrance.PurchaseOrderCode &&
                                                                                               q.ProductId == thisTrance.ProductId).ToList();
                                            var thisPlanForRepayments = purchasePayPlans.Where(q => q.AccountPeriodType == AccountPeriodTypeEnum.Repayment &&
                                                                                                    q.PurchaseCode == thisTrance.PurchaseOrderCode &&
                                                                                                    q.ProductId == thisTrance.ProductId).ToList();
                                            if (thisPlanForSales != null && thisPlanForSales.Count > 0)
                                            {
                                                int startIndex = 0;
                                                foreach (var thisPlanForSale in thisPlanForSales)
                                                {
                                                    var thisDebtDetailForSale = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForSale.PurchaseCode &&
                                                                                                                !q.ProbablyPayTime.HasValue && q.AccountPeriodType == 2 &&
                                                                                                                q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                                q.AccountPeriodDays == thisPlanForSale.AccountPeriodDays);
                                                    if (thisPlanForSale.AccountPeriodDays == 0)
                                                    {
                                                        thisDebtDetailForSale = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForSale.PurchaseCode &&
                                                                                                                !q.ProbablyPayTime.HasValue && q.AccountPeriodType == 2 &&
                                                                                                                q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                                (q.AccountPeriodDays == 0 || q.AccountPeriodDays == null));
                                                    }

                                                    if (thisDebtDetailForSale != null)//销售账期的付款计划拆分
                                                    {
                                                        if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForSale.Id && p.Type == 1))
                                                        {
                                                            updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                            {
                                                                Id = thisDebtDetailForSale.Id,
                                                                Value = thisDebtDetailForSale.Value,
                                                                Type = 1,
                                                                AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForSale.AccountPeriodType,
                                                                AccountPeriodDays = thisDebtDetailForSale.AccountPeriodDays,
                                                                Code = thisDebtDetailForSale.Code,
                                                                DebtId = thisDebtDetailForSale.DebtId,
                                                                ReceiveCode = thisDebtDetailForSale.ReceiveCode,
                                                                RecognizeReceiveCode = thisDebtDetailForSale.RecognizeReceiveCode,
                                                                Status = (DebtDetailStatusEnum)thisDebtDetailForSale.Status,
                                                                PurchaseCode = thisDebtDetailForSale.PurchaseCode,
                                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",

                                                            });
                                                        }
                                                        if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForSale.Id && p.Type == 2 && p.DetailId == d.id && p.DebtId == thisDebtDetailForSale.DebtId) || startIndex == 0)
                                                        {
                                                            updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                            {
                                                                Id = thisDebtDetailForSale.Id,
                                                                //Value = Math.Round(d.quantity * thisPlanForSale.RatioPrice, 2),
                                                                Value = d.quantity * thisTrance.unitCost * thisPlanForSale.Ratio,
                                                                DetailId = d.id,
                                                                Type = 2,
                                                                OriginValue = thisDebtDetailForSale.OriginValue,
                                                                AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForSale.AccountPeriodType,
                                                                AccountPeriodDays = thisDebtDetailForSale.AccountPeriodDays,
                                                                Code = thisDebtDetailForSale.Code,
                                                                CreditId = credit.Id,
                                                                DebtId = thisDebtDetailForSale.DebtId,
                                                                Discount = thisDebtDetailForSale.Discount,
                                                                ReceiveCode = thisDebtDetailForSale.ReceiveCode,
                                                                RecognizeReceiveCode = thisDebtDetailForSale.RecognizeReceiveCode,
                                                                Status = (DebtDetailStatusEnum)thisDebtDetailForSale.Status,
                                                                PurchaseCode = thisDebtDetailForSale.PurchaseCode,
                                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                                                OrderNo = creditSaleInput.BillCode,
                                                                ProbablyPayTime = DateTime.Now.Date.AddDays(thisPlanForSale.AccountPeriodDays.HasValue ? thisPlanForSale.AccountPeriodDays.Value : 0)
                                                            });
                                                        }
                                                    }
                                                    startIndex++;
                                                }
                                            }
                                            if (thisPlanForRepayments != null && thisPlanForRepayments.Count > 0)
                                            {
                                                int startIndex = 0;
                                                foreach (var thisPlanForRepayment in thisPlanForRepayments)
                                                {
                                                    var thisDebtDetailForRepayments = debtsDetails.Where(q => q.PurchaseCode == thisPlanForRepayment.PurchaseCode &&
                                                                                                              q.AccountPeriodType == 0 &&
                                                                                                              q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                              q.AccountPeriodDays == thisPlanForRepayment.AccountPeriodDays).ToList();
                                                    var thisDebtDetailForRepayment = thisDebtDetailForRepayments.FirstOrDefault();
                                                    if (thisPlanForRepayment.AccountPeriodDays == 0)
                                                    {
                                                        thisDebtDetailForRepayment = debtsDetails.FirstOrDefault(q => q.PurchaseCode == thisPlanForRepayment.PurchaseCode &&
                                                                                                                      q.AccountPeriodType == 0 &&
                                                                                                                      q.Debt.RelateCode == thisTrance.StoreInCode &&
                                                                                                                     (q.AccountPeriodDays == 0 || q.AccountPeriodDays == null));
                                                    }

                                                    if (thisDebtDetailForRepayment != null)//回款账期的付款计划拆分
                                                    {
                                                        if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForRepayment.Id && p.Type == 1))
                                                        {
                                                            updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                            {
                                                                Id = thisDebtDetailForRepayment.Id,
                                                                Value = thisDebtDetailForRepayment.Value,
                                                                Type = 1,
                                                                OriginValue = thisDebtDetailForRepayment.OriginValue,
                                                                AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForRepayment.AccountPeriodType,
                                                                AccountPeriodDays = thisDebtDetailForRepayment.AccountPeriodDays,
                                                                Code = thisDebtDetailForRepayment.Code,
                                                                DebtId = thisDebtDetailForRepayment.DebtId,
                                                                Discount = thisDebtDetailForRepayment.Discount,
                                                                ReceiveCode = thisDebtDetailForRepayment.ReceiveCode,
                                                                RecognizeReceiveCode = thisDebtDetailForRepayment.RecognizeReceiveCode,
                                                                Status = thisDebtDetailForRepayment.Status,
                                                                PurchaseCode = thisDebtDetailForRepayment.PurchaseCode,
                                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                                            });
                                                        }
                                                        if (!updateDebtDetailList.Any(p => p.Id == thisDebtDetailForRepayment.Id && p.Type == 2 && p.DetailId == d.id && p.DebtId == thisDebtDetailForRepayment.DebtId) || startIndex == 0)
                                                        {
                                                            updateDebtDetailList.Add(new DebtDetailUpdateDto()
                                                            {
                                                                Id = thisDebtDetailForRepayment.Id,
                                                                //Value = Math.Round(d.quantity * thisPlanForRepayment.RatioPrice, 2),
                                                                Value = d.quantity * thisTrance.unitCost * thisPlanForRepayment.Ratio,
                                                                Type = 2,
                                                                DetailId = d.id,
                                                                OriginValue = thisDebtDetailForRepayment.OriginValue,
                                                                AccountPeriodType = (AccountPeriodTypeEnum)thisDebtDetailForRepayment.AccountPeriodType,
                                                                AccountPeriodDays = thisDebtDetailForRepayment.AccountPeriodDays,
                                                                Code = thisDebtDetailForRepayment.Code,
                                                                CreditId = credit.Id,
                                                                DebtId = thisDebtDetailForRepayment.DebtId,
                                                                Discount = thisDebtDetailForRepayment.Discount,
                                                                ReceiveCode = thisDebtDetailForRepayment.ReceiveCode,
                                                                RecognizeReceiveCode = thisDebtDetailForRepayment.RecognizeReceiveCode,
                                                                Status = thisDebtDetailForRepayment.Status,
                                                                PurchaseCode = thisDebtDetailForRepayment.PurchaseCode,
                                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                                                OrderNo = creditSaleInput.BillCode,
                                                                ProbablyPayTime = null //DateTime.Now.Date.AddDays(thisPlanForRepayment.AccountPeriodDays.HasValue ? thisPlanForRepayment.AccountPeriodDays.Value : 0)
                                                            });
                                                        }
                                                    }
                                                    startIndex++;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (updateDebtDetailList.Any())
                                {
                                    var toUpdateList = new List<DebtDetailUpdateDto>();
                                    var toInsertList = new List<DebtDetailUpdateDto>();
                                    var ugroup = updateDebtDetailList.GroupBy(p => new { p.DebtId, p.AccountPeriodDays });
                                    ugroup.ForEach(u =>
                                    {
                                        var saleForUpdateAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale && p.Type == 1).Sum(t => t.Value);
                                        var saleForInsertAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale && p.Type == 2).Sum(t => t.Value);
                                        var RepaymentForUpdateAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment && p.Type == 1).Sum(t => t.Value);
                                        var RepaymentForInsertAmount = u.ToList().Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment && p.Type == 2).Sum(t => t.Value);


                                        var waitforupdateList = u.ToList().Where(p => p.Type == 1).ToList();
                                        var waitforInsertList = u.ToList().Where(p => p.Type == 2).ToList();
                                        waitforupdateList.ForEach(p =>
                                        {
                                            var splitAmount = waitforInsertList.Where(t => t.Id == p.Id).Sum(q => q.Value);
                                            p.Value -= splitAmount;
                                            p.UpdatedBy = creditSaleInput.CreatedBy;
                                        });
                                        var insertList = waitforInsertList.GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                        {
                                            Id = Guid.NewGuid(),
                                            Value = p.Sum(t => t.Value),
                                            Type = 2,
                                            AccountPeriodType = p.Key.AccountPeriodType,
                                            AccountPeriodDays = p.Key.AccountPeriodDays,
                                            Code = p.First().Code,
                                            CreditId = credit.Id,
                                            DebtId = p.First().DebtId,
                                            Discount = p.First().Discount,
                                            ReceiveCode = p.First().ReceiveCode,
                                            RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                            ProbablyPayTime = p.First().ProbablyPayTime,
                                            Status = p.First().Status,
                                            CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                            PurchaseCode = p.First().PurchaseCode,
                                            OrderNo = creditSaleInput.BillCode,
                                        }).ToList();


                                        var sale = waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale).FirstOrDefault();
                                        if (sale != null && sale.Value <= 0)
                                        {
                                            var insert = u.ToList().FirstOrDefault(p => p.Type == 2 && p.AccountPeriodType == AccountPeriodTypeEnum.Sale);
                                            sale.Value = saleForUpdateAmount;
                                            sale.CreditId = insert.CreditId;
                                            sale.ProbablyPayTime = insert.ProbablyPayTime;
                                            sale.OrderNo = creditSaleInput.BillCode;
                                            toUpdateList.Add(sale);
                                        }
                                        else
                                        {
                                            toUpdateList.AddRange(waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale));
                                            var list = waitforInsertList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Sale).GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                            {
                                                Id = Guid.NewGuid(),
                                                Value = p.Sum(t => t.Value),
                                                Type = 2,
                                                AccountPeriodType = p.Key.AccountPeriodType,
                                                AccountPeriodDays = p.Key.AccountPeriodDays,
                                                Code = p.First().Code,
                                                CreditId = credit.Id,
                                                DebtId = p.First().DebtId,
                                                Discount = p.First().Discount,
                                                ReceiveCode = p.First().ReceiveCode,
                                                RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                                ProbablyPayTime = p.First().ProbablyPayTime,
                                                Status = p.First().Status,
                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                                PurchaseCode = p.First().PurchaseCode,
                                                OrderNo = creditSaleInput.BillCode,
                                            }).ToList();
                                            toInsertList.AddRange(list);
                                        }
                                        var repay = waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment).FirstOrDefault();
                                        if (repay != null && repay.Value <= 0)
                                        {
                                            var insert = u.ToList().FirstOrDefault(p => p.Type == 2 && p.AccountPeriodType == AccountPeriodTypeEnum.Repayment);
                                            repay.Value = RepaymentForUpdateAmount;
                                            repay.CreditId = insert.CreditId;
                                            repay.ProbablyPayTime = insert.ProbablyPayTime;
                                            repay.OrderNo = creditSaleInput.BillCode;
                                            toUpdateList.Add(repay);
                                        }
                                        else
                                        {
                                            toUpdateList.AddRange(waitforupdateList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment));
                                            var list = waitforInsertList.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.Repayment).GroupBy(p => new { p.Id, p.AccountPeriodType, p.AccountPeriodDays }).Select(p => new DebtDetailUpdateDto
                                            {
                                                Id = Guid.NewGuid(),
                                                Value = p.Sum(t => t.Value),
                                                Type = 2,
                                                AccountPeriodType = p.Key.AccountPeriodType,
                                                AccountPeriodDays = p.Key.AccountPeriodDays,
                                                Code = p.First().Code,
                                                CreditId = credit.Id,
                                                DebtId = p.First().DebtId,
                                                Discount = p.First().Discount,
                                                ReceiveCode = p.First().ReceiveCode,
                                                RecognizeReceiveCode = p.First().RecognizeReceiveCode,
                                                ProbablyPayTime = p.First().ProbablyPayTime,
                                                Status = p.First().Status,
                                                CreatedBy = creditSaleInput.CreatedBy ?? "none",
                                                PurchaseCode = p.First().PurchaseCode,
                                                OrderNo = creditSaleInput.BillCode,
                                            }).ToList();
                                            toInsertList.AddRange(list);
                                        }
                                    });

                                    var toUpdateList1 = toUpdateList.Adapt<List<DebtDetail>>();
                                    var toInsertLis1 = toInsertList.Adapt<List<DebtDetail>>();
                                    if (toUpdateList1.Any())
                                    {
                                        toUpdateList1 = toUpdateList1.Where(p => p.Value != 0).Distinct().ToList();
                                        await _debtDetailRepository.UpdateManyAsync(toUpdateList1);
                                    }
                                    if (toInsertLis1.Any())
                                    {
                                        toInsertLis1 = toInsertLis1.Where(p => p.Value != 0).Distinct().ToList();
                                        await _debtDetailRepository.AddManyAsync(toInsertLis1);
                                    }
                                }
                                #endregion
                            }
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, $"生成应收失败:{insertRes?.ToJson()}");
                        }
                        index++;
                    }
                    var kingdeeRes = await _kingdeeApiClient.PushCreditsToKingdee(kingdeeCredits, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await _unitOfWork.CommitAsync();
                        var debtIds = debtsDetails.Select(p => p.DebtId.Value).Distinct().ToList();
                        await _debtRepository.RepaireDebtDiff(debtIds);
                        var (list, count) = await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                        {
                            CreditBillCodes = kingdeeCredits.Select(p => p.billno).ToList(),
                            RelateCodes = kingdeeCredits.Select(p => p.jfzx_ordernumber).ToList(),
                        });
                        foreach (var debtDetail in debtsDetails)
                        {
                            string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                            _easyCaching.Remove(cachekey);
                        }
                    }
                    else
                    {
                        foreach (var debtDetail in debtsDetails)
                        {
                            string cachekey = $"SplitDebtDetail_{debtDetail.Id}";
                            _easyCaching.Remove(cachekey);
                        }
                        return BaseResponseData<int>.Failed(500, "生成应收到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, "未找到对应的公司");
                }
            }
            ret.Data = index;
            return ret;

        }
        #endregion


        public async Task<BaseResponseData<List<KingdeeCredit>>> GetKingdeeTempSaleCreditParams(EventBusDTO dto)
        {
            var input = await _sellApiClient.GetTempSaleByCodeAsync(dto.BusinessCode);
            if (input == null || !input.TempInventoryDetails.Any())
            {
                return BaseResponseData<List<KingdeeCredit>>.Failed(500, "查询上游单据时未获取到相关数据");
            }
            var projectIds = input.TempInventoryDetails.Select(p => p.projectId.Value).Distinct().ToList();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            var productNameIds = input.TempInventoryDetails.Select(p => p.productNameId.Value).Distinct().ToList();
            var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
            var groupDetail = input.TempInventoryDetails.GroupBy(p => new { p.mark, p.businessUnitId, p.projectId });
            var index = 1;
            var kingdeeCredits = new List<KingdeeCredit>();
            foreach (var g in groupDetail)
            {
                var thisProjectInfo = projectInfo.FirstOrDefault(p => p.Id == g.Key.projectId);
                var credit = new CreditDto
                {
                    //CompanyId = Guid.Parse(companyInfo.companyId),
                    //CompanyName = companyInfo.companyName,
                    //NameCode = companyInfo.nameCode,
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    Value = g.ToList().Sum(p => p.quantity * p.price),
                    BillCode = $"{input.BillCode}-{index.ToString().PadLeft(3, '0')}",
                    BillDate = input.BillDate,
                    CreatedBy = input.CreatedBy ?? "none",
                    CreatedTime = DateTime.Now,
                    Mark = g.Key.mark,
                    CreditType = g.Key.mark == 0|| g.Key.mark == 3 ? CreditTypeEnum.selforder.ToString() : CreditTypeEnum.sale.ToString(),
                    CustomerId = input.CustomerId,
                    CustomerName = input.CustomerName,
                    Id = Guid.NewGuid(),
                    InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                    ServiceId = g.Key.businessUnitId,
                    RelateCode = input.BillCode,
                    IsSureIncome = 1,
                    //IsSureIncomeDate = DateTime.Now,
                    OrderNo = input.BillCode,
                    ShipmentCode = input.Source == SaleSourceEnum.Spd ? input.RelateCode : "",
                    BusinessDeptFullName = input.businessDeptFullName,
                    BusinessDeptFullPath = input.businessDeptFullPath,
                    BusinessDeptId = input.businessDeptId.ToString(),
                    SaleSystemId = input.SaleSystemId,
                    SaleSystemName = input.SaleSystemName,
                    SaleSource = input.Source,
                    HospitalId = input.HospitalId,
                    HospitalName = input.HospitalName,
                    SaleType = input.SaleType,
                    Note = input.Description,
                    ProjectName = thisProjectInfo?.Name,
                    ProjectId = g.Key.projectId,
                    ProjectCode = thisProjectInfo?.Code,
                    CustomerOrderCode = input.CustomerOrderCode,
                    CustomerPersonName = input.CustomerPersonName,
                    SunPurchaseRelatecode = input.SunPurchaseRelatecode,
                    AgentName = string.Join(",", g.Select(p => p.agentName).Distinct().ToList()),
                    ProducerName = string.Join(",", g.Select(p => p.producerName).Distinct().ToList()),
                };
                credit.IsSureIncomeDate = credit.BillDate;
                credit.DeptName = input.DeptName;
                if (input.TempInventoryDetails != null && input.TempInventoryDetails.Any())
                {
                    credit.DeptName = string.Join(",", input.TempInventoryDetails.Select(p => p.deptName).Distinct());
                }
                //if (!string.IsNullOrEmpty(input.RelateCode) && (input.RelateCode.Contains("-PSA-") || input.SaleType == SaleTypeEnum.SaleForB)) //原始订单号
                {
                    credit.OriginOrderNo = input.RelateCode;
                }
                var amount = 0m;//不含税总额
                var jfzx_alltotalcost = 0m;
                #region 包装金蝶应收参数
                var kingdeeCredit = new KingdeeCredit()
                {
                    asstact_number1 = input.CustomerId,
                    billno = credit.BillCode,
                    billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                    bizdate = credit.BillDate.Value,
                    org_number = credit.NameCode,
                    jfzx_businessnumber = input.businessDeptId.ToString(),
                    jfzx_ordernumber = input.BillCode,
                    jfzx_iscofirm = true,
                    jfzx_creator = credit.CreatedBy ?? "none",
                    jfzx_serviceid = credit.ServiceName,
                };

                kingdeeCredit.jfzx_rebate = input.RebateType.HasValue;
                var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                g.ToList().GroupBy(a => new { a.productId, a.price, a.projectId, a.salesTaxRate, a.agentId, a.actualCost, a.standardUnitCost, a.taxRate }).ForEach(b =>
                {
                    var d = new KingdeeCreditDetail();
                    d.e_taxunitprice = b.Key.price;
                    d.e_unitprice = d.e_taxunitprice / (1 + b.Key.salesTaxRate.Value / 100.00M);
                    d.e_quantity = b.Sum(c => c.quantity);
                    d.salestaxrate = b.Key.salesTaxRate.Value;
                    var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == b.First().productNameId);
                    if (thisProductInfo.classificationNewGuid.HasValue)
                    {
                        d.e_material_number1 = thisProductInfo.classificationNewGuid.ToString();
                    }
                    else
                    {
                        d.e_material_number1 = thisProductInfo.classificationGuid.ToString();
                    }
                    d.jfzx_outbound_type = "B";
                    var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.Key.projectId);
                    d.jfzx_projectnumber = thisProject?.Code;
                    //d.jfzx_unitcost = b.Key.unitCost;
                    // 成本相关字段统一处理：如果应收子类型为平台，成本相关字段设置为 0
                    var isPlatformType = credit.CreditSaleSubType.HasValue && credit.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform;
                    var unitCostExcludingTax = isPlatformType ? 0 : Math.Round(b.Key.actualCost.Value / (1 + b.Key.taxRate.Value / 100.00M), 2);

                    d.jfzx_unitcost = unitCostExcludingTax;
                    d.jfzx_supplier = b.Key.agentId.ToString().ToUpper();
                    if (g.Key.mark != 0 && g.Key.mark != 3)//寄售货需要将标准成本传入金蝶
                    {
                        if (b.Key.standardUnitCost.HasValue)
                        {
                            d.jfzx_standardtotal = b.Key.standardUnitCost.Value;
                        }
                        else
                        {
                            throw new Exception("销售明细mark=1,但是standardUnitCost 为空");
                        }
                    }
                    if (input.RebateType.HasValue)
                    {
                        d.jfzx_rebateType = (int)input.RebateType;
                    }
                    kingdeeCreditDetails.Add(d);

                    ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                    //明细总成本：如果是平台类型，总成本也为 0
                    d.jfzx_totalcostMany = isPlatformType ? 0 : (b.Key.actualCost / (1 + b.Key.taxRate / 100.00M)) * d.e_quantity;
                    d.jfzx_totalcostMany = Math.Round(d.jfzx_totalcostMany ?? 0, 2);
                    //计算总税额 
                    jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                    amount += d.e_unitprice * d.e_quantity;
                });

                //应收不含税总额
                kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                //应收不含税总额
                kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                //总成本
                kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                kingdeeCredits.Add(kingdeeCredit);
                #endregion
                index++;
            }
            return new BaseResponseData<List<KingdeeCredit>>()
            {
                Code = CodeStatusEnum.Success,
                Message = "成功",
                Data = kingdeeCredits
            };
        }
    }
}
