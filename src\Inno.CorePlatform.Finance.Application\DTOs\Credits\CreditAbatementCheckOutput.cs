using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Application.DTOs.Credits
{
    /// <summary>
    /// 应收冲销状态检查输出结果
    /// </summary>
    public class CreditAbatementCheckOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedAmount { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
    }
}
