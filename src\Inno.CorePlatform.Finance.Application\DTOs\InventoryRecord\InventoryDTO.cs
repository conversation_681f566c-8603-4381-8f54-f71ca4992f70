﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;

namespace Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord
{
    public class InventoryDTO : InventoryItem
    {
        public string StatusName
        {
            get
            {
                if (Status == 0)
                    return "待库存盘点";
                if (Status == 1)
                    return "库存盘点中";
                if (Status == 2)
                    return "库存盘点完成";
                return "已完成";
            }
        }
    }

    public class InventoryQueryDto : BaseQueryInput
    {
        public string SysMonth { get; set; } = "";
        public Guid? CompanyId { get; set; }
        public int? Status { get; set; }
        public Guid UserId { get; set; }
    }
    public class RecordBaseQueryDto : BaseQuery
    {
        /// <summary>
        /// 订货系统前端传回（机构客户id）
        /// </summary>
        public Guid? customerId { get; set; }

        /// <summary>
        /// 订货系统前端传回（客户id）
        /// </summary>
        public List<Guid?>? customerIds { get; set; }

        /// <summary>
        /// 订货系统前端传回（销售子系统id）
        /// </summary>
        public Guid? saleSystemId { get; set; }

        /// <summary>
        /// 订货系统前端传回（销售子系统id集合）
        /// </summary>
        public List<Guid>? saleSystemIds { get; set; }

        public Guid? companyId { get; set; }
        public string? companyName { get; set; }
        public string? code { get; set; }
        public string? billDateBeging { get; set; }
        public string? billDateEnd { get; set; }
        public Guid? UserId { get; set; }
        public string? CurrentUserName { get; set; }

        /// <summary>
        /// 项目code
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 盘点类型
        /// </summary>
        public CreditRecordItemClassifyEnum? Classify { get; set; }
    }
    public class DetailRecordBaseQueryDto : BaseQuery
    {
        public List<Guid>? ids { get; set; }
        /// <summary>
        /// 订货系统前端传回（机构客户id）
        /// </summary>
        public Guid? customerId { get; set; }
        /// <summary>
        /// 订货系统前端传回（客户id）
        /// </summary>
        public List<Guid?>? customerIds { get; set; }
        public Guid UserId { get; set; }
        /// <summary>
        /// 是否过滤公司
        /// </summary>
        public bool? IsFilterCompany { get; set; }
        public Guid ItemId { get; set; }

    }
    public class CreditRecordDetailsOutputDto : CreditRecordDetailPo
    {
        public string? InvoiceNo { get; set; }
        public decimal? InvoiceAmount { get; set; }
        public decimal? Amount { get; set; }
        public DateTime? InvoiceTime { get; set; }
    }
    public class CreditRecordOutputDto : CreditRecordItemPo
    {
        /// <summary>
        /// 业务日期
        /// </summary>
        public string? BillDateStr
        {
            get
            {
                return BillDate.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 是否确认
        /// </summary>
        public string IsConfirmStr
        {
            get
            {
                return IsConfirm.HasValue && IsConfirm.Value == 1 ? "已确认" : "未确认";
            }
        }
    }
    public class DebtRecordOutputDto : DebtRecordItemPo
    {
    }

    /// <summary>
    /// 应付盘点导出
    /// </summary>
    public class DebtDownLoadOutput
    {
        /// <summary>
        /// 盘点单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal Vaule { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedVaule { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance
        {
            get; set;
        }
        public DateTime? DebtBillDate { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string? ProjectCode { get; set; }
    }

    /// <summary>
    /// 订货系统应付盘点导出
    /// </summary>
    public class DebtDownLoadDhOutput
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public string? BillDateStr { get; set; }

        /// <summary>
        /// 是否确认
        /// </summary>
        public string? IsConfirmStr { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 单据类型
        /// </summary>
        public string? Typename { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal? AbatedValue { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal? Balance { get; set; }
    }

    public class PaymentRecordOutputDto : PaymentRecordItemPo
    {
    }
    public class CreditRecordDetailDto : CreditRecordDetailPo
    {
        public string typename
        {
            get
            {
                return Credit.CreditType.GetDescription();
            }
        }

        /// <summary>
        /// 确认收入金额
        /// </summary>
        public decimal? SureIncomeAmount { get; set; } = 0;

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; } = 0;
    }
    public class DebtRecordDetailDto : DebtRecordDetailPo
    {
        public string typename
        {
            get
            {
                return Debt.DebtType.GetDescription();
            }
        }
    }
    public class PaymentRecordDetailDto : PaymentRecordDetailPo
    {
        public string typename
        {
            get
            {
                return Payment.Type.GetDescription();
            }
        }
    }

    /// <summary>
    /// 应收盘点导出
    /// </summary>
    public class CreditDownLoadOutput
    {
        public Guid Id { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 应收单日期
        /// </summary>
        public DateTime CreditBillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal Vaule { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedVaule { get; set; }

        /// <summary>
        /// 确认收入金额
        /// </summary>
        public decimal SureIncomeAmount { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 核算部门名称
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 损失确认金额
        /// </summary>
        public decimal? LossValue { get; set; }

        /// <summary>
        /// 未冲销金额
        /// </summary>
        public decimal UnAbatedAmount { get; set; }

        /// <summary>
        /// 未确认收入金额
        /// </summary>
        public decimal UnSureIncomeAmount { get; set; }

        /// <summary>
        /// 未开票金额
        /// </summary>
        public decimal UnInvoiceAmount { get; set; }
    }

    /// <summary>
    /// 付款盘点导出
    /// </summary>
    public class PaymentDownLoadOutput
    {
        /// <summary>
        /// 盘点单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PayCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Vaule { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedVaule { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance
        {
            get; set;
        }
        public DateTime PaymentBillDate { get; set; }
    }
    public class StoreInventoryUpdateInputDto
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 盘点单号
        /// </summary>
        public string StoreCheckCode { get; set; }

        /// <summary>
        /// 是否完成实盘，true=完成，false=未实盘
        /// </summary>
        public bool StoreCheckStatus { get; set; }
        /// <summary>
        /// 盘点单对应库房id
        /// </summary>
        public Guid StoreHouseId { get; set; }
        /// <summary>
        /// 该公司总计库房数
        /// </summary>
        public int Total { get; set; }
    }

    public class NoFinishBillDto
    {
        /// <summary>
        ///单号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public string BillDate { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string Operator { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string StatusName { get; set; }
    }

    /// <summary>
    /// 公司的盘点情况
    /// </summary>
    public class InventoryInfoForCompanyDto
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 总盘点状态,-1表示没有正在盘点的总盘点单，0-待库存盘点，1-库存盘点中，2-库存盘点完成，99-已完成
        /// </summary>
        public int InventoryStatus { get; set; }

        /// <summary>
        /// 库房盘点状态，如果对应的StoreHouseId没有记录，说明未启动该库房的盘点
        /// </summary>
        public List<StoreCheckInfoDto> StoreCheckInfo { get; set; }
    }

    /// <summary>
    /// 库房盘点状态
    /// </summary>
    public class StoreCheckInfoDto
    {
        /// <summary>
        /// 库存id
        /// </summary>
        public Guid StoreHouseId { get; set; }
        /// <summary>
        /// 盘点状态，1-盘点中，2-盘点审核中，99-盘点完成
        /// </summary>
        public int Status { get; set; }
    }

    public class SginyInventoryCreateOutputDto
    {
        public string InventoryNo { get; set; }
    }

    public class TinyInventoryCreateOutputDto
    {
        public string StocktakingCode { get; set; }
    }

    public class ExchangeInventoryCreateOutputDto
    {
        public string Data { get; set; }
        public int Code { get; set; }
    }

    public class UpdateCompanySysMonthOutputDto
    {
        public int code { get; set; }
        public string data { get; set; }
        public string message { get; set; }
    }

    public class StoreInventoryDeleteInputDto
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 盘点单号
        /// </summary>
        public string StoreCheckCode { get; set; }
    }


    public class OutInventoryItemInput
    {

        /// <summary>
        /// 公司ids
        /// </summary>
        public List<Guid> CompanyIds { get; set; }
    }

    public class OutInventoryItemOutput
    {

        /// <summary>
        /// 公司ids
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        public string SysMonth { get; set; }
    }
    #region  垫资盘点
    /// <summary>
    /// 垫资盘点Item
    /// </summary>
    public class AdvanceRecordOutputDto : AdvanceFundBusinessCheckItemPO
    {
    }

    /// <summary>
    /// 垫资盘点导出查询DTO
    /// </summary>
    public class AdvanceRecordExportQueryDto
    {
        /// <summary>
        /// 垫资盘点单Ids
        /// </summary>
        public List<Guid> InventoryIds { get; set; } = new List<Guid>();

        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 当前用户名
        /// </summary>
        public string? CurrentUserName { get; set; }
    }
    /// <summary>
    /// 垫资单详情
    /// </summary>
    public class AdvanceRecordDetailDto : AdvanceFundBusinessCheckDetailPO
    {
        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        public int IsInvoice { get; set; }

        /// <summary>
        /// 垫资天数
        /// </summary>
        public int AdvanceDays
        {
            get { return ReceivePeriod - AccountPeriod; }
        }
        public string CreditDateFormat
        {
            get
            {
                return CreditDate.ToString("yyyy-MM-dd");
            }
        }
        public string DebtDateFormat
        {
            get
            {
                return DebtDate.HasValue ? DebtDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string InvoiceDateFormat
        {
            get
            {
                return InvoiceDate.HasValue ? InvoiceDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string PaymentDateFormat
        {
            get
            {
                return PaymentDate.HasValue ? PaymentDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ExpectPaymentDateFormat
        {
            get
            {
                return ExpectPaymentDate.HasValue ? ExpectPaymentDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ReceiveDateFormat
        {
            get
            {
                return ReceiveDate.HasValue ? ReceiveDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ExpectReceiveDateFormat
        {
            get
            {
                return ExpectReceiveDate.HasValue ? ExpectReceiveDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public decimal ReceiveAmount
        {
            get
            {
                return ReceiveDate.HasValue ? CreditValue : 0;
            }
        }
        public decimal PaymentAmount
        {
            get
            {
                return PaymentDate.HasValue ? DebtValue : 0;
            }
        }

        /// <summary>
        /// 提前回款利息
        /// 如果逾期天数大于等于0，返回0
        /// 如果有回款，为付款金额*（供应链金融年化利率（%）/360）*逾期天数*-1，（*-1是因为提前回款的话逾期天数是负数）否则为0
        /// </summary>
        public decimal AdvanceReceiveInterest
        {
            get
            {
                return EarlyReturnInterest.HasValue ? EarlyReturnInterest.Value : 0;
            }
        }
    }
    #endregion
}
