﻿using Dapr.Client;
using Inno.CorePlatform.Common.CompetenceCenter.Enums;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Npoi.Mapper;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 寄售转购货
    /// </summary>
    public class ConsignmentToPurchaseService : BaseAppService, IConsignmentToPurchaseService
    {
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IPurchasePayPlanRepository _purchasePayPlanRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ICodeGenClient _codeGenClient;
        private readonly ICreditQueryService _creditQueryService;
        private readonly ILogger<ConsignmentToPurchaseService> _logger;
        protected readonly IExchangeRateService _exchangeRateService;
        protected readonly DaprClient _daprClient;
        protected readonly FinanceDbContext _db;

        public ConsignmentToPurchaseService(
            IPaymentRepository paymentRepository,
            IPurchasePayPlanRepository purchasePayPlanRepository,
            ISubLogService subLogService,
            ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            IPurchaseApiClient purchaseApiClient,
            IUnitOfWork _unitOfWork,
            IBDSApiClient bDSApiClient,
            IDomainEventDispatcher? deDispatcher,
            IKingdeeApiClient kingdeeApiClient,
            ICreditQueryService creditQueryService,
            ICodeGenClient codeGenClient,
            IExchangeRateService exchangeRateService,
            DaprClient daprClient,
            FinanceDbContext db,
            IAppServiceContextAccessor? contextAccessor,
            ILogger<ConsignmentToPurchaseService> logger) :
            base(creditRepository, debtRepository, subLogService, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._unitOfWork = _unitOfWork;
            this._purchaseApiClient = purchaseApiClient;
            this._purchasePayPlanRepository = purchasePayPlanRepository;
            this._bDSApiClient = bDSApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._codeGenClient = codeGenClient;
            this._creditQueryService = creditQueryService;
            this._exchangeRateService = exchangeRateService;
            this._daprClient = daprClient;
            this._db = db;
            this._logger = logger;
        }
        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var purchaseOrder = await _purchaseApiClient.GetByIdAsync(input.BusinessId.Value);
                if (purchaseOrder == null || purchaseOrder.PurchaseOrderDetails == null || !purchaseOrder.PurchaseOrderDetails.Any())
                {
                    _logger.LogError($"寄售转购货：{input.BusinessCode}，没有查到采购单");
                    throw new Exception("未获取到单据或单据明细为空");
                }
                var check = await _db.Debts.FirstOrDefaultAsync(p => p.PurchaseCode == input.BusinessCode);
                if (check != null)
                {
                    if (input.IsAutoBill.HasValue && input.IsAutoBill.Value)
                    {
                        _logger.LogError($"寄售转购货：{input.BusinessCode}，操作成功:但是该数据已存在");
                        return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                    }
                    else
                    {
                        _logger.LogError($"寄售转购货：{input.BusinessCode}，该单据已生成过应付");
                        throw new Exception("该单据已生成过应付");
                    }
                }
                var productIds = purchaseOrder.PurchaseOrderDetails.Select(p => p.Product.NameId.Value).Distinct().ToList();
                var agents = purchaseOrder.Agent != null && purchaseOrder.Agent.Id != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { purchaseOrder.Agent.Id.Value }) : new List<AgentBankInfo>();
                var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
                int limit = 500;//一次最多500条

                var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
                productNameInfoOutputAll.AddRange(productInfo);
                int totalPages = (int)Math.Ceiling((double)total / limit);
                if (totalPages > 1)
                {
                    for (int i = 2; i <= totalPages; i++)
                    {
                        (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                        productNameInfoOutputAll.AddRange(productInfo);
                    }
                }

                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(purchaseOrder.Consignee.Id.ToString()));

                var debt = new DebtDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    BillCode = purchaseOrder.Code,
                    BillDate = billDate,
                    CreatedBy = purchaseOrder.CreatedBy ?? "none",
                    CreatedTime = DateTime.Now,
                    Id = Guid.NewGuid(),
                    Value = Math.Round(purchaseOrder.PurchaseOrderDetails.Sum(p => Math.Abs(p.Quantity) * p.Cost), 2),
                    AgentId = purchaseOrder.Agent.Id,
                    AgentName = purchaseOrder.Agent.Name,
                    IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                    CompanyId = purchaseOrder.Consignee.Id,
                    CompanyName = purchaseOrder.Consignee.Name,
                    //RelateCode = purchaseOrder.Code,
                    DebtType = DebtTypeEnum.order,
                    OrderNo = purchaseOrder.Code,
                    RelateCode = purchaseOrder.SaleOrder?.code,
                    NameCode = purchaseOrder.Consignee.NameCode,
                    ServiceId = purchaseOrder.Service?.Id,
                    ServiceName = purchaseOrder.Service?.Name,
                    BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                    BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                    BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                    ProjectId = Guid.Parse(purchaseOrder.Project.Id),
                    ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                    PurchaseCode = purchaseOrder.Code,
                    PurchaseContactNo = purchaseOrder?.Contract?.Code,
                    ProjectCode = purchaseOrder?.Project.Code,
                    ProjectName = purchaseOrder.Project.Name,
                    CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                    CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                    CustomerId = purchaseOrder?.Hospital?.Id,
                    CustomerName = purchaseOrder?.Hospital?.Name,
                    Mark = purchaseOrder.PurchaseOrderDetails.First().Mark,
                    IsInternalTransactions = Utility.IsInternalTransactions(purchaseOrder.RelateCodeType.HasValue ? (int)purchaseOrder.RelateCodeType : 0)
                };
                if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                {
                    debt.RebateNo = purchaseOrder.RelateCode;
                }
                if (purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.TempGroupConsignmentToPurchase ||
                    purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.StockGroupConsignmentToPurchase)
                {
                    //集团寄售
                    debt.DebtType = DebtTypeEnum.grouporder;
                }
                if (debt.Value == 0)
                {
                    return BaseResponseData<int>.Success("金额为0不生成应付");
                }
                if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
                {
                    var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                    {
                        Effectdate = DateTime.Now,
                        OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                    });
                    if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                    {
                        _logger.LogError($"寄售转购货：{input.BusinessCode}，操作失败：未获取到汇率");
                        throw new Exception("操作失败：未获取到汇率");
                    }
                    debt.RMBAmount = Math.Round(debt.Value, 2) * exchange.Data.Excval;
                }
                else
                {
                    debt.RMBAmount = debt.Value;
                }
                var credits = new List<Credit>();
                if (purchaseOrder.PaymentPlans != null && purchaseOrder.PaymentPlans.Any())
                {
                    var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        ids = new List<string> { purchaseOrder.Consignee.Id.ToString() }
                    })).FirstOrDefault();
                    debt.DebtDetails = new List<DebtDetail>();
                    if (purchaseOrder.SaleOrder != null && !string.IsNullOrEmpty(purchaseOrder.SaleOrder.code))
                    {
                        credits = await _creditQueryService.GetByOrderNo(purchaseOrder.SaleOrder.code, purchaseOrder.Service?.Id);
                    }
                    if (!purchaseOrder.Remark.Equals("暂存出库转购货") && !purchaseOrder.Remark.Equals("暂存实盘转购货") && !(purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.TempGroupConsignmentToPurchase ||
                        purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.StockGroupConsignmentToPurchase))
                    {
                        if (credits == null || !credits.Any())
                        {
                            _logger.LogError($"寄售转购货：{input.BusinessCode}，操作失败：销售订单还未完成，请稍后重试");
                            throw new Exception("销售订单还未完成，请稍后重试");
                        }
                        //应付写入客户
                        debt.CustomerId = credits.First().CustomerId;
                        debt.CustomerName = credits.First().CustomerName;
                    }
                    await _purchasePayPlanRepository.DeleteByPurchaseNo(purchaseOrder.Code);
                    var purchasePayPlans = new List<PurchasePayPlan>();
                    var deebtDetailAll = new List<DebtDetail>();
                    foreach (var plan in purchaseOrder.PaymentPlans)
                    {
                        if (plan.APRevisionDetails == null || !plan.APRevisionDetails.Any())
                        {
                            _logger.LogError($"寄售转购货：{input.BusinessCode}，操作失败：采购单：{purchaseOrder.Code}，没有应付修订明细");
                            throw new Exception($"操作失败：采购单：{purchaseOrder.Code}，没有应付修订明细");
                        }
                        foreach (var detail in plan.APRevisionDetails)
                        {
                            var temp = new PurchasePayPlan
                            {
                                Id = Guid.NewGuid(),
                                CompanyId = purchaseOrder.Consignee?.Id,
                                CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                                NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                                ServiceId = purchaseOrder.Service?.Id,
                                ServiceName = purchaseOrder.Service?.Name,
                                AgentId = purchaseOrder.Agent?.Id,
                                AgentName = purchaseOrder.Agent?.Name,
                                ProductNo = (detail.Product?.ProductNo) ?? string.Empty,
                                ProductId = detail.Product?.Id,
                                PurchaseId = input.BusinessId,
                                PurchaseCode = purchaseOrder.Code,
                                ForwardPurchaseCode = purchaseOrder.ForwardOrder?.Code,
                                AccountPeriodType = (AccountPeriodTypeEnum)plan.DPOType,
                                Ratio = plan.ProportionRevision == null ? 0 : plan.ProportionRevision.Value,
                                Quantity = detail.Quantity,
                                RatioPrice = detail.PurchaseCostRevision,
                                Price = detail.PurchaseCost,
                                ProbablyPayTime = plan.ProbablyPayTime.HasValue ? plan.ProbablyPayTime.Value.DateTime : plan.APTime,
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now,
                                PurchaseDetailId = detail.PurchaseDetailId,
                                AccountPeriodDays = plan.DPO
                            };
                            await _purchasePayPlanRepository.AddAsync(temp);

                            var product = purchaseOrder.PurchaseOrderDetails.FirstOrDefault(p => p.Product.Id == detail.Product.Id);
                            if (product != null)
                            {

                                var debtDetail = new DebtDetail
                                {
                                    OriginValue = !product.CostDiscount.HasValue || product.CostDiscount.Value == 0 ? null : (detail.Quantity * Math.Round(detail.PurchaseCostRevision, 2)) / product.CostDiscount * 100,
                                    AccountPeriodType = plan.DPOType,
                                    Value = detail.Quantity * detail.PurchaseCostRevision,
                                    CostDiscount = product.CostDiscount ?? 0,
                                    FinanceDiscount = product.FinanceDiscount ?? 0,
                                    DistributionDiscount = product.DistributionDiscount ?? 0,
                                    SpdDiscount = product.SpdDiscount ?? 0,
                                    TaxDiscount = product.TaxDiscount ?? 0,
                                    ProbablyPayTime = (plan.DPOType == 2 || plan.DPOType == 1) ? DateTime.Now.AddDays(plan.DPO).Date : null,
                                    AccountPeriodDays = plan.DPO,
                                };
                                deebtDetailAll.Add(debtDetail);
                            }
                        }
                    }
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = debt.BillCode.Split('-')[0],
                        BillType = "DPP",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    int index = 1;
                    deebtDetailAll.GroupBy(p => new
                    {
                        p.AccountPeriodType,
                        p.CostDiscount,
                        p.FinanceDiscount,
                        p.DistributionDiscount,
                        p.SpdDiscount,
                        p.TaxDiscount,
                        p.AccountPeriodDays
                    }).ForEach(g =>
                    {

                        var debtDetail = new DebtDetail
                        {
                            Id = Guid.NewGuid(),
                            AccountPeriodType = g.Key.AccountPeriodType,
                            Code = outPut.Codes.First() + "-" + index.ToString().PadLeft(3, '0'),
                            CreatedBy = purchaseOrder.CreatedBy ?? "none",
                            CreatedTime = DateTime.Now,
                            DebtId = debt.Id,
                            PurchaseCode = purchaseOrder.Code,
                            Status = DebtDetailStatusEnum.WaitExecute,
                            OriginValue = g.Sum(p => p.OriginValue),
                            Value = g.Sum(p => p.Value),
                            OrderNo = purchaseOrder.SaleOrder?.code,
                            CreditId = credits != null && credits.Any() ? credits.First().Id : null,
                            CostDiscount = g.Key.CostDiscount ?? 0,
                            FinanceDiscount = g.Key.FinanceDiscount ?? 0,
                            DistributionDiscount = g.Key.DistributionDiscount ?? 0,
                            SpdDiscount = g.Key.SpdDiscount ?? 0,
                            TaxDiscount = g.Key.TaxDiscount ?? 0,
                            AccountPeriodDays = g.First().AccountPeriodDays,
                        };
                        if (debtDetail.AccountPeriodType == 2 || debtDetail.AccountPeriodType == 1)
                        {
                            debtDetail.ProbablyPayTime = g.Max(p => p.ProbablyPayTime);
                        }
                        //如果是暂存出库触发的，生成的应付明细账期类型写入库账期，预计付款日期写当前日期，订单号和应收单号置空，关联单号写出库单号
                        if (purchaseOrder.Remark.Equals("暂存出库转购货") || purchaseOrder.Remark.Equals("暂存实盘转购货") ||
                        purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.TempGroupConsignmentToPurchase ||
                        purchaseOrder?.RelateCodeType == RelateCodeTypeEnums.StockGroupConsignmentToPurchase)
                        {
                            debtDetail.AccountPeriodType = (int)AccountPeriodTypeEnum.StoreIn;
                            debtDetail.ProbablyPayTime = DateTime.Now;
                            debtDetail.OrderNo = string.Empty;
                            debtDetail.CreditId = null;
                            debt.RelateCode = purchaseOrder.RelateCode;
                        }
                        debt.DebtDetails.Add(debtDetail);
                        index++;
                    });
                }
                if (debt.DebtDetails == null || !debt.DebtDetails.Any())
                {
                    _logger.LogError($"寄售转购货：{input.BusinessCode}，操作失败：采购单：{purchaseOrder.Code}，没有付款计划");
                    throw new Exception($"操作失败：采购单：{purchaseOrder.Code}，没有付款计划");
                }
                var kingdeeDebts = new List<KingdeeDebt>();
                InitKingdeeDebt(purchaseOrder, productNameInfoOutputAll, debt, kingdeeDebts);
                var requestBody = JsonConvert.SerializeObject(input);
                _logger.LogInformation($"寄售转购货：{input.BusinessCode}，生成应付到金蝶系统，请求参数：{requestBody}");
                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, input.BusinessSubType, requestBody);
                _logger.LogInformation($"寄售转购货：{input.BusinessCode}，生成应付到金蝶系统，返回结果：{kingdeeRes.Message}");
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    await base.CreateDebt(debt);
                    await _debtRepository.RepaireDebtDiff(new List<Guid> { debt.Id });
                    return BaseResponseData<int>.Success("生成应付成功");
                }
                else
                {
                    _logger.LogError($"寄售转购货：{input.BusinessCode}，生成应付到金蝶系统失败，原因：{kingdeeRes.Message}");
                    throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"寄售转购货：{input.BusinessCode}，异常：{ex.Message}");
                throw;
            }
        }
        /// <summary>
        /// 金蝶应付参数
        /// </summary>
        /// <param name="purchaseOrder"></param>
        /// <param name="productInfo"></param>
        /// <param name="debt"></param>
        /// <param name="kingdeeDebts"></param>
        private static void InitKingdeeDebt(PurchaseOutPut purchaseOrder, List<ProductNameInfoOutput> productInfo, DebtDto debt, List<KingdeeDebt> kingdeeDebts)
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = debt.OrderNo,
                jfzx_creator = debt.CreatedBy ?? "none",
                currency_number = debt.CoinCode ?? "CNY",
                pricetaxtotal4 = debt.Value,
            };
            string jfzx_revisiontype = "";
            //暂存来的集团寄售转购货
            if (purchaseOrder.RelateCodeType == RelateCodeTypeEnums.TempGroupConsignmentToPurchase)
            {
                jfzx_revisiontype = "temporarystorage";
                kingdeeDebt.jfzx_staging_purchase = false;
            }
            //库存来的集团寄售转购货
            if (purchaseOrder.RelateCodeType == RelateCodeTypeEnums.StockGroupConsignmentToPurchase)
            {
                jfzx_revisiontype = "stock";
                kingdeeDebt.jfzx_staging_purchase = false;
            }
            kingdeeDebt.jfzx_rebate = purchaseOrder.RebateType.HasValue;
            if (purchaseOrder.Remark.Equals("暂存出库转购货") || purchaseOrder.Remark.Equals("暂存实盘转购货"))
            {
                kingdeeDebt.jfzx_staging_purchase = true;
            }
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
            var amount = 0m;

            purchaseOrder.PurchaseOrderDetails!.GroupBy(a => new { a.Product.NameId, a.Cost, a.TaxRate, a.StandardCost, a.BDetail?.CustomerId }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                //d.taxrate = t.Key.TaxRate;
                d.taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? t.Key.TaxRate : 0;
                d.quantity = t.Sum(b => Math.Abs(b.Quantity));
                d.pricetax = t.Key.Cost;
                d.jfzx_standard_cost = t.Key.StandardCost.HasValue ? t.Key.StandardCost.Value : 0;
                d.jfzx_project_number = purchaseOrder.Project.Code;
                d.jfzx_rebatecustomerid = string.IsNullOrEmpty(t.Key.CustomerId) ? "" : t.Key.CustomerId.ToUpper();
                if (!string.IsNullOrEmpty(jfzx_revisiontype))
                {
                    d.jfzx_revisiontype = jfzx_revisiontype;
                }
                var thisProductInfo = productInfo.FirstOrDefault(e => e.productNameId == t.Key.NameId);
                if (thisProductInfo != null)
                {
                    if (thisProductInfo.classificationNewGuid.HasValue)
                    {
                        d.material_number1 = thisProductInfo.classificationNewGuid.Value.ToString().ToUpper();
                    }
                    else
                    {
                        if (thisProductInfo.classificationGuid.HasValue)
                        {
                            d.material_number1 = thisProductInfo.classificationGuid.Value.ToString().ToUpper();
                        }
                    }
                }
                else
                {
                    throw new Exception("操作失败：没有找到对应的产品Id:" + t.Key.NameId);
                }
                if (purchaseOrder.RebateType.HasValue)
                {
                    d.jfzx_rebateType = (int)purchaseOrder.RebateType;
                }
                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + t.Key.TaxRate / 100.00M)), 20);
                amount += d.price2 * d.quantity;

            });

            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);

            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);
        }

        public async Task<BaseResponseData<List<KingdeeDebt>>> GetParkOrderKingdeeDebtParams(EventBusDTO dto)
        {
            var purchaseOrder = await _purchaseApiClient.GetByIdAsync(dto.BusinessId.Value);
            if (purchaseOrder == null || purchaseOrder.PurchaseOrderDetails == null || !purchaseOrder.PurchaseOrderDetails.Any())
            {
                return BaseResponseData<List<KingdeeDebt>>.Failed(500, "未取到上游数据");
            }
            var productIds = purchaseOrder.PurchaseOrderDetails.Select(p => p.Product.NameId.Value).Distinct().ToList();

            var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
            int limit = 500;//一次最多500条

            var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
            productNameInfoOutputAll.AddRange(productInfo);
            int totalPages = (int)Math.Ceiling((double)total / limit);
            if (totalPages > 1)
            {
                for (int i = 2; i <= totalPages; i++)
                {
                    (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                    productNameInfoOutputAll.AddRange(productInfo);
                }
            }
            var debt = new DebtDto
            {
                AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                BillCode = purchaseOrder.Code,
                CreatedBy = purchaseOrder.CreatedBy ?? "none",
                CreatedTime = DateTime.Now,
                Id = Guid.NewGuid(),
                Value = Math.Round(purchaseOrder.PurchaseOrderDetails.Sum(p => Math.Abs(p.Quantity) * p.Cost), 2),
                AgentId = purchaseOrder.Agent.Id,
                AgentName = purchaseOrder.Agent.Name,
                CompanyId = purchaseOrder.Consignee.Id,
                CompanyName = purchaseOrder.Consignee.Name,
                //RelateCode = purchaseOrder.Code,
                DebtType = DebtTypeEnum.order,
                OrderNo = purchaseOrder.Code,
                RelateCode = purchaseOrder.SaleOrder?.code,
                NameCode = purchaseOrder.Consignee.NameCode,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                ProjectId = Guid.Parse(purchaseOrder.Project.Id),
                ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                PurchaseCode = purchaseOrder.Code,
                PurchaseContactNo = purchaseOrder?.Contract?.Code,
                ProjectCode = purchaseOrder?.Project.Code,
                ProjectName = purchaseOrder.Project.Name,
                CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
            };
            if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
            {
                debt.RebateNo = purchaseOrder.RelateCode;
            }
            if (debt.Value == 0)
            {
                return BaseResponseData<List<KingdeeDebt>>.Failed(500, "无需生成应付");
            }
            if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
            {
                var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                {
                    Effectdate = DateTime.Now,
                    OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                });
                if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                {
                    throw new Exception("操作失败：未获取到汇率");
                }
                debt.RMBAmount = Math.Round(debt.Value, 2) * exchange.Data.Excval;
            }
            else
            {
                debt.RMBAmount = debt.Value;
            }
            var kingdeeDebts = new List<KingdeeDebt>();
            InitKingdeeDebt(purchaseOrder, productNameInfoOutputAll, debt, kingdeeDebts);
            return new BaseResponseData<List<KingdeeDebt>>()
            {
                Code = CodeStatusEnum.Success,
                Data = kingdeeDebts
            };
        }
    }
}
