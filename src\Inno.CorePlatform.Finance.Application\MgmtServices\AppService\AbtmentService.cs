﻿using Amazon.Runtime.Internal;
using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Npoi.Mapper;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class AbtmentService : IAbtmentService
    {
        private readonly IAbatementRepository _abatementRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IPaymentAutoItemRepository _paymentAutoItemRepository;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IUnitOfWorkFactory _uowFactory;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AbtmentService> _logger;
        private readonly IDebtRepository _debtRepository;
        private readonly IBaseAllQueryService<AbatementPo> _abtmentQueryService;
        private readonly IBaseAllQueryService<DebtPaymentUseDetailPo> _debtPaymentUseDetailQueryService;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<DebtPo> _debtQueryService;
        private readonly IBaseAllQueryService<CreditPo> _creditQueryService;
        private readonly ICreditRepository _creditRepository;
        private readonly IBaseAllQueryService<PaymentAutoDetailPo> _paymentAutoDetailQueryService;
        private readonly IPaymentAutoDetailRepository _paymentAutoDetailRepository;
        private readonly IBaseAllQueryService<PaymentAutoItemPo> _paymentAutoItemQueryService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly DaprClient _daprClient;
        private readonly ISPDApiClient _sPDApiClient;
        private readonly IBaseAllQueryService<InvoiceCreditPo> _invoiceCreditQueryService;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        private readonly IBaseAllQueryService<PaymentAutoAgentBankInfoPo> _paymentAutoAgentQueryService;
        private readonly FinanceDbContext _db;
        private readonly ISubLogService _subLogService;
        
        public AbtmentService(IAbatementRepository abatementRepository,
                              IPaymentRepository paymentRepository,
                              IPaymentAutoItemRepository paymentAutoItemRepository,
                              IDebtDetailRepository debtDetailRepository,
                              IUnitOfWorkFactory uowFactory,
                              ILogger<AbtmentService> logger,
                              IDebtRepository debtRepository,
                              IBaseAllQueryService<AbatementPo> abtmentQueryService,
                              IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
                              IBaseAllQueryService<DebtPo> debtQueryService,
                              IBaseAllQueryService<CreditPo> creditQueryService,
                              ICreditRepository creditRepository,
                              IBaseAllQueryService<PaymentAutoDetailPo> paymentAutoDetailQueryService,
                              IPaymentAutoDetailRepository paymentAutoDetailRepository,
                              IBaseAllQueryService<PaymentAutoItemPo> paymentAutoItemQueryService,
                              IBDSApiClient bDSApiClient,
                              DaprClient daprClient,
                              ISPDApiClient sPDApiClient,
                              IBaseAllQueryService<InvoiceCreditPo> invoiceCreditQueryService,
                              IBaseAllQueryService<DebtPaymentUseDetailPo> debtPaymentUseDetailQueryService,
                              IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
                              IExchangeRateService exchangeRateService,
                              IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
                              FinanceDbContext db,
                              IKingdeeApiClient kingdeeApiClient,
                              IBaseAllQueryService<PaymentAutoAgentBankInfoPo> paymentAutoAgentQueryService,
                              ISubLogService subLogService)
        {
            this._daprClient = daprClient;
            _abatementRepository = abatementRepository;
            _paymentRepository = paymentRepository;
            _paymentAutoItemRepository = paymentAutoItemRepository;
            _debtDetailRepository = debtDetailRepository;
            _debtRepository = debtRepository;
            _logger = logger;
            _uowFactory = uowFactory;
            _abtmentQueryService = abtmentQueryService;
            _creditRepository = creditRepository;
            _kingdeeApiClient = kingdeeApiClient;
            _unitOfWork = _uowFactory.Create(_abatementRepository, _paymentRepository, _paymentAutoItemRepository, _debtDetailRepository, _debtRepository, _creditRepository);
            _debtDetailQueryService = debtDetailQueryService;
            _debtQueryService = debtQueryService;
            _creditQueryService = creditQueryService;
            _paymentAutoDetailQueryService = paymentAutoDetailQueryService;
            _paymentAutoDetailRepository = paymentAutoDetailRepository;
            _paymentAutoItemQueryService = paymentAutoItemQueryService;
            _bDSApiClient = bDSApiClient;
            _purchasePayPlanQueryService = purchasePayPlanQueryService;
            _exchangeRateService = exchangeRateService;
            _debtPaymentUseDetailQueryService = debtPaymentUseDetailQueryService;
            _sPDApiClient = sPDApiClient;
            _invoiceCreditQueryService = invoiceCreditQueryService;
            _inventoryQueryService = inventoryQueryService;
            _db = db;
            this._paymentAutoAgentQueryService = paymentAutoAgentQueryService;
            this._subLogService = subLogService;
        }

        public async Task<int> GenerateAbtAsync(List<GenerateAbtInput> lstInput)
        {
            if (lstInput.Count <= 0)
            {
                throw new AppServiceException("生成冲销的明细不能为空!");
            }
            var lstDebtDetailId = lstInput.Select(t => t.DebtDetilId).ToList();
            // 1.根据应付执行计划Id查询应付执行计划
            var lstDebtDetail = await _debtDetailRepository.GetListForAbtAsync(lstDebtDetailId);
            var lstDebtId = lstDebtDetail.Select(t => t.DebtId).ToList();
            var lstDebt = await _debtQueryService.GetAllListAsync(t => lstDebtId.Contains(t.Id));
            if (lstDebt != null && lstDebt.Any())
            {
                var lstAbtment = new List<Abatement>();
                try
                {
                    lstDebtDetail = lstDebtDetail.Where(p => p.Status == DebtDetailStatusEnum.WaitExecute).ToList(); //只查询待执行的数据
                    if (lstDebtDetail == null || !lstDebtDetail.Any())
                    {
                        throw new AppServiceException("冲销的明细不能为空!");
                    }
                    if (lstDebt.GroupBy(t => t.CompanyId).Count() > 1)
                    {
                        throw new AppServiceException("冲销的明细必须属于同一个公司!");
                    }

                    var companyId = lstDebt.First().CompanyId;
                    var lstPayment = new List<Payment>();
                    var dictPayment = new Dictionary<Guid, string>();
                    var dictPaymentDebtDetail = new Dictionary<Guid, string>();
                    //根据DebtDetailId 查找 PaymentAutoDetail
                    var lstPaymentAutoDetail = await _paymentAutoDetailQueryService.GetAllListAsync(t => lstDebtDetailId.Contains(t.DebtDetilId), new List<string> { "PaymentAutoItem" });
                    var paymentItemIds = lstPaymentAutoDetail.Select(o => o.PaymentAutoItemId).Distinct().ToList();
                    var lstPaymentAutoAgents = await _paymentAutoAgentQueryService.GetAllListAsync(t => paymentItemIds.Contains(t.PaymentAutoItemId));

                    // 2.根据付款信息生成付款单
                    lstInput.GroupBy(p => p.PaymentCode).ForEach(p =>
                    {
                        var items = lstInput.Where(t => t.PaymentCode == p.Key).ToList();
                        var item = items.FirstOrDefault();
                        var debtDetilIds = items.Select(p => p.DebtDetilId).ToList();
                        var _debtDetails = lstDebtDetail.Where(t => debtDetilIds.Contains(t.Id)).ToList();
                        var _debtDetail = _debtDetails.FirstOrDefault();
                        if (_debtDetail != null)
                        {
                            var _debtIds = _debtDetails.Select(p => p.DebtId).ToList();
                            var _debts = lstDebt.Where(t => _debtIds.Contains(t.Id)).ToList();
                            _debts.GroupBy(g => g.ServiceId).ForEach(g =>
                            {
                                var _debt = g.First();
                                var _paymentDebtIds = g.Select(p => p.Id).ToList();
                                var customerNames = g.Where(p => !string.IsNullOrEmpty(p.CustomerName)).Select(p => p.CustomerName).Distinct().ToList();
                                var _paymentAutoDetail = lstPaymentAutoDetail.FirstOrDefault(t => t.DebtDetilId == _debtDetail.Id);
                                var _paymentAutoItem = _paymentAutoDetail?.PaymentAutoItem;
                                var _paymentDebtDetailIds = _debtDetails.Where(t => t.DebtId != null && _paymentDebtIds.Contains(t.DebtId.Value)).Select(p => p.Id).ToList();
                                var value = lstInput.Where(t => _paymentDebtDetailIds.Contains(t.DebtDetilId)).Sum(t => t.Value);
                                var limitDiscount = lstPaymentAutoDetail.Where(t => _paymentDebtDetailIds.Contains(t.DebtDetilId))?.Sum(t => t.LimitedDiscount);
                                Payment payment = new Payment
                                {
                                    Id = Guid.NewGuid(),
                                    CompanyId = companyId,
                                    AbatedStatus = AbatedStatusEnum.Abated,
                                    CompanyName = item.CompanyName,
                                    NameCode = _debt.NameCode,
                                    Code = generatePaymentCode(lstPayment, item.PaymentCode),
                                    Value = value,
                                    PaymentDate = item.PaymentDate,
                                    BillDate = item.PaymentDate,
                                    ServiceId = _debt.ServiceId,
                                    ServiceName = _debt.ServiceName,
                                    PayClassify = item.PayClassify,
                                    AgentId = _debt.AgentId,
                                    AgentName = _debt.AgentName,
                                    Type = PaymentTypeEnum.Normal,
                                    BusinessDeptId = _paymentAutoItem?.BusinessDeptId,
                                    BusinessDeptFullName = _paymentAutoItem?.BusinessDeptFullName,
                                    BusinessDeptFullPath = _paymentAutoItem?.BusinessDeptFullPath,
                                    CoinCode = _debt.CoinCode,
                                    CoinName = _debt.CoinName,
                                    AdvancePayMode = AdvancePayModeEnum.NotUseQuota,
                                    PaymentAutoItemCode = _paymentAutoItem?.Code,
                                    ProjectId = _debt.ProjectId,
                                    ProjectCode = _debt.ProjectCode,
                                    ProjectName = _debt.ProjectName,
                                    PurchaseContactNo = _debt.PurchaseContactNo,
                                    CustomerId = _debt.CustomerId,
                                    CustomerName = string.Join(",", customerNames),
                                    LimitedDiscount = limitDiscount,
                                    OriginCode = item.PaymentCode,
                                    AttachFileIds = lstPaymentAutoAgents?.FirstOrDefault(t => t.PaymentAutoItemId == _paymentAutoItem.Id && t.AgentId == _debt.AgentId)?.AttachFileIds,
                                };
                                payment.CreateBy(item.UserName);
                                lstPayment.Add(payment);

                                foreach (DebtPo? dt in g)
                                {
                                    dictPayment.TryAdd(dt.Id, payment.Code);
                                }
                                foreach (var id in _paymentDebtDetailIds)
                                {
                                    dictPaymentDebtDetail.TryAdd(id, payment.Code);
                                }
                            });
                        }
                    });
                    foreach (var payment in lstPayment)
                    {
                        if (!string.IsNullOrEmpty(payment.CoinName) && payment.CoinName != "人民币")
                        {
                            var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                            {
                                Effectdate = DateTime.Now,
                                OrgcurName = payment.CoinName
                            });
                            if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                            {
                                throw new Exception("操作失败：未获取到汇率");
                            }
                            payment.RMBAmount = payment.Value * exchange.Data.Excval;
                        }
                        else
                        {
                            payment.RMBAmount = payment.Value;
                        }
                    }
                    if (lstPayment.Any())
                    {
                        foreach (var detail in lstDebtDetail)
                        {
                            //修改DebtDetail付款状态
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var payments = lstInput.Where(p => p.DebtDetilId == detail.Id).ToList();
                            //根据DebtDetail生成DebtDetailExcute记录
                            foreach (var payment in payments)
                            {
                                DebtDetailExcute excute = new DebtDetailExcute();
                                excute.DebtDetailId = detail.Id;
                                excute.PaymentCode = dictPaymentDebtDetail.TryGetValue(detail.Id, out string? code) ? code : payment.PaymentCode;
                                excute.PaymentDate = payment.PaymentDate;
                                excute.Value = payment.Value;
                                detail.AddDebtDetailExcute(excute, payment.UserName);
                            }
                            await _debtDetailRepository.UpdateAsync(detail);
                        }

                        await _paymentRepository.AddManyAsync(lstPayment);
                    }

                    // 3.根据付款单生成冲销单,付款冲应付

                    //查询应付单的冲销记录
                    var lstDebtCode = lstDebt.Select(t => t.BillCode).Distinct().ToList();
                    var lstDebtAbt = await _abtmentQueryService.GetAllListAsync(t => lstDebtCode.Contains(t.CreditBillCode));

                    var lstUpdateDebt = new List<Debt>();

                    var lstUpdatePaymentAutoDetail = new List<PaymentAutoDetail>();
                    var sysMonth = await _bDSApiClient.GetSystemMonth(lstDebt.First().CompanyId.Value.ToString());
                    await CheckSysMonth(lstDebt.First().CompanyId.Value, sysMonth);
                    foreach (var item in lstInput)
                    {
                        var _debtDetail = lstDebtDetail.FirstOrDefault(t => t.Id == item.DebtDetilId);
                        if (_debtDetail == null)
                        {
                            continue;
                        }
                        var _debt = lstDebt.First(t => t.Id == _debtDetail.DebtId);

                        var _paymentCode = dictPayment.TryGetValue(_debt.Id, out string? code) ? code : item.PaymentCode;
                        var _payment = lstPayment.FirstOrDefault(t => t.Code == _paymentCode);
                        Abatement abt = new Abatement();
                        abt.Id = Guid.NewGuid();
                        abt.CreditBillCode = _debt.BillCode;
                        abt.CreditType = "debt";
                        abt.DebtBillCode = _paymentCode;
                        abt.DebtType = "payment";
                        abt.Value = item.Value;
                        abt.CreateBy(item.UserName);
                        abt.Abtdate = DateTime.Now;
                        lstAbtment.Add(abt);
                        //更新paymentAutoDetail表的paymentCode 字段
                        if (lstUpdatePaymentAutoDetail.Count(p => p.DebtDetilId == _debtDetail.Id) <= 0)
                        {
                            var _paymentAutoDetail = lstPaymentAutoDetail.First(t => t.DebtDetilId == _debtDetail.Id);
                            var paymentAutoDetailDo = _paymentAutoDetail.Adapt<PaymentAutoDetail>();
                            paymentAutoDetailDo.PaymentCode = _paymentCode;
                            paymentAutoDetailDo.PaymentDate = item.PaymentDate;
                            lstUpdatePaymentAutoDetail.Add(paymentAutoDetailDo);
                        }
                        if (_debt.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            _logger.LogInformation($"应付单：{_debt.BillCode}已经冲销完成，本次付款单:{item.PaymentCode}无法冲销改应付:{item.Value}");
                            continue;
                        }

                        //计算应付的余额
                        var thisDebtAbatementValue = lstDebtAbt.Where(t => t.CreditBillCode == _debt.BillCode).Sum(q => q.Value);
                        var thisDebtLeftValue = _debt.Value - thisDebtAbatementValue;
                        if (thisDebtLeftValue <= 0)
                        {
                            throw new AppServiceException($"应付单：{_debt.BillCode},剩余额度小于0,不允许操作");
                        }

                        if (item.Value > thisDebtLeftValue)
                        {
                            throw new AppServiceException($"应付单：{_debt.BillCode},剩余额度小于冲销额度,不允许操作");
                        }
                        //计算应付的余额，余额为0的应付，冲销状态为已完成
                        //thisDebtLeftValue = thisDebtLeftValue - item.Value; //应付剩余金额减去本次冲销金额
                        thisDebtLeftValue = thisDebtLeftValue - lstAbtment.Where(t => t.CreditBillCode == _debt.BillCode).Sum(p => p.Value);//应付剩余金额减去本次冲销金额
                        if (thisDebtLeftValue <= 0)
                        {
                            _debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                            var _debtDo = _debt.Adapt<Debt>();
                            lstUpdateDebt.Add(_debtDo);
                        }
                    }

                    var lstPaymentAutoItem = await _paymentAutoItemQueryService.GetAllListAsync(t => lstUpdatePaymentAutoDetail.Select(q => q.PaymentAutoItemId).Contains(t.Id));
                    var lstPaymentAutoItemId = lstPaymentAutoItem.Select(t => t.Id).ToList();
                    var lstPaymentAutoDetailForItem = await _paymentAutoDetailQueryService.GetAllListAsync(t => lstPaymentAutoItemId.Contains(t.PaymentAutoItemId));
                    var lstUpdatePaymentAutoItem = new List<PaymentAutoItem>();

                    foreach (var item in lstPaymentAutoItem)
                    {
                        var list = lstPaymentAutoDetailForItem.Where(t => t.PaymentAutoItemId == item.Id).ToList();
                        //var _uddateCount = lstUpdatePaymentAutoDetail.Count(t => t.PaymentAutoItemId == item.Id);
                        if (list.Sum(p => p.Value) <= (lstInput.Sum(p => p.Value)))
                        {
                            PaymentAutoItem itemDo = item.Adapt<PaymentAutoItem>();
                            itemDo.Status = Domain.PaymentAutoItemStatusEnum.Completed;
                            lstUpdatePaymentAutoItem.Add(itemDo);
                        }
                    }

                    if (lstAbtment.Count > 0)
                    {
                        await _abatementRepository.AddManyAsync(lstAbtment);
                    }

                    if (lstUpdateDebt.Count > 0)
                    {
                        await _debtRepository.UpdateManyAsync(lstUpdateDebt);
                    }

                    if (lstUpdatePaymentAutoDetail.Count > 0)
                    {
                        await _paymentAutoDetailRepository.UpdateManyAsync(lstUpdatePaymentAutoDetail);
                    }

                    if (lstUpdatePaymentAutoItem.Count > 0)
                    {
                        await _paymentAutoItemRepository.UpdateManyAsync(lstUpdatePaymentAutoItem);
                    }
                    int nRes = await _unitOfWork.CommitAsync();
                    return nRes;
                }
                catch (Exception ex)
                {
                    _subLogService.LogAzure("GenerateAbt",
                        $"{lstInput.FirstOrDefault().PaymentCode}异常:{ex.Message}-{ex.StackTrace}", "金蝶批量付款回调");
                    throw;
                }
                finally
                {
                    //推给金蝶
                    await PushPaymentSettlementKDForBatchPaymentAsync(lstDebt, lstAbtment);
                    //关税、进口增值税付款后通知采购
                    var customsDebts = lstDebt.Where(p => p.DebtType == DebtTypeEnum.expenses && (p.Note.Contains("关税") || p.Note.Contains("进口增值税"))).ToList();
                    await CustomsPaiedPub(customsDebts);

                    //服务费付款后通知采购
                    var servicefeeDebts = await _debtQueryService.GetAllListAsync(t => t.DebtType == DebtTypeEnum.servicefee && lstDebtId.Contains(t.Id), new List<string> { "DebtDetails" });

                    await ServicePaiedPubForSplit(servicefeeDebts, lstDebtDetailId);
                    //寄售转购货应付 通知防腐层 
                    await OrderPaiedPub(lstInput);

                    //垫资评价 通知
                    await AdvancePub(lstDebtDetail);
                }
            }
            return 0;

            string generatePaymentCode(List<Payment> lstPayment, string paymentCode)
            {
                if (lstPayment == null || lstPayment.Count <= 0)
                {
                    return paymentCode;
                }

                if (!lstPayment.Any(p => p.Code == paymentCode))
                {
                    return paymentCode;
                }

                var code = paymentCode;
                var i = 1;
                while (lstPayment.Any(p => p.Code == code))
                {
                    code = paymentCode + "-" + i.ToString().PadLeft(3, '0');
                    i++;
                }
                return code;
            }
        }


        private async Task<string> CheckSysMonth(Guid companyId, string sysMonth)
        {
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
            {
                var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
                if (inventory != null)
                {
                    if (inventory.Status == 2)
                    {
                        throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                    }
                    if (inventory.Status == 99)
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }
                }
            }
            else
            {
                DateTime.TryParse(sysMonth, out DateTime billDate);
                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                {
                    throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                }
            }
            return sysMonth;
        }
        /// <summary>
        /// 通知采购，关税和进口增值税付款通知
        /// </summary>
        /// <param name="debts"></param>
        /// <returns></returns>
        private async Task CustomsPaiedPub(List<DebtPo> debts)
        {
            var jsonStr = JsonConvert.SerializeObject(debts);
            _logger.LogWarning($"关税、进口增值税付款后通知采购：" + jsonStr);
            if (debts != null && debts.Any())
            {
                var publishBodys = debts.Select(p => new CustomsOrderPaiedInput
                {
                    CustomsPaymentCode = p.RelateCode,
                    PaymentAmount = p.Value,
                    Tag = p.Note.Contains("关税") ? "关税" : "进口增值税"
                }).ToList();

                await _daprClient.PublishEventAsync<List<CustomsOrderPaiedInput>>("pubsub-default", "sia-customs-order-paied", publishBodys);
            }
        }

        /// <summary>
        /// 服务费付款通知
        /// </summary>
        /// <param name="debts"></param>
        /// <returns></returns>
        public async Task ServicePaiedPub(List<DebtPo> debts, List<string> debtBillCodes = null)
        {
            if (debtBillCodes != null)
            {
                debts.AddRange(await _db.Debts.Where(p => debtBillCodes.Contains(p.BillCode) && p.DebtType == DebtTypeEnum.servicefee && p.AbatedStatus == AbatedStatusEnum.Abated).ToListAsync());
            }
            var jsonStr = JsonConvert.SerializeObject(debts);
            _logger.LogWarning($"服务费付款后通知采购：" + jsonStr);
            if (debts != null && debts.Any())
            {
                var publishBodys = debts.Select(p => new ServiceFeeOrderPaiedInput
                {
                    Code = p.RelateCode,
                    PaymentAmount = p.Value,
                }).ToList();

                await _daprClient.PublishEventAsync<List<ServiceFeeOrderPaiedInput>>("pubsub-default", "finance-servicepurchase-paied", publishBodys);
            }
        }
        /// <summary>
        /// 负数应付冲销之后通知采购
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task MinusDebtAbtPurchasePub(Guid debtId, List<GenerateAbtForDebtInput> inputs)
        {
            List<string> debtBillCodes = inputs.Select(p => p.BillCode).Distinct().ToList();
            var debt = await _db.Debts.AsNoTracking().FirstOrDefaultAsync(p => p.Id == debtId);
            if (debt != null && debtBillCodes != null)
            {
                var debtAbtPurchasePubs = new List<DebtAbtPurchasePubInput>();
                var debts = await _db.Debts.Where(p => debtBillCodes.Contains(p.BillCode)).ToListAsync();
                if (debts != null && debts.Any())
                {
                    if (debt.Value < 0)
                    {
                        foreach (var item in debts)
                        {
                            var AbatementAmount = inputs.Where(p => p.BillCode == item.BillCode).ToList().Sum(p => p.Value);
                            if (AbatementAmount != 0)
                            {
                                debtAbtPurchasePubs.Add(new DebtAbtPurchasePubInput
                                {
                                    MinusDebtCode = debt.BillCode,
                                    AbatementCode = item.BillCode,
                                    AbatementAmount = AbatementAmount
                                });
                            }
                        }
                    }
                    else
                    {
                        var debtTemps = debts.Where(p => p.Value < 0).ToList();
                        if (debtTemps != null && debtTemps.Any())
                        {
                            foreach (var item in debtTemps)
                            {
                                var AbatementAmount = inputs.Where(p => p.BillCode == item.BillCode).ToList().Sum(p => p.Value);
                                if (AbatementAmount != 0)
                                {
                                    debtAbtPurchasePubs.Add(new DebtAbtPurchasePubInput
                                    {
                                        AbatementCode = debt.BillCode,
                                        MinusDebtCode = item.BillCode,
                                        AbatementAmount = AbatementAmount
                                    });
                                }
                            }
                        }
                    }
                    _logger.LogWarning($"拆分的服务费采购：" + debtAbtPurchasePubs.ToJson());
                    await _daprClient.PublishEventAsync<List<DebtAbtPurchasePubInput>>("pubsub-default", "finance-purchase-minusdebtabtpurchase", debtAbtPurchasePubs);
                }
            }
        }

        /// <summary>
        /// 拆分的服务费采购，金额实际付款
        /// </summary>
        /// <param name="debts"></param>
        /// <returns></returns>
        public async Task ServicePaiedPubForSplit(List<DebtPo> debts, List<Guid> detailIds)
        {
            if (debts != null && debts.Any())
            {
                //对应应付单中已付款明细的金额
                var publishBodys = debts.Select(p => new ServiceFeeOrderPaiedInput
                {
                    Code = p.RelateCode,
                    PaymentAmount = p.DebtDetails.Where(w => detailIds.Contains(w.Id)).Sum(t => t.Value),//明细中当前付款的金额
                }).ToList();
                _logger.LogWarning($"拆分的服务费采购：" + publishBodys.ToJson());
                await _daprClient.PublishEventAsync<List<ServiceFeeOrderPaiedInput>>("pubsub-default", "finance-servicepurchase-paied", publishBodys);
            }
        }

        /// <summary>
        /// 寄售转购货应付 通知防腐层
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task OrderPaiedPub(List<GenerateAbtInput> inputs)
        {
            if (inputs != null && inputs.Any())
            {
                var debtDetilIds = inputs.Select(p => p.DebtDetilId).ToList();
                var debtCodeDetailIds = await _debtDetailRepository.GetDebtCodeByDetailIdsAsync(debtDetilIds);
                var pubInput = new List<OrderPaiedInput>();
                foreach (var item in inputs)
                {
                    pubInput.Add(new OrderPaiedInput
                    {
                        DebtCode = debtCodeDetailIds.First(p => p.DebtDetailId == item.DebtDetilId).DebtCode,
                        AbtValue = item.Value,
                        PaymentCode = item.PaymentCode
                    });
                }
                pubInput = pubInput.GroupBy(p => p.DebtCode).Select(p => new OrderPaiedInput
                {
                    DebtCode = p.Key,
                    AbtValue = p.Sum(x => x.AbtValue)
                }).ToList();
                var jsonStr = JsonConvert.SerializeObject(pubInput);
                //_logger.LogWarning($"寄售转购货应付 通知防腐层：" + jsonStr);
                //await _daprClient.PublishEventAsync<List<OrderPaiedInput>>("pubsub-default", "finance-oldsystem-paied", pubInput);
            }
        }

        /// <summary>
        /// 垫资评价 通知
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task AdvancePub(List<DebtDetail> inputs)
        {
            if (inputs != null && inputs.Any())
            {
                var creditIds = inputs.Where(p => p.CreditId.HasValue).Select(p => p.CreditId.Value).ToList();
                var jsonStr = JsonConvert.SerializeObject(creditIds);
                _logger.LogWarning($"垫资评价通知：" + jsonStr);
                //await _daprClient.PublishEventAsync<List<Guid>>("pubsub-default", "finance-finance-advance", creditIds);
            }
        }
        /// <summary>
        /// 正负应付对冲
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="lstInput"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        /// <exception cref="AppServiceException"></exception>
        /// <exception cref="ApplicationException"></exception>
        public async Task<int> GenerateAbtForDebtAsync(Guid debtId, List<GenerateAbtForDebtInput> lstInput, string userName, bool? singleSettle = null)
        {

            if (lstInput.Any(t => t.Value <= 0))
            {
                throw new AppServiceException("冲销金额必须大于0");
            }
            var debt = await _debtRepository.GetWithNoTrackAsync(debtId);
            if (debt == null)
            {
                throw new AppServiceException("应付单不存在");
            }
            if (debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}冲销状态为已完成，不允许再次冲销!");
            }
            // 如果是垫资来的应付，且非自动冲销，限制
            if (!string.IsNullOrEmpty(debt.RelateCode) && debt.RelateCode.Contains("-ADV-") && userName != "AutoAbatementByAdvance")
            {
                throw new AppServiceException($"应付单：{debt.BillCode}来源于提前垫资，不允许手动冲销!");
            }
            if (debt.CompanyId.HasValue)
            {
                await CheckInventoryState(debt.CompanyId.Value);
            }
            var debtNos = lstInput.Select(p => p.BillCode).ToList();
            //var abate_accountPeriodTypes = await _db.DebtDetails.Where(p => p.DebtId == debtId).Select(p => p.AccountPeriodType).Distinct().ToListAsync();
            //if (abate_accountPeriodTypes != null && abate_accountPeriodTypes.Count() == 1)
            //{
            //    var accountPeriodType = abate_accountPeriodTypes.First();
            //    var generateAbt_debtDetails = await _db.Debts.Include(p => p.DebtDetails).Where(p => debtNos.Contains(p.BillCode)).SelectMany(p => p.DebtDetails).ToListAsync();

            //    var generateAbt_accountPeriodTypes = generateAbt_debtDetails.Select(p => p.AccountPeriodType).Distinct().ToList();
            //    if (generateAbt_accountPeriodTypes != null && generateAbt_accountPeriodTypes.Count() == 1)
            //    {
            //        if (accountPeriodType!= generateAbt_accountPeriodTypes.First())
            //        {
            //            throw new AppServiceException($"当前选中应付账期类型为【{((AccountPeriodTypeEnum)accountPeriodType).GetDescription()}账期】，账期不同，无法冲销");
            //        }
            //    }

            //}

            #region 应付单号集合 
            debtNos.Add(debt.BillCode);
            if (debtNos != null && debtNos.Count > 0)
            {
                await CheckForOngoingLossRecognition(debtNos);
            }

            var paymentAutoDetails = await _paymentAutoDetailQueryService.GetAllListAsync(p => p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed);
            if (paymentAutoDetails != null)
            {
                var debtDetilIds = paymentAutoDetails.Select(p => p.DebtDetilId);
                var debtDetails = await _debtDetailQueryService.GetAllListAsync(p => debtDetilIds.Contains(p.Id), new List<string> { "Debt" });
                foreach (var detail in paymentAutoDetails)
                {
                    var debtDetail = debtDetails.Where(p => p.Id == detail.DebtDetilId).First();
                    if (debtNos.Contains(debtDetail.Debt.BillCode))
                    {
                        throw new AppServiceException($"应付单：{debtDetail.Debt.BillCode}在批量付款单中未完成，请在批量付款中完成后操作!");
                    }
                }
            }
            #endregion

            var lstAddAbt = new List<Abatement>();
            var lstUpdateDebt = new List<Debt>();

            var lstAddDebtDetail = new List<DebtDetail>();
            var lstUpdateDebtDetail = new List<DebtDetail>();

            List<Guid> lstDebtIdForRepaireDiff = new List<Guid>();

            if (debt.Value > 0) //正数应付发起
            {
                lstDebtIdForRepaireDiff.Add(debt.Id);

                var thisDebtAbatementValue = (await _abtmentQueryService.GetAllListAsync(p => p.CreditBillCode == debt.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(p => p.Value);
                var leftThisDebtValue = debt.Value - thisDebtAbatementValue;
                if (leftThisDebtValue <= 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于0，不允许操作!");
                }

                if (lstInput.Sum(p => p.Value) > leftThisDebtValue)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于冲销额度，不允许操作!");
                }

                //找出所选单据已存在的冲销记录
                var abatementCodes = lstInput.Select(p => p.BillCode).ToList();
                var abatementInfos = await _abtmentQueryService.GetAllListAsync(p => abatementCodes.Contains(p.DebtBillCode) || abatementCodes.Contains(p.CreditBillCode));

                //所有负数应付单
                var lstDebt = await _debtQueryService.GetAllListAsync(t => abatementCodes.Contains(t.BillCode) && t.Value < 0);


                //查找正数应付对应的DebtDetail
                var lstDebtDetail = await _debtDetailQueryService.GetAllListAsync(t => t.DebtId == debtId && t.Status == Domain.DebtDetailStatusEnum.WaitExecute);

                if (lstDebtDetail.Count <= 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}没有对应的未完成的应付明细，不允许操作!");
                }

                //循环被冲销的负数应付单
                foreach (var item in lstInput)
                {
                    //负数应付单
                    var _debt = lstDebt.FirstOrDefault(q => q.BillCode == item.BillCode);
                    if (_debt == null)
                    {
                        throw new AppServiceException($"应付单：{item.BillCode}不存在!");
                    }
                    if (_debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
                    {
                        throw new AppServiceException($"应付单：{item.BillCode}已经冲销完成，不允许再次冲销!");
                    }
                    item.BillValue = _debt.Value;
                    var entityAbt = new Abatement();

                    var _thisDebtAbatementValue = abatementInfos.Where(q => q.DebtBillCode == item.BillCode || q.CreditBillCode == item.BillCode).Sum(q => q.Value);

                    var _leftThisDebtValue = Math.Abs(_debt.Value) - _thisDebtAbatementValue;
                    var debtPaymentUses = await _debtPaymentUseDetailQueryService.GetAllListAsync(p => p.DebtCode == item.BillCode);
                    if (debtPaymentUses != null && debtPaymentUses.Any())
                    {
                        _leftThisDebtValue = _leftThisDebtValue - debtPaymentUses.Sum(p => p.UseAmount);
                    }
                    if (item.Value > _leftThisDebtValue)
                    {
                        throw new AppServiceException($"应付单：{item.BillCode}剩余额度{_leftThisDebtValue}小于冲销额度，不允许操作!");
                    }

                    entityAbt = new Abatement()
                    {
                        CreditBillCode = debt.BillCode,
                        CreditType = "debt",
                        DebtBillCode = item.BillCode,
                        DebtType = "debt",
                        Value = item.Value,
                        Abtdate = DateTime.Now

                    };
                    entityAbt.CreateBy(userName);
                    lstAddAbt.Add(entityAbt);

                    //负数应付单的冲销金额等于应付单的金额，将应付单的冲销状态改为已完成
                    if (_leftThisDebtValue == item.Value)
                    {
                        _debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                        var _debtDo = _debt.Adapt<Debt>();
                        lstUpdateDebt.Add(_debtDo);
                    }

                    //查找该应付对应的DebtDetail预付账期的，如果金额全部冲销，则将DebtDetail的冲销状态改为已完成,否则改为部分冲销，拆分DebtDetail
                    var _lstDebtDetailForProbablyPay = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.ProbablyPay && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    var abtValue = item.Value; //冲销金额

                    foreach (var detail in _lstDebtDetailForProbablyPay)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;

                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;

                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }


                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取入库账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForStorein = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.StoreIn && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetailForStorein)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;

                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取销售账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForSale = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Sale && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetailForSale)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            //detail.ProbablyPayTime = DateTime.Now;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取回款账期的DebtDetail数据进行冲销
                    var _lstDebtDetail = lstDebtDetail.Where(t => t.DebtId == debt.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Repayment && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetail)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);

                            }


                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;

                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            //detail.ProbablyPayTime = DateTime.Now;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }


                        }
                    }
                }

                if (leftThisDebtValue == lstInput.Sum(p => p.Value))
                {
                    debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                    lstUpdateDebt.Add(debt);
                }

            }
            else //负数应付发起
            {

                var thisDebtAbatementValue = (await _abtmentQueryService.GetAllListAsync(p => p.DebtBillCode == debt.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(p => p.Value);

                var thisDebtAbatementValue2 = (await _abtmentQueryService.GetAllListAsync(p => p.CreditBillCode == debt.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(p => p.Value);

                thisDebtAbatementValue = thisDebtAbatementValue + Math.Abs(thisDebtAbatementValue2);

                var leftThisDebtValue = Math.Abs(debt.Value) - Math.Abs(thisDebtAbatementValue);

                if (leftThisDebtValue <= 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于0，不允许操作!");
                }
                if (lstInput.Sum(p => p.Value) > leftThisDebtValue)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于冲销额度，不允许操作!");

                }

                var _debtPaymentUses = await _debtPaymentUseDetailQueryService.GetAllListAsync(p => p.DebtCode == debt.BillCode);
                if (_debtPaymentUses != null && _debtPaymentUses.Any())
                {
                    leftThisDebtValue = leftThisDebtValue - _debtPaymentUses.Sum(p => p.UseAmount);
                }
                if (leftThisDebtValue < 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}冲销额度不足!");
                }
                if (lstInput.Sum(p => p.Value) > leftThisDebtValue)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度{leftThisDebtValue}小于冲销额度，不允许操作!");
                }


                //正数应付单号
                var debtCodes = lstInput.Select(p => p.BillCode).ToList();

                var debtInfos = await _debtQueryService.GetAllListAsync(p => debtCodes.Contains(p.BillCode) && p.Value > 0);

                var abatementInfos = await _abtmentQueryService.GetAllListAsync(p => debtCodes.Contains(p.CreditBillCode) || debtCodes.Contains(p.DebtBillCode));

                var lstDebtId = debtInfos.Select(t => t.Id).ToList();
                var lstDebtDetail = await _debtDetailQueryService.GetAllListAsync(t => t.DebtId.HasValue && lstDebtId.Contains(t.DebtId.Value) && t.Status == Domain.DebtDetailStatusEnum.WaitExecute);


                foreach (var item in lstInput)
                {
                    //正数应付单
                    var debtInfo = debtInfos.FirstOrDefault(p => p.BillCode == item.BillCode);
                    if (debtInfo == null)
                    {
                        throw new AppServiceException($"应付单：{debtInfo.BillCode}不存在!");
                    }
                    if (debtInfo.AbatedStatus == Domain.AbatedStatusEnum.Abated)
                    {
                        throw new AppServiceException($"应付单：{debtInfo.BillCode}已经冲销完成，不允许再次冲销!");
                    }
                    var leftDebtValue = debtInfo.Value - abatementInfos.Where(q => q.CreditBillCode == item.BillCode || q.DebtBillCode == item.BillCode).Sum(q => q.Value);

                    var debtPaymentUses = await _debtPaymentUseDetailQueryService.GetAllListAsync(p => p.DebtCode == item.BillCode);
                    if (debtPaymentUses != null && debtPaymentUses.Any())
                    {
                        leftDebtValue = leftDebtValue - debtPaymentUses.Sum(p => p.UseAmount);
                    }
                    if (leftDebtValue < 0)
                    {
                        throw new AppServiceException($"应付单：{debtInfo.BillCode}冲销额度不足!");
                    }
                    if (item.Value > leftDebtValue)
                    {
                        throw new AppServiceException($"应付单：{debtInfo.BillCode}剩余额度{leftDebtValue}小于冲销额度，不允许操作!");
                    }

                    lstDebtIdForRepaireDiff.Add(debtInfo.Id);

                    var entityAbt = new Abatement()
                    {
                        CreditBillCode = item.BillCode,
                        CreditType = "debt",
                        DebtBillCode = debt.BillCode,
                        DebtType = "debt",
                        Value = item.Value,
                        Abtdate = DateTime.Now
                    };
                    entityAbt.CreateBy(userName);
                    lstAddAbt.Add(entityAbt);

                    if (leftDebtValue == item.Value)
                    {
                        debtInfo.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                        var debtDo = debtInfo.Adapt<Debt>();
                        lstUpdateDebt.Add(debtDo);
                    }

                    var _lstDebtDetailForProbablyPay = lstDebtDetail.Where(t => t.DebtId == debtInfo.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.ProbablyPay && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();
                    var abtValue = item.Value; //冲销金额

                    foreach (var detail in _lstDebtDetailForProbablyPay)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //lstUpdateDebtDetail.Add(detailDo);
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.Status = detailDo.Status;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = detail.DebtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;
                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);

                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }
                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取入库账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForStorein = lstDebtDetail.Where(t => t.DebtId == debtInfo.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.StoreIn && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetailForStorein)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }
                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = detail.DebtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;
                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }


                        }
                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取销售账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForSale = lstDebtDetail.Where(t => t.DebtId == debtInfo.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Sale && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();
                    //根据时间排序
                    if (_lstDebtDetailForSale != null && _lstDebtDetailForSale.Any())
                    {
                        _lstDebtDetailForSale = _lstDebtDetailForSale.OrderByDescending(x => x.CreatedTime).ToList();
                    }
                    foreach (var detail in _lstDebtDetailForSale)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }
                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = detail.DebtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }


                        }
                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取回款账期的DebtDetail数据进行冲销
                    var _lstDebtDetail = lstDebtDetail.Where(t => t.DebtId == debtInfo.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Repayment && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetail)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;

                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }
                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = detail.DebtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = debt.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }


                        }
                    }

                }

                if (leftThisDebtValue == lstInput.Sum(p => p.Value))
                {
                    debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                    lstUpdateDebt.Add(debt);
                }
            }

            if (lstAddAbt.Count > 0)
            {
                await _abatementRepository.AddManyAsync(lstAddAbt);
            }
            if (lstUpdateDebt.Count > 0)
            {
                // 获取当前被跟踪的所有 DebtPo 实体
                var trackedEntries = _db.ChangeTracker.Entries<DebtPo>()
                    .Where(e => e.State != EntityState.Detached)
                    .ToList();

                // 批量切断跟踪
                foreach (var entry in trackedEntries)
                {
                    entry.State = EntityState.Detached;
                }

                await _debtRepository.UpdateManyAsync(lstUpdateDebt);
            }


            if (lstUpdateDebtDetail.Count > 0)
            {
                await _debtDetailRepository.UpdateManyAsync(lstUpdateDebtDetail);
            }

            if (lstAddDebtDetail.Count > 0)
            {
                await _debtDetailRepository.AddManyAsync(lstAddDebtDetail);
            }
            await PushPaymentSettlement_KD(debt, lstInput, singleSettle: singleSettle);
  
            int nRes = await _unitOfWork.CommitAsync();

            lstDebtIdForRepaireDiff = lstDebtIdForRepaireDiff.Distinct().ToList();
            await _debtRepository.RepaireDebtDiff(lstDebtIdForRepaireDiff);

            //应付冲销发送消息
            var lstDebts = await _debtQueryService.GetAllListAsync(t => t.BillCode != null && debtNos.Contains(t.BillCode));
            var lstDebtIds = lstDebts.Select(t => t.Id).Distinct().ToList();
            //await _daprClient.PublishEventAsync<List<Guid>>("pubsub-default", "finance-finance-advance", lstDebtIds);
            return nRes;
        }

        /// <summary>
        /// 服务费应收冲应付
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="lstInput"></param>
        /// <param name="userName"></param>
        /// <param name="singleSettle"></param>
        /// <returns></returns>
        public async Task<int> GenerateAbtForDebtToCreditAsync(Guid debtId, List<GenerateAbtForDebtInput> lstInput, string userName, bool? singleSettle = null)
        {
            if (lstInput.Any(t => t.Value <= 0))
            {
                throw new AppServiceException("冲销金额必须大于0");
            }
            var debt = await _debtRepository.GetWithNoTrackAsync(debtId);
            if (debt == null)
            {
                throw new AppServiceException("应付单不存在");
            }
            if (debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}冲销状态为已完成，不允许再次冲销!");
            }

            if (debt.CompanyId.HasValue)
            {
                 await CheckInventoryState(debt.CompanyId.Value);
            }
            #region 应付单号集合 
            var debtNos = lstInput.Select(p => p.BillCode).ToList();
            debtNos.Add(debt.BillCode);

            if (debtNos != null && debtNos.Count > 0)
            {
                await CheckForOngoingLossRecognition(debtNos);
            }

            var paymentAutoDetails = await _paymentAutoDetailQueryService.GetAllListAsync(p => p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed);
            if (paymentAutoDetails != null)
            {
                var debtDetilIds = paymentAutoDetails.Select(p => p.DebtDetilId);
                var debtDetails = await _debtDetailQueryService.GetAllListAsync(p => debtDetilIds.Contains(p.Id), new List<string> { "Debt" });
                foreach (var detail in paymentAutoDetails)
                {
                    var debtDetail = debtDetails.Where(p => p.Id == detail.DebtDetilId).First();
                    if (debtNos.Contains(debtDetail.Debt.BillCode))
                    {
                        throw new AppServiceException($"应付单：{debtDetail.Debt.BillCode}在批量付款单中未完成，请在批量付款中完成后操作!");
                    }
                }
            }
            #endregion

            var lstAddAbt = new List<Abatement>();
            var lstUpdateCredit = new List<Credit>();

            var lstUpdatedebt = new List<Debt>();
            var lstAddDebtDetail = new List<DebtDetail>();
            var lstUpdateDebtDetail = new List<DebtDetail>();
            List<Guid> lstDebtIdForRepaireDiff = new List<Guid>();
            //正数应付发起
            if (debt.Value > 0)
            {
                var thisDebtAbatementValue = (await _abtmentQueryService.GetAllListAsync(p => p.CreditBillCode == debt.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(p => p.Value);
                var leftThisDebtValue = debt.Value - thisDebtAbatementValue;
                if (leftThisDebtValue <= 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于0，不允许操作!");
                }
                if (lstInput.Sum(p => p.Value) > leftThisDebtValue)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}剩余额度小于冲销额度，不允许操作!");
                }

                //找出所选单据已存在的冲销记录
                var abatementCodes = lstInput.Select(p => p.BillCode).ToList();
                var abatementInfos = await _abtmentQueryService.GetAllListAsync(p => abatementCodes.Contains(p.DebtBillCode) || abatementCodes.Contains(p.CreditBillCode));

                //所有应收单
                var lstCredit = await _creditQueryService.GetAllListAsync(t => abatementCodes.Contains(t.BillCode) && t.Value > 0);


                //查找正数应付对应的DebtDetail
                var lstDebtDetail = await _debtDetailQueryService.GetAllListAsync(t => t.DebtId == debtId && t.Status == Domain.DebtDetailStatusEnum.WaitExecute);

                if (lstDebtDetail.Count <= 0)
                {
                    throw new AppServiceException($"应付单：{debt.BillCode}没有对应的未完成的应付明细，不允许操作!");
                }

                //循环被冲销的服务费应收
                foreach (var item in lstInput)
                {
                    //应收单
                    var credit = lstCredit.FirstOrDefault(q => q.BillCode == item.BillCode);
                    if (credit == null)
                    {
                        throw new AppServiceException($"应收单：{item.BillCode}不存在!");
                    }
                    if (credit.AbatedStatus == Domain.AbatedStatusEnum.Abated)
                    {
                        throw new AppServiceException($"应收单：{item.BillCode}已经冲销完成，不允许再次冲销!");
                    }
                    item.BillValue = credit.Value;
                    var entityAbt = new Abatement();
                    var _thisDebtAbatementValue = abatementInfos.Where(q => q.DebtBillCode == item.BillCode).Sum(q => q.Value);
                    var _leftThisDebtValue = Math.Abs(credit.Value) - _thisDebtAbatementValue;
                    var debtPaymentUses = await _debtPaymentUseDetailQueryService.GetAllListAsync(p => p.DebtCode == item.BillCode);
                    if (debtPaymentUses != null && debtPaymentUses.Any())
                    {
                        _leftThisDebtValue = _leftThisDebtValue - debtPaymentUses.Sum(p => p.UseAmount);
                    }
                    if (item.Value > _leftThisDebtValue)
                    {
                        throw new AppServiceException($"应收单：{item.BillCode}剩余额度{_leftThisDebtValue}小于冲销额度，不允许操作!");
                    }
                    entityAbt = new Abatement()
                    {
                        CreditBillCode = debt.BillCode,
                        CreditType = "debt",
                        DebtBillCode = item.BillCode,
                        DebtType = "credit",
                        Value = item.Value,
                        Abtdate = DateTime.Now

                    };
                    entityAbt.CreateBy(userName);
                    lstAddAbt.Add(entityAbt);

                    //负数应付单的冲销金额等于应付单的金额，将应付单的冲销状态改为已完成
                    if (_leftThisDebtValue == item.Value)
                    {
                        credit.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                        var creditDo = credit.Adapt<Credit>();
                        lstUpdateCredit.Add(creditDo);
                    }
                    //查找该应付对应的DebtDetail预付账期的，如果金额全部冲销，则将DebtDetail的冲销状态改为已完成,否则改为部分冲销，拆分DebtDetail
                    var _lstDebtDetailForProbablyPay = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.ProbablyPay && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    var abtValue = item.Value; //冲销金额

                    foreach (var detail in _lstDebtDetailForProbablyPay)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;

                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;

                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }


                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取入库账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForStorein = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.StoreIn && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetailForStorein)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;

                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取销售账期的DebtDetail数据进行冲销
                    var _lstDebtDetailForSale = lstDebtDetail.Where(t => t.DebtId == debtId && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Sale && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetailForSale)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();


                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            //if (lstUpdateDebtDetail.Contains(detailDo) == false)
                            //{
                            //    lstUpdateDebtDetail.Add(detailDo);
                            //}

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }

                            abtValue = 0;

                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();
                            //lstUpdateDebtDetail.Add(detailDo);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;
                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            //detail.ProbablyPayTime = DateTime.Now;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;

                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }
                        }


                    }

                    if (abtValue <= 0)
                    {
                        continue;
                    }

                    //如果abtValue>0,说明还有剩余的冲销金额，需要读取回款账期的DebtDetail数据进行冲销
                    var _lstDebtDetail = lstDebtDetail.Where(t => t.DebtId == debt.Id && t.AccountPeriodType == (int)Domain.AccountPeriodTypeEnum.Repayment && t.Status == (int)DebtDetailStatusEnum.WaitExecute).ToList();

                    foreach (var detail in _lstDebtDetail)
                    {
                        if (abtValue == detail.Value) //冲销金额等于循环的应付明细金额
                        {
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            //detail.ProbablyPayTime = DateTime.Now;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;

                            detailDo.AddDebtDetailExcute(excute, userName);

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);

                            }


                            abtValue = 0;
                            break;
                        }
                        else if (abtValue < detail.Value) //冲销金额小于循环的应付明细金额
                        {
                            detail.Value = detail.Value - abtValue;
                            if (detail.CostDiscount.HasValue)
                            {
                                if (detail.CostDiscount != 0)
                                {
                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                }
                            }
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.Value = detailDo.Value;
                                _updateDebtDetail.OriginValue = detailDo.OriginValue;

                            }

                            //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                            Domain.AggregateRoot.DebtDetailAggregate.DebtDetail _newDebtDetail = new Domain.AggregateRoot.DebtDetailAggregate.DebtDetail();
                            _newDebtDetail.Id = Guid.NewGuid();
                            _newDebtDetail.Code = detail.Code;
                            _newDebtDetail.DebtId = debtId;
                            _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                            _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                            _newDebtDetail.Discount = detail.Discount;
                            _newDebtDetail.CreditId = detail.CreditId;
                            _newDebtDetail.ReceiveCode = detail.ReceiveCode;
                            if (detail.CostDiscount.HasValue && detail.CostDiscount.Value != 0)
                            {
                                _newDebtDetail.OriginValue = (abtValue / (detail.CostDiscount / 100));
                            }

                            _newDebtDetail.Value = abtValue;
                            _newDebtDetail.Status = Domain.DebtDetailStatusEnum.Completed;
                            _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                            _newDebtDetail.OrderNo = detail.OrderNo;
                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                            _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                            _newDebtDetail.CostDiscount = detail.CostDiscount;
                            _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                            _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                            _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                            _newDebtDetail.TaxDiscount = detail.TaxDiscount;

                            // _newDebtDetail.CreatedTime = DateTime.Now;
                            _newDebtDetail.CreateBy(userName);

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = _newDebtDetail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = abtValue;

                            _newDebtDetail.AddDebtDetailExcute(excute, userName);

                            lstAddDebtDetail.Add(_newDebtDetail);
                            abtValue = 0;
                            break;
                        }
                        else if (abtValue > detail.Value)//冲销金额大于循环的应付明细金额
                        {
                            abtValue = abtValue - detail.Value;
                            //detail.ProbablyPayTime = DateTime.Now;
                            detail.Status = Domain.DebtDetailStatusEnum.Completed;
                            var detailDo = detail.Adapt<Domain.AggregateRoot.DebtDetailAggregate.DebtDetail>();

                            DebtDetailExcute excute = new DebtDetailExcute();
                            excute.DebtDetailId = detail.Id;
                            excute.PaymentCode = item.BillCode;
                            excute.PaymentDate = DateTime.Now;
                            excute.Value = detail.Value;
                            detailDo.AddDebtDetailExcute(excute, userName);
                            if (lstUpdateDebtDetail.Exists(t => t.Id == detailDo.Id) == false)
                            {
                                lstUpdateDebtDetail.Add(detailDo);
                            }
                            else
                            {
                                var _updateDebtDetail = lstUpdateDebtDetail.Where(t => t.Id == detail.Id).FirstOrDefault();
                                _updateDebtDetail.ProbablyPayTime = detailDo.ProbablyPayTime;
                                _updateDebtDetail.Status = detailDo.Status;
                                _updateDebtDetail.AddDebtDetailExcute(excute, userName);
                            }


                        }
                    }
                }
                if (leftThisDebtValue == lstInput.Sum(p => p.Value))
                {
                    debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                    lstUpdatedebt.Add(debt);
                }

            }
            if (lstAddAbt.Count > 0)
            {
                await _abatementRepository.AddManyAsync(lstAddAbt);
            }
            if (lstUpdatedebt.Count > 0)
            {
                await _debtRepository.UpdateManyAsync(lstUpdatedebt);
            }
            if (lstUpdateCredit.Count > 0)
            {
                await _creditRepository.UpdateManyAsync(lstUpdateCredit);
            }

            if (lstUpdateDebtDetail.Count > 0)
            {
                await _debtDetailRepository.UpdateManyAsync(lstUpdateDebtDetail);
            }

            if (lstAddDebtDetail.Count > 0)
            {
                await _debtDetailRepository.AddManyAsync(lstAddDebtDetail);
            }
        
            await PushPaymentSettlement_KD(debt, lstInput, "credit", singleSettle);
            int nRes = await _unitOfWork.CommitAsync();
            return nRes;
        }

        /// <summary>
        /// 收款冲应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        public async Task<int> GenerateAbtForCreditAsync(GenerateAbtForCreditInput input)
        {
            var userName = input.userName;
            if (input.lstAbtDetail.Count <= 0)
            {
                throw new AppServiceException("冲销明细不能为空!");
            }

            if (input.lstAbtDetail.Any(t => t.Value <= 0))
            {
                throw new AppServiceException("含有冲销金额小于等于0的明细,冲销金额必须大于0!");
            }

            var lstCreditCode = input.lstAbtDetail.Select(p => p.BillCode).Distinct().ToList();

            var lstCredit = await _creditQueryService.GetAllListAsync(t => lstCreditCode.Contains(t.BillCode));

            if (lstCreditCode.Count != lstCredit.Count)
            {
                throw new AppServiceException("含有不存在的应收单!");
            }
            //查出应收单列表对应的已冲销信息列表
            var existThisCreditAbatementInfos = await _abtmentQueryService.GetAllListAsync(p => lstCreditCode.Contains(p.DebtBillCode));

            var existThisCreditAbatementInfos2 = await _abtmentQueryService.GetAllListAsync(p => lstCreditCode.Contains(p.CreditBillCode));

            existThisCreditAbatementInfos.AddRange(existThisCreditAbatementInfos2);

            var lstCreditId = lstCredit.Select(t => t.Id).Distinct().ToList();
            var companyId = lstCredit.First().CompanyId;
            //查询出应付明细 关联该应收单的，并且状态为待执行的
            var debtDetails = await _debtDetailQueryService.GetAllListAsync(t =>
            t.CreditId.HasValue
            && lstCreditId.Contains(t.CreditId.Value)
            && t.Status == Domain.DebtDetailStatusEnum.WaitExecute
            && string.IsNullOrWhiteSpace(t.ReceiveCode));
            var debtDetailIds = debtDetails.Select(p => p.Id).ToList();
            //过滤掉批量付款中的付款计划
            var paymentAutoDetailIds = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem).Where(p =>
            debtDetailIds.Contains(p.DebtDetilId) &&
            p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed &&
            p.PaymentAutoItem.CompanyId == companyId).Select(p => p.DebtDetilId).ToListAsync();

            debtDetails = debtDetails.Where(p => !paymentAutoDetailIds.Contains(p.Id)).ToList();

            ////从debtDetails 查询出回款账期的应付明细
            //var lstDebtDetail = debtDetails.Where(t => t.CreditId.HasValue && t.AccountPeriodType==(int)AccountPeriodTypeEnum.Repayment && lstCreditId.Contains(t.CreditId.Value) && t.Status == Domain.DebtDetailStatusEnum.WaitExecute && t.ProbablyPayTime.HasValue == false&&string.IsNullOrWhiteSpace(t.ReceiveCode)).ToList();

            ////从debtDetails 查询出销售账期的应付明细
            //var lstDebtDetailForSale = debtDetails.Where(t => t.CreditId.HasValue && t.AccountPeriodType == (int)AccountPeriodTypeEnum.Sale && lstCreditId.Contains(t.CreditId.Value) && t.Status == Domain.DebtDetailStatusEnum.WaitExecute&&string.IsNullOrWhiteSpace(t.ReceiveCode));

            var lstUpdateCredit = new List<Credit>();
            var lstAddAbt = new List<Abatement>();
            var lstUpdateDebtDetail = new List<DebtDetail>();
            var lstAddDebtDetail = new List<DebtDetail>();

            List<Guid> lstDebtIdForRepaireDiff = new List<Guid>();
            int nRes = 0;
            try
            {
                if (input.BillType == "receive") //收款冲应收
                {
                    //查出该收款单已冲销的金额
                    var existThisReceiveAbatementValue = (await _abtmentQueryService.GetAllListAsync(p => p.CreditBillCode == input.Code, null, q => new AbatementPo { Value = q.Value })).Sum(p => p.Value);

                    //计算该收款单的可冲销余额（收款单金额-该收款单的已冲销金额）
                    //var leftThisReceiveValue = Math.Abs(input.Value) - Math.Abs(existThisReceiveAbatementValue);
                    var receiveAmount = 0M;
                    var recognizeReceiveItem = await _db.RecognizeReceiveItems.Include(p => p.RecognizeReceiveDetails).FirstOrDefaultAsync(p => p.Code == input.RecognizeReceiveCode);
                    var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == recognizeReceiveItem.Id).AsNoTracking().ToListAsync();
                    //是否由暂收款转货款
                    bool isTempToRecognizeReceive = false;
                    if (recognizeReceiveItem != null)
                    {
                        if (recognizeReceiveItem.Classify == RecognizeReceiveClassifyEnum.Goods && !string.IsNullOrEmpty(recognizeReceiveItem.RelateCode))
                        {
                            isTempToRecognizeReceive = true;
                        }
                        var sysMonth = await _bDSApiClient.GetSystemMonth(recognizeReceiveItem.CompanyId);
                        sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
                        await CheckSysMonth(Guid.Parse(recognizeReceiveItem.CompanyId), sysMonth);
                        receiveAmount = recognizeReceiveItem.ReceiveValue;
                    }
                    else
                    {
                        receiveAmount = Math.Abs(input.Value);
                    }
                    var leftThisReceiveValue = Math.Abs(receiveAmount);
                    //由暂收款转货款提交时会校验住，故不作额外判断 
                    if (!isTempToRecognizeReceive)
                    {
                        leftThisReceiveValue = Math.Abs(receiveAmount) - Math.Abs(existThisReceiveAbatementValue);
                    }
                    //计算本次要冲销的金额
                    var thisAbatementTotalValue = input.lstAbtDetail.Sum(p => p.Value);

                    if (Math.Abs(thisAbatementTotalValue) > Math.Abs(leftThisReceiveValue))
                    {
                        throw new AppServiceException("冲销金额大于该收款单的可冲销余额!");
                    }

                    if (thisAbatementTotalValue == 0)
                    {
                        throw new AppServiceException("冲销金额不能为0!");
                    }

                    var lstPurchaseCode = debtDetails.Select(p => p.PurchaseCode).Distinct().ToList();

                    var lstPurchasePlan = await _purchasePayPlanQueryService.GetAllListAsync(t => lstPurchaseCode.Contains(t.PurchaseCode));

                    foreach (var credit in lstCredit)
                    {
                        //该应收单的已冲销金额
                        var thisCreditAbatementValue = existThisCreditAbatementInfos.Where(q => q.DebtBillCode == credit.BillCode).Sum(q => q.Value);

                        //该应收单的已冲销金额  有可能是 负数应收 所以要判断 CreditBillCode (正负应收冲销情况)
                        var thisCreditAbatementValue2 = existThisCreditAbatementInfos.Where(q => q.CreditBillCode == credit.BillCode).Sum(q => q.Value);

                        thisCreditAbatementValue = Math.Abs(thisCreditAbatementValue) + Math.Abs(thisCreditAbatementValue2);

                        //找出该应收单本次要冲销的金额
                        var lstabt = input.lstAbtDetail.Where(q => q.BillCode == credit.BillCode).ToList();
                        var thisAbtDetailValue = lstabt.Sum(t => t.Value);

                        if (thisCreditAbatementValue + Math.Abs(thisAbtDetailValue) > Math.Abs(credit.Value))
                        {
                            throw new AppServiceException($"应收单:{credit.BillCode}的已冲销金额:{thisCreditAbatementValue}+本次该应收单要冲销的金额:{thisAbtDetailValue}大于该应收单的总金额：{credit.Value}");
                        }
                        if (thisCreditAbatementValue + Math.Abs(thisAbtDetailValue) >= Math.Abs(credit.Value))
                        { //该应收单的已冲销金额+本次该应收单要冲销的金额==该应收单的总金额
                          //这时需要改该应收单的冲销状态为1，表示已冲销完成
                            credit.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                            lstUpdateCredit.Add(credit.Adapt<Credit>());
                        }

                        var currentCurrentValueByCredit = rrdcs.Where(x=>x.CreditCode == credit.BillCode).Sum(x => x.CurrentValue);
                        if (currentCurrentValueByCredit != thisAbtDetailValue)
                        {
                            throw new AppServiceException($"应收单:{credit.BillCode}的在当前认款单中的认款金额:{currentCurrentValueByCredit}不等于本次该应收单要冲销的金额{thisAbtDetailValue}");
                        }

                        //写入冲销表abatement
                        var entityAbt = new Abatement()
                        {
                            CreditBillCode = input.Code,
                            CreditType = "receive",
                            DebtBillCode = credit.BillCode,
                            DebtType = "credit",
                            Value = thisAbtDetailValue,
                            Abtdate = DateTime.Now,
                            RecognizeReceiveCode = input.RecognizeReceiveCode

                        };
                        entityAbt.CreateBy(userName);
                        lstAddAbt.Add(entityAbt);

                        //处理DebtDetail 表，如果是全额回款，写上预计付款时间（为当前冲销时间），如果是部分回款，需要拆分DebtDetail
                        var _lstDebtDetail = debtDetails.Where(t => t.CreditId == credit.Id).ToList();
                        if (_lstDebtDetail.Count > 0)
                        {
                            lstDebtIdForRepaireDiff.AddRange(_lstDebtDetail.Select(t => t.DebtId.Value).Distinct().ToList());

                            if (credit.AbatedStatus == Domain.AbatedStatusEnum.Abated)  //全额回款
                            {
                                foreach (var detail in _lstDebtDetail)
                                {
                                    detail.BackPayTime = DateTime.Now;
                                    detail.ReceiveCode = input.Code;
                                    detail.RecognizeReceiveCode = input.RecognizeReceiveCode;
                                    if (detail.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment)
                                    {
                                        //查找账期天数
                                        var accountPeriodTypeEnum = (Domain.AccountPeriodTypeEnum)detail.AccountPeriodType;
                                        var day = detail.AccountPeriodDays;
                                        PurchasePayPlanPo? purchasePlan = day.HasValue && day.Value >= 0 ? lstPurchasePlan.FirstOrDefault(t => t.PurchaseCode == detail.PurchaseCode && t.AccountPeriodType == accountPeriodTypeEnum && t.AccountPeriodDays == day.Value) : lstPurchasePlan.FirstOrDefault(t => t.PurchaseCode == detail.PurchaseCode && t.AccountPeriodType == accountPeriodTypeEnum);
                                        if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                        {
                                            var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                            detail.ProbablyPayTime = DateTime.Now.AddDays(accountPeriodDays);
                                        }
                                        else
                                        {
                                            if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                            {
                                                detail.ProbablyPayTime = DateTime.Now.AddDays(purchasePlan.AccountPeriodDays.Value);
                                            }
                                            else
                                            {
                                                detail.ProbablyPayTime = DateTime.Now;
                                            }
                                        }
                                        var credittemp = lstCredit.Where(p => p.Id == detail.CreditId).FirstOrDefault();
                                        if (credittemp != null)
                                        {
                                            var abtSubmitAbatement = input.lstAbtDetail.Where(p => p.BillCode == credittemp.BillCode).FirstOrDefault();
                                            if (abtSubmitAbatement != null)
                                            {
                                                var recognizeReceiveDetail = recognizeReceiveItem.RecognizeReceiveDetails.Where(p => p.Code == abtSubmitAbatement.Code).FirstOrDefault();
                                                if (recognizeReceiveDetail != null && recognizeReceiveDetail.BackDateTime.HasValue)
                                                {
                                                    if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                                    {
                                                        var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                                        detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value.AddDays(accountPeriodDays);
                                                    }
                                                    else
                                                    {
                                                        if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                                        {
                                                            detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value.AddDays(purchasePlan.AccountPeriodDays.Value);
                                                        }
                                                        else
                                                        {
                                                            detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value;
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    detail.BackPayTime = detail.ProbablyPayTime;
                                                }
                                            }
                                        }
                                        detail.Settletype = recognizeReceiveItem.Settletype;
                                        detail.DraftBillExpireDate = recognizeReceiveItem.DiscountDate ??= recognizeReceiveItem.DraftBillExpireDate;
                                        detail.ReceiveCode = input.Code;
                                    }
                                    else if (detail.AccountPeriodType == (int)AccountPeriodTypeEnum.Sale) //销售账期
                                    {
                                        detail.ReceiveCode = input.Code;
                                    }
                                    var detailDo = detail.Adapt<DebtDetail>();
                                    detailDo.UpdateBy(userName);
                                    lstUpdateDebtDetail.Add(detailDo);
                                }
                            }
                            else //收款冲销金额小于应收单金额的情况(部分回款)
                            {
                                var debtDetailGroupByDebtId = _lstDebtDetail.GroupBy(p => p.DebtId).ToList();
                                var debtIds = debtDetailGroupByDebtId.Select(p => p.Key).ToList();

                                var debts = await _db.Debts.Include(p => p.DebtDetails).Where(p => debtIds.Contains(p.Id)).AsNoTracking().ToListAsync();

                                foreach (var debtDetailGroup in debtDetailGroupByDebtId)
                                {
                                    var ReceiveValue = input.lstAbtDetail.Where(p => p.BillCode == credit.BillCode).Sum(p => p.Value);
                                    //比例
                                    decimal per = ReceiveValue / credit.Value;
                                    var tempDebtDetails = debts.First(p => p.Id == debtDetailGroup.Key).DebtDetails.Where(p => p.CreditId == credit.Id).ToList();
                                    var thisPayValue = tempDebtDetails.Sum(p => p.Value) * per;
                                    var tempDetails = debtDetailGroup.OrderByDescending(p => p.AccountPeriodType).ThenBy(p => p.Value).ToList();
                                    foreach (var detail in tempDetails)
                                    {
                                        detail.ReceiveCode = input.Code;
                                        detail.RecognizeReceiveCode = input.RecognizeReceiveCode;
                                        var leftPayValue = detail.Value - thisPayValue;
                                        //查找账期天数
                                        var accountPeriodTypeEnum = (AccountPeriodTypeEnum)detail.AccountPeriodType;
                                        var day = detail.AccountPeriodDays;
                                        PurchasePayPlanPo? purchasePlan = day.HasValue && day.Value >= 0 ? lstPurchasePlan.FirstOrDefault(t => t.PurchaseCode == detail.PurchaseCode && t.AccountPeriodType == accountPeriodTypeEnum && t.AccountPeriodDays == day.Value) : lstPurchasePlan.FirstOrDefault(t => t.PurchaseCode == detail.PurchaseCode && t.AccountPeriodType == accountPeriodTypeEnum);

                                        if (leftPayValue <= 0) //可以付款
                                        {
                                            if (detail.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment)
                                            {
                                                if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                                {
                                                    var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                                    detail.ProbablyPayTime = DateTime.Now.AddDays(accountPeriodDays);
                                                }
                                                else
                                                {
                                                    if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                                    {
                                                        detail.ProbablyPayTime = DateTime.Now.AddDays(purchasePlan.AccountPeriodDays.Value);
                                                    }
                                                    else
                                                    {
                                                        detail.ProbablyPayTime = DateTime.Now;
                                                    }
                                                }
                                                var credittemp = lstCredit.Where(p => p.Id == detail.CreditId).FirstOrDefault();
                                                if (credittemp != null)
                                                {
                                                    var abtSubmitAbatement = input.lstAbtDetail.Where(p => p.BillCode == credittemp.BillCode).FirstOrDefault();
                                                    if (abtSubmitAbatement != null)
                                                    {
                                                        var recognizeReceiveDetail = recognizeReceiveItem.RecognizeReceiveDetails.Where(p => p.Code == abtSubmitAbatement.Code).FirstOrDefault();
                                                        if (recognizeReceiveDetail != null && recognizeReceiveDetail.BackDateTime.HasValue)
                                                        {
                                                            if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                                            {
                                                                var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                                                detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value.AddDays(accountPeriodDays);
                                                            }
                                                            else
                                                            {
                                                                if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                                                {
                                                                    detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value.AddDays(purchasePlan.AccountPeriodDays.Value);
                                                                }
                                                                else
                                                                {
                                                                    detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value;
                                                                }
                                                            }
                                                        }
                                                        else
                                                        {
                                                            detail.BackPayTime = detail.ProbablyPayTime;
                                                        }
                                                    }
                                                }
                                                detail.Settletype = recognizeReceiveItem.Settletype;
                                                detail.DraftBillExpireDate = recognizeReceiveItem.DiscountDate ??= recognizeReceiveItem.DraftBillExpireDate;
                                            }
                                            if (detail.CostDiscount.HasValue)
                                            {
                                                if (detail.CostDiscount != 0)
                                                {
                                                    detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                                }
                                            }
                                            var detailDo2 = detail.Adapt<DebtDetail>();
                                            lstUpdateDebtDetail.Add(detailDo2);
                                            thisPayValue = thisPayValue - detail.Value;
                                            continue;
                                        }

                                        detail.Value = thisPayValue;
                                        //可以付款
                                        if (detail.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment)
                                        {
                                            if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                            {
                                                var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                                detail.ProbablyPayTime = DateTime.Now.AddDays(accountPeriodDays);
                                            }
                                            else
                                            {
                                                if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                                {
                                                    detail.ProbablyPayTime = DateTime.Now.AddDays(purchasePlan.AccountPeriodDays.Value);
                                                }
                                                else
                                                {
                                                    detail.ProbablyPayTime = DateTime.Now;
                                                }
                                            }
                                            var credittemp = lstCredit.Where(p => p.Id == detail.CreditId).FirstOrDefault();
                                            if (credittemp != null)
                                            {
                                                var abtSubmitAbatement = input.lstAbtDetail.Where(p => p.BillCode == credittemp.BillCode).FirstOrDefault();
                                                if (abtSubmitAbatement != null)
                                                {
                                                    var recognizeReceiveDetail = recognizeReceiveItem.RecognizeReceiveDetails.Where(p => p.Code == abtSubmitAbatement.Code).FirstOrDefault();
                                                    if (recognizeReceiveDetail != null && recognizeReceiveDetail.BackDateTime.HasValue)
                                                    {
                                                        if (detail.AccountPeriodDays.HasValue && detail.AccountPeriodDays.Value > 0)
                                                        {
                                                            var accountPeriodDays = double.Parse(detail.AccountPeriodDays.Value.ToString());
                                                            detail.BackPayTime = DateTime.Now.AddDays(accountPeriodDays);
                                                        }
                                                        else
                                                        {
                                                            if (purchasePlan != null && purchasePlan.AccountPeriodDays.HasValue)
                                                            {
                                                                detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value.AddDays(purchasePlan.AccountPeriodDays.Value);
                                                            }
                                                            else
                                                            {
                                                                detail.BackPayTime = recognizeReceiveDetail.BackDateTime.Value;
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        detail.BackPayTime = detail.ProbablyPayTime;
                                                    }
                                                }
                                            }
                                            detail.Settletype = recognizeReceiveItem.Settletype;
                                            detail.DraftBillExpireDate = recognizeReceiveItem.DiscountDate ??= recognizeReceiveItem.DraftBillExpireDate;
                                        }

                                        if (detail.CostDiscount.HasValue)
                                        {
                                            if (detail.CostDiscount != 0)
                                            {
                                                detail.OriginValue = (detail.Value / (detail.CostDiscount / 100));
                                            }
                                        }
                                        var detailDo = detail.Adapt<DebtDetail>();
                                        lstUpdateDebtDetail.Add(detailDo);

                                        //写入应付明细
                                        //拆分出一条DebtDetail数据，状态为已完成,金额为冲销金额
                                        DebtDetail _newDebtDetail = new DebtDetail();
                                        _newDebtDetail.Id = Guid.NewGuid();
                                        _newDebtDetail.Code = detail.Code;
                                        _newDebtDetail.DebtId = detail.DebtId;
                                        _newDebtDetail.AccountPeriodType = detailDo.AccountPeriodType;
                                        _newDebtDetail.AccountPeriodDays = detailDo.AccountPeriodDays;
                                        if (detail.AccountPeriodType == (int)AccountPeriodTypeEnum.Sale)
                                        {
                                            _newDebtDetail.ProbablyPayTime = detail.ProbablyPayTime;
                                        }
                                        _newDebtDetail.CreditId = detail.CreditId;
                                        _newDebtDetail.Discount = detail.Discount;
                                        if (detail.CostDiscount.HasValue)
                                        {
                                            if (detail.CostDiscount != 0)
                                            {
                                                _newDebtDetail.OriginValue = (leftPayValue / (detail.CostDiscount / 100));
                                            }
                                        }

                                        _newDebtDetail.Value = leftPayValue;
                                        _newDebtDetail.Status = Domain.DebtDetailStatusEnum.WaitExecute;
                                        _newDebtDetail.PurchaseCode = detail.PurchaseCode;
                                        _newDebtDetail.OrderNo = detail.OrderNo;
                                        _newDebtDetail.IsInvoiceReceipt = detail.IsInvoiceReceipt;
                                        _newDebtDetail.CostDiscount = detail.CostDiscount;
                                        _newDebtDetail.DistributionDiscount = detail.DistributionDiscount;
                                        _newDebtDetail.FinanceDiscount = detail.FinanceDiscount;
                                        _newDebtDetail.SpdDiscount = detail.SpdDiscount;
                                        _newDebtDetail.TaxDiscount = detail.TaxDiscount;
                                        _newDebtDetail.CreateBy(userName);
                                        if (_newDebtDetail.Value > 0)
                                        {
                                            lstAddDebtDetail.Add(_newDebtDetail);
                                        }
                                        break;
                                    }
                                }

                            }
                        }
                    }
                    //回写预收应收认款数据
                    var addrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    var updrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    if (recognizeReceiveItem != null &&
                        recognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled &&
                        recognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.WaitSubmit &&
                        input.lstAbtDetail != null &&
                        input.lstAbtDetail.Any())
                    {
                        if (rrdcs != null && rrdcs.Any())
                        {
                            //foreach (var rrdc in rrdcs)
                            //{
                            //    var rrd = recognizeReceiveItem.RecognizeReceiveDetails.FirstOrDefault(x => x.Id == rrdc.RecognizeReceiveDetailId);
                            //    var single = input.lstAbtDetail.FirstOrDefault(x => x.Code == rrd.Code && x.BillCode == rrdc.CreditCode);
                            //    if (single != null)
                            //    {
                            //        var credit = lstCredit.FirstOrDefault(x => x.BillCode == single.BillCode);
                            //        //更新
                            //        rrdc.CurrentValue = single.Value;
                            //        rrdc.CreditCode = single.BillCode;
                            //        rrdc.CreditId = credit != null ? credit.Id : null;
                            //        updrrdcs.Add(rrdc);
                            //    }
                            //}
                        }
                        else
                        {
                            //针对未结算的历史数据根据金蝶返回写入
                            foreach (var item in input.lstAbtDetail)
                            {
                                var rrd = recognizeReceiveItem.RecognizeReceiveDetails.FirstOrDefault(x => x.Code == item.Code);
                                var credit = lstCredit.FirstOrDefault(x => x.BillCode == item.BillCode);
                                if (rrd != null)
                                {
                                    var single = new RecognizeReceiveDetailCreditPo();
                                    single.Id = Guid.NewGuid();
                                    single.RecognizeReceiveDetailId = rrd.Id;
                                    single.CurrentValue = item.Value;
                                    single.CreditId = credit != null ? credit.Id : null;
                                    single.CreatedBy = userName;
                                    single.CreditCode = item.BillCode;
                                    single.InvoiceNo = rrd.Type == 1 ? rrd.Code : null;
                                    single.RecognizeReceiveItemId = recognizeReceiveItem.Id;
                                    single.OrderNo = rrd.Type == 2 ? rrd.Code : null;
                                    addrrdcs.Add(single);
                                }
                            }
                        }
                    }
                    if (addrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.AddRange(addrrdcs);
                    }
                    if (updrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.UpdateRange(updrrdcs);
                    }
                }
                else if (input.BillType == "credit") //正负应收对冲
                {
                    var credit = (await _creditQueryService.GetAllListAsync(t => t.BillCode == input.Code)).FirstOrDefault();
                    if (credit == null)
                    {
                        throw new AppServiceException("执行冲销的应收单不存在");
                    }
                    if (credit.AbatedStatus == Domain.AbatedStatusEnum.Abated)
                    {
                        throw new AppServiceException($"应收单:{credit.BillCode}已冲销完成");
                    }
                    var sysMonth = await _bDSApiClient.GetSystemMonth(credit.CompanyId.Value.ToString());
                    sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
                    await CheckSysMonth(credit.CompanyId.Value, sysMonth);
                    var thisCreditAbtValue = (await _abtmentQueryService.GetAllListAsync(p => p.CreditBillCode == credit.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(q => q.Value);
                    var thisCreditAbtValue2 = (await _abtmentQueryService.GetAllListAsync(p => p.DebtBillCode == credit.BillCode, null, q => new AbatementPo { Value = q.Value })).Sum(q => q.Value);
                    thisCreditAbtValue = thisCreditAbtValue + thisCreditAbtValue2;
                    var leftThisCreditValue = Math.Abs(credit.Value) - thisCreditAbtValue;
                    if (leftThisCreditValue <= 0)
                    {
                        throw new AppServiceException($"应收单:{credit.BillCode}已冲销完成");
                    }

                    var finish = leftThisCreditValue - input.lstAbtDetail.Sum(p => p.Value);
                    if (finish < 0)
                    {
                        throw new AppServiceException($"应收单:{credit.BillCode}冲销金额不能大于剩余未冲销金额");
                    }

                    if (finish == 0)
                    {
                        credit.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                        lstUpdateCredit.Add(credit.Adapt<Credit>());
                    }

                    foreach (var p in lstCredit)
                    {
                        //该应收单的已冲销金额
                        var thisCreditAbatementValue = existThisCreditAbatementInfos.Where(q => q.DebtBillCode == p.BillCode).Sum(q => q.Value);

                        //该应收单的已冲销金额  有可能是 负数应收 所以要判断 CreditBillCode (正负应收冲销情况)
                        var thisCreditAbatementValue2 = existThisCreditAbatementInfos.Where(q => q.CreditBillCode == p.BillCode).Sum(q => q.Value);

                        thisCreditAbatementValue = Math.Abs(thisCreditAbatementValue) + Math.Abs(thisCreditAbatementValue2);

                        //找出该应收单本次要冲销的金额
                        var thisAbtDetailValue = input.lstAbtDetail.Where(q => q.BillCode == p.BillCode).Sum(t => t.Value);

                        if (thisCreditAbatementValue + Math.Abs(thisAbtDetailValue) > Math.Abs(p.Value))
                        {
                            throw new AppServiceException($"应收单:{p.BillCode}的已冲销金额:{thisCreditAbatementValue}+本次该应收单要冲销的金额:{thisAbtDetailValue}大于该应收单的总金额：{p.Value}");
                        }
                        if (thisCreditAbatementValue + Math.Abs(thisAbtDetailValue) >= Math.Abs(p.Value))
                        { //该应收单的已冲销金额+本次该应收单要冲销的金额==该应收单的总金额
                          //这时需要改该应收单的冲销状态为1，表示已冲销完成
                            p.AbatedStatus = Domain.AbatedStatusEnum.Abated;

                            lstUpdateCredit.Add(p.Adapt<Credit>());
                        }

                        if (credit.Value > 0) //正数应收发起
                        {
                            //写入冲销表abatement
                            var entityAbt = new Abatement()
                            {
                                CreditBillCode = p.BillCode,
                                CreditType = "credit",
                                DebtBillCode = credit.BillCode,
                                DebtType = "credit",
                                Value = thisAbtDetailValue,
                                Abtdate = DateTime.Now,
                                RecognizeReceiveCode = input.RecognizeReceiveCode
                            };
                            entityAbt.CreateBy(userName);
                            lstAddAbt.Add(entityAbt);
                        }
                        else //负数应收发起
                        {
                            //写入冲销表abatement
                            var entityAbt = new Abatement()
                            {
                                CreditBillCode = credit.BillCode,
                                CreditType = "credit",
                                DebtBillCode = p.BillCode,
                                DebtType = "credit",
                                Value = thisAbtDetailValue,
                                Abtdate = DateTime.Now,
                                RecognizeReceiveCode = input.RecognizeReceiveCode

                            };
                            entityAbt.CreateBy(userName);
                            lstAddAbt.Add(entityAbt);
                        }
                    }
                    //回写预收应收认款数据
                    var recognizeReceiveItem = await _db.RecognizeReceiveItems.Include(p => p.RecognizeReceiveDetails).FirstOrDefaultAsync(p => p.Code == input.RecognizeReceiveCode);
                    if (recognizeReceiveItem != null && recognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled && recognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.WaitSubmit && input.lstAbtDetail != null && input.lstAbtDetail.Any())
                    {
                        var addrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                        var rrdcs = await _db.RecognizeReceiveDetailCredits.Where(x => x.RecognizeReceiveItemId == recognizeReceiveItem.Id).AsNoTracking().ToListAsync();
                        if (rrdcs == null || !rrdcs.Any())
                        {
                            //针对未结算的历史数据根据金蝶返回写入
                            foreach (var item in input.lstAbtDetail)
                            {
                                var rrd = recognizeReceiveItem.RecognizeReceiveDetails.FirstOrDefault(x => x.Code == item.Code);
                                if (rrd != null)
                                {
                                    var single = new RecognizeReceiveDetailCreditPo();
                                    single.Id = Guid.NewGuid();
                                    single.RecognizeReceiveDetailId = rrd.Id;
                                    single.CurrentValue = item.Value;
                                    single.CreditId = credit != null ? credit.Id : null;
                                    single.CreatedBy = userName;
                                    single.CreditCode = item.BillCode;
                                    single.InvoiceNo = rrd.Type == 1 ? rrd.Code : null;
                                    single.RecognizeReceiveItemId = recognizeReceiveItem.Id;
                                    single.OrderNo = rrd.Type == 2 ? rrd.Code : null;
                                    addrrdcs.Add(single);
                                }
                            }
                        }
                        if (addrrdcs.Any())
                        {
                            _db.RecognizeReceiveDetailCredits.AddRange(addrrdcs);
                        }
                    }
                }
                else if (input.BillType == "arpaysettle")
                {
                    var abatements = await _db.Abatements.Where(p => lstCreditCode.Contains(p.DebtBillCode) || lstCreditCode.Contains(p.CreditBillCode)).ToListAsync();
                    foreach (var credit in lstCredit)
                    {
                        var tempabatementValue = abatements.Where(p => p.DebtBillCode == credit.BillCode || p.CreditBillCode == credit.BillCode).Sum(p => p.Value);
                        var needabatementValue = input.lstAbtDetail.Where(p => p.BillCode == credit.BillCode).Sum(p => p.Value);
                        if (Math.Abs(credit.Value) - tempabatementValue < needabatementValue)
                        {
                            throw new AppServiceException($"应收单:{credit.BillCode}的已冲销金额:{tempabatementValue}+本次该应收单要冲销的金额:{needabatementValue}大于该应收单的总金额：{credit.Value}");
                        }
                        lstAddAbt.Add(new Abatement
                        {
                            CreditBillCode = input.Code,
                            CreditType = "payment",
                            DebtBillCode = credit.BillCode,
                            DebtType = "credit",
                            Value = needabatementValue,
                            Abtdate = DateTime.Now,
                            CreatedBy = userName ?? "none",
                            RecognizeReceiveCode = input.RecognizeReceiveCode
                        });
                        if (needabatementValue + Math.Abs(tempabatementValue) >= Math.Abs(credit.Value))
                        { //该应收单的已冲销金额+本次该应收单要冲销的金额==该应收单的总金额
                          //这时需要改该应收单的冲销状态为1，表示已冲销完成
                            credit.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                            lstUpdateCredit.Add(credit.Adapt<Credit>());
                        }
                    }
                }
                if (lstAddAbt.Count > 0)
                {
                    await _abatementRepository.AddManyAsync(lstAddAbt);
                }
                if (lstUpdateDebtDetail.Count > 0)
                {
                    await _debtDetailRepository.UpdateManyAsync(lstUpdateDebtDetail);
                }
                lstAddDebtDetail = lstAddDebtDetail.Where(p => p.Value != 0).ToList();
                if (lstAddDebtDetail.Count > 0)
                {
                    await _debtDetailRepository.AddManyAsync(lstAddDebtDetail);
                }
                if (lstUpdateCredit.Count > 0)
                {
                    await _creditRepository.UpdateManyAsync(lstUpdateCredit);
                }
                nRes = await _unitOfWork.CommitAsync();
                lstDebtIdForRepaireDiff = lstDebtIdForRepaireDiff.Distinct().ToList();
                await _debtRepository.RepaireDebtDiff(lstDebtIdForRepaireDiff);
                if (input.BillType == "receive")
                {
                    await PushSPD(lstCredit, input);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                await PushSale(lstCredit, input);
                //await _daprClient.PublishEventAsync<List<Guid>>("pubsub-default", "finance-finance-advance", lstCreditId);
            }
            return nRes;

        }

        private async Task PushSPD(List<CreditPo> credits, GenerateAbtForCreditInput input)
        {
            if (credits != null && credits.Any())
            {
                var spdCredits = credits.Where(p => p.SaleSource == SaleSourceEnum.Spd && p.OrderNo.Contains("YS-")).ToList();
                var receiveItemPos = await _db.RecognizeReceiveItems.Include(p => p.RecognizeReceiveDetails).Where(p => p.Code == input.RecognizeReceiveCode && p.Status == RecognizeReceiveItemStatusEnum.Completed).ToListAsync();
                if (spdCredits != null && spdCredits.Any() && receiveItemPos != null && receiveItemPos.Any())
                {
                    var alldetails = new List<RecognizeReceiveDetailPo>();
                    foreach (var item in receiveItemPos)
                    {
                        alldetails.AddRange(item.RecognizeReceiveDetails);
                    }
                    #region 发票明细
                    var recognizeReceiveDetails = alldetails.Where(p => p.Type == 1).ToList();

                    if (recognizeReceiveDetails != null && recognizeReceiveDetails.Any())
                    {
                        var invoiceNos = recognizeReceiveDetails.Select(p => p.Code).ToList();
                        foreach (var credit in spdCredits)
                        {
                            var invoiceCredits = await _invoiceCreditQueryService.GetAllListAsync(p => p.CreditId == credit.Id);
                            if (invoiceCredits != null && invoiceCredits.Any())
                            {
                                var receiveinput = new RecognizeReceiveSpdInput
                                {
                                    relateCode = input.Code,
                                };
                                recognizeReceiveDetails.ForEach(p =>
                                {
                                    foreach (var abt in input.lstAbtDetail)
                                    {
                                        if (receiveinput.billReceiveInvoiceRelList.Sum(p => p.receiveInvoiceAmount) < abt.Value)
                                        {
                                            if (p.Value == abt.Value)
                                            {
                                                receiveinput.billReceiveInvoiceRelList.Add(new abatement
                                                {
                                                    invoiceNum = abt.Code,
                                                    receiveInvoiceAmount = abt.Value,
                                                    creditItemCode = credit.OrderNo
                                                });
                                            }
                                        }
                                    }
                                });
                                if (receiveinput.billReceiveInvoiceRelList == null || !receiveinput.billReceiveInvoiceRelList.Any())
                                {
                                    input.lstAbtDetail.ForEach(p =>
                                    {
                                        receiveinput.billReceiveInvoiceRelList.Add(new abatement
                                        {
                                            invoiceNum = invoiceCredits.First().InvoiceNo,
                                            receiveInvoiceAmount = p.Value,
                                            creditItemCode = credit.OrderNo
                                        });
                                    });
                                }
                                await _sPDApiClient.SynReceive(receiveinput);
                            }
                        }
                    }
                    #endregion
                    //初始应收明细
                    recognizeReceiveDetails = alldetails.Where(p => p.Type == 3).ToList();
                    if (recognizeReceiveDetails != null && recognizeReceiveDetails.Any())
                    {
                        var codes = recognizeReceiveDetails.Select(p => p.Code).ToList();
                        var receiveinput = new RecognizeReceiveSpdInitInput
                        {
                            relateCode = input.Code,
                            amount = input.Value,
                            receiveTime = DateTime.Now.ToString("yyyy-MM-dd"),
                            remark = "",
                        };
                        foreach (var credit in spdCredits)
                        {
                            var lstAbtDetail = input.lstAbtDetail.Where(p => p.BillCode == credit.BillCode).FirstOrDefault();
                            if (lstAbtDetail != null)
                            {
                                receiveinput.billReceiveInvoiceRelList.Add(new abatementInit
                                {
                                    creditAmount = lstAbtDetail.Value,
                                    creditCode = credit.OrderNo
                                });

                            }
                        }
                        await _sPDApiClient.synReceiveNoInvoice(receiveinput);
                    }

                    //初始应收明细
                    recognizeReceiveDetails = alldetails.Where(p => p.Type == 2).ToList();
                    //认款单订单
                    if (recognizeReceiveDetails != null && recognizeReceiveDetails.Any())
                    {
                        var receiveinput = new RecognizeReceiveSpdInitInput
                        {
                            relateCode = input.Code,
                            amount = input.Value,
                            receiveTime = DateTime.Now.ToString("yyyy-MM-dd"),
                            remark = "",
                        };
                        foreach (var spdcredit in spdCredits)
                        {
                            var lstAbtDetail = input.lstAbtDetail.Where(p => p.BillCode == spdcredit.BillCode).FirstOrDefault();
                            if (lstAbtDetail != null)
                            {
                                receiveinput.billReceiveInvoiceRelList.Add(new abatementInit
                                {
                                    creditAmount = lstAbtDetail.Value,
                                    creditCode = spdcredit.OrderNo
                                });

                            }
                        }
                        await _sPDApiClient.synReceiveNoInvoice(receiveinput);
                    }
                }
            }

        }
        private async Task PushSale(List<CreditPo> credits, GenerateAbtForCreditInput input)
        {
            if (credits != null && credits.Any())
            {
                var receiveItemPos = await _db.RecognizeReceiveItems.Include(p => p.RecognizeReceiveDetails).
                                          Where(p => p.Code == input.RecognizeReceiveCode).ToListAsync();

                //发票明细
                var recognizeReceiveDetails = input.lstAbtDetail.Where(p => p.Classify == 1).ToList();
                if (recognizeReceiveDetails != null && recognizeReceiveDetails.Any())
                {
                    var financeSalePaymentPubInputs = new List<FinanceSalePaymentPubInput>();
                    foreach (var item in recognizeReceiveDetails)
                    {
                        var credit = credits.Where(p => p.BillCode == item.BillCode).First();
                        financeSalePaymentPubInputs.Add(new FinanceSalePaymentPubInput
                        {
                            CreditNo = item.BillCode,
                            InvoiceNo = item.Code,
                            SaleNo = credit.SaleSource == SaleSourceEnum.Spd && credit.CreditType == CreditTypeEnum.servicefee ? credit.RelateCode : credit.OrderNo,
                            AbatementAmount = item.Value,//input.lstAbtDetail.Sum(p => p.Value), //本次认款明细金额
                            ReceiveCode = input.Code,
                            RecognizeReceiveCode = input.RecognizeReceiveCode
                        });
                    }
                    var jsonStr = JsonConvert.SerializeObject(financeSalePaymentPubInputs);
                    _logger.LogInformation($"推送给销售PushSale:{jsonStr}");
                    await _daprClient.PublishEventAsync<List<FinanceSalePaymentPubInput>>("pubsub-default", "finance-sale-payment", financeSalePaymentPubInputs);
                }
            }
        }

        private async Task PushPaymentSettlement_KD(Debt debt, List<GenerateAbtForDebtInput> generateAbts, string abtmentClassify = "debt", bool? singleSettle = null)
        {
            var inputs = new List<PaymentSettlementInput>();
            foreach (var item in generateAbts)
            {
                var tempInput = new PaymentSettlementInput();
                if (debt.Value < 0)
                {
                    tempInput.mianbillno = item.BillCode;
                    tempInput.mianSettleAmt = item.Value;
                    tempInput.asstbillno = debt.BillCode;
                    tempInput.asstSettleAmt = item.Value;
                    tempInput.singleSettle = singleSettle;
                    if (debt.Value < 0)
                    {
                        tempInput.asstSettleAmt = tempInput.asstSettleAmt * -1;
                    }
                }
                else
                {
                    tempInput.mianbillno = debt.BillCode;
                    tempInput.mianSettleAmt = item.Value;
                    tempInput.asstbillno = item.BillCode;
                    tempInput.asstSettleAmt = item.Value;
                    tempInput.singleSettle = singleSettle;
                    if (item.BillValue.HasValue && item.BillValue.Value < 0)
                    {
                        tempInput.asstSettleAmt = tempInput.asstSettleAmt * -1;
                    }
                }
                inputs.Add(tempInput);
            }
            var ret = BaseResponseData<int>.Success("操作成功");
            if (abtmentClassify == "debt")
            {
                ret = await _kingdeeApiClient.PaymentSettlement(inputs);
            }
            else
            {
                ret = await _kingdeeApiClient.PayablesOffsetReceivables(inputs);
            }
            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }
        }

        private async Task PushPaymentSettlementKDForBatchPaymentAsync(List<DebtPo> lstDebt, List<Abatement> lstAbtment)
        {
            var inputs = new List<PaymentSettlementInput>();
            foreach (var item in lstAbtment)
            {
                var debt = lstDebt.FirstOrDefault(p => p.BillCode == item.CreditBillCode);
                if (debt == null)
                {
                    continue;
                }
                inputs.Add(new PaymentSettlementInput
                {
                    mianbillno = debt.BillCode,
                    mianSettleAmt = item.Value,
                    asstbillno = item.DebtBillCode,
                    asstSettleAmt = item.Value,
                });
            }

            var ret = await _kingdeeApiClient.PaymentSettlement(inputs);
            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }
        }
        /// <summary>
        /// 负数应付冲收款单
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="input"></param>
        /// <param name="userName"></param>
        /// <param name="singleSettle"></param>
        /// <returns></returns>
        public async Task<int> GenerateAbtForReceiveAsync(Guid debtId, List<GenerateAbtForDebtInput> input, string userName, bool? singleSettle = null)
        {
            if (input.Any(t => t.Value <= 0))
            {
                throw new AppServiceException("冲销金额必须大于0");
            }
            if (input.Count() > 1)
            {
                throw new AppServiceException("只能选择一个收款单冲销");
            }
            var debt = await _debtRepository.GetWithNoTrackAsync(debtId);
            if (debt == null)
            {
                throw new AppServiceException("应付单不存在");
            }
            if (debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}冲销状态为已完成，不允许再次冲销!");
            }
            if (input.Sum(p => p.Value) > input.Sum(p => p.BillValue))
            {
                throw new AppServiceException("冲销金额不能大于单据金额");
            }
            if (debt.CompanyId.HasValue)
            {
                await CheckInventoryState(debt.CompanyId.Value);
            }
            await CheckForOngoingLossRecognition(new List<string>() { debt.BillCode! });

            var hasAbatedAmount = _db.Abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).Sum(p => p.Value);
            if ((Math.Abs(debt.Value) - hasAbatedAmount) < input.Sum(p => p.Value))
            {
                throw new AppServiceException($"应付单：{debt.BillCode}本次冲销金额大于剩余可冲销金额!");
            }

            var leftThisDebtValue = Math.Abs(debt.Value) - hasAbatedAmount;
            var _debtPaymentUses = await _debtPaymentUseDetailQueryService.GetAllListAsync(p => p.DebtCode == debt.BillCode);
            if (_debtPaymentUses != null && _debtPaymentUses.Any())
            {
                leftThisDebtValue = leftThisDebtValue - _debtPaymentUses.Sum(p => p.UseAmount);
            }
            if (leftThisDebtValue < 0)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}冲销额度不足!");
            }
            if (input.Sum(p => p.Value) > leftThisDebtValue)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}剩余额度{leftThisDebtValue}小于冲销额度，不允许操作!");
            }

            var inputs = new List<DebtRefundSettleInput>();
            foreach (var item in input)
            {

                inputs.Add(new DebtRefundSettleInput
                {
                    mainBill = debt.BillCode,
                    mainSettleAmt = item.Value * -1,//主方单据因为是负数应收，都是负的，所以主方结算金额，要改成负的冲销金额,
                    asstBill = item.BillCode,
                    asstSettleAmt = item.Value,
                    singleSettle = singleSettle,
                });
            }
            var ret = await _kingdeeApiClient.DebtRefundSettle(inputs);
            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }
            var lstAddAbt = new List<Abatement>();
            foreach (var item in input)
            {
                //写入冲销表abatement
                var entityAbt = new Abatement()
                {
                    CreditBillCode = debt.BillCode,
                    CreditType = "debt",
                    DebtBillCode = item.BillCode,
                    DebtType = "receive",
                    Value = item.Value,
                    Abtdate = DateTime.Now

                };
                entityAbt.CreateBy(userName);
                lstAddAbt.Add(entityAbt);

            }
            //负数应付单的冲销金额等于收款单的金额，将应付单的冲销状态改为已完成
            if ((Math.Abs(debt.Value) - hasAbatedAmount) == input.Sum(p => p.Value))
            {
                debt.AbatedStatus = Domain.AbatedStatusEnum.Abated;
                var lstPo = debt.Adapt<DebtPo>();
                _db.Debts.Update(lstPo);
            }
            await _abatementRepository.AddManyAsync(lstAddAbt);
            return await _unitOfWork.CommitAsync();
        }

        /// <summary>
        /// 正数应付冲付款单
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="input"></param>
        /// <param name="userName"></param>
        /// <param name="singleSettle">单次冲销</param>
        /// <returns></returns>
        public async Task<int> GenerateAbtForPaymentAsync(Guid debtId, List<GenerateAbtForDebtInput> input, string userName, bool? singleSettle = null)
        {
            if (input.Any(t => t.Value <= 0))
            {
                throw new AppServiceException("冲销金额必须大于0");
            }
            if (input.Count() > 1)
            {
                throw new AppServiceException("只能选择一个付款单冲销");
            }
            var debt = await _debtRepository.GetWithNoTrackAsync(debtId);
            if (debt == null)
            {
                throw new AppServiceException("应付单不存在");
            }
            if (!debt.CompanyId.HasValue)
            {
                
                throw new AppServiceException("应付单公司id为空，不允许冲销!");
            }
            if (debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
            {
                throw new AppServiceException($"应付单：{debt.BillCode}冲销状态为已完成，不允许再次冲销!");
            }
            if (input.Sum(p => p.Value) > input.Sum(p => p.BillValue))
            {
                throw new AppServiceException("冲销金额不能大于单据金额");
            }
            if (debt.CompanyId.HasValue)
            {
                await CheckInventoryState(debt.CompanyId.Value);
            }
            await CheckForOngoingLossRecognition(new List<string>() { debt.BillCode! });

            var debtDetails = await _db.DebtDetails.AsNoTracking().Where(p => p.DebtId == debt.Id && p.Status == DebtDetailStatusEnum.WaitExecute).OrderBy(o => o.Value).ToListAsync();
            if (debtDetails == null || !debtDetails.Any())
            {
                throw new AppServiceException($"应付单：{debt.BillCode}没有待执行的付款计划!");
            }

            var hasAbatedAmount = _db.Abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).Sum(p => p.Value);
            if ((Math.Abs(debt.Value) - hasAbatedAmount) < input.Sum(p => p.Value))
            {
                throw new AppServiceException($"应付单：{debt.BillCode}本次冲销金额大于剩余可冲销金额!");
            }


            var payCode = input.First().BillCode;
            var payment = await _db.Payments.FirstAsync(p => p.Code == payCode && string.IsNullOrEmpty(p.PurchaseCode));
            if (payment == null)
            {
                throw new AppServiceException("付款单不存在");
            }
            if (debt.CompanyId != payment.CompanyId || debt.ProjectId != payment.ProjectId || debt.AgentId != payment.AgentId)
            {
                throw new AppServiceException("只能冲销同公司、同项目、同供应商的应付单!");
            }

            var payAbatedAmount = _db.Abatements.Where(p => p.DebtBillCode == payCode || p.CreditBillCode == payCode).Sum(p => p.Value);
            if ((Math.Abs(payment.Value) - payAbatedAmount) < input.Sum(p => p.Value))
            {
                throw new AppServiceException($"付款单：{debt.BillCode}本次冲销金额大于剩余可冲销金额!");
            }

            #region 应付单号集合 
            List<string> debtNos = new List<string>();
            debtNos.Add(debt.BillCode);
            var paymentAutoDetails = await _paymentAutoDetailQueryService.GetAllListAsync(p => p.PaymentAutoItem.Status != PaymentAutoItemStatusEnum.Completed);
            if (paymentAutoDetails != null)
            {
                var debtDetilIds = paymentAutoDetails.Select(p => p.DebtDetilId);
                var debtDetailss = await _debtDetailQueryService.GetAllListAsync(p => debtDetilIds.Contains(p.Id), new List<string> { "Debt" });
                foreach (var detail in paymentAutoDetails)
                {
                    var debtDetail = debtDetailss.Where(p => p.Id == detail.DebtDetilId).FirstOrDefault();
                    if (debtDetail != null && debtDetail.Debt != null && debtNos.Contains(debtDetail.Debt.BillCode))
                    {
                        throw new AppServiceException($"应付单：{debtDetail.Debt.BillCode}在批量付款单中未完成，请在批量付款中完成后操作!");
                    }
                }
            }
            #endregion
            var leftValue = input.First().Value;

            var addDebtDetails = new List<DebtDetail>(debtDetails.Count);
            var updateDebtDetails = new List<DebtDetail>(debtDetails.Count);
            //循环付款明细
            foreach (var item in debtDetails)
            {
                if (item.Value > leftValue && leftValue > 0)
                {
                    var newValue = item.Value - leftValue;
                    item.Value = leftValue;
                    item.OriginValue = item.CostDiscount.HasValue && item.CostDiscount.Value > 0 ? Math.Round((leftValue * 100 / item.CostDiscount.Value), 2) : null;
                    item.Status = DebtDetailStatusEnum.Completed;
                    item.DebtDetailExcutes.Add(new DebtDetailExcutePo()
                    {
                        DebtDetailId = item.Id,
                        CreatedBy = item.CreatedBy,
                        CreatedTime = DateTimeOffset.UtcNow,
                        ExcuteType = "payment",
                        PaymentCode = payment.Code,
                        PaymentDate = DateTime.Now,
                        Value = leftValue,
                    });
                    updateDebtDetails.Add(item.Adapt<DebtDetail>());
                    addDebtDetails.Add(new DebtDetail
                    {
                        DebtId = debt.Id,
                        AccountPeriodType = item.AccountPeriodType,
                        Value = newValue,
                        OriginValue = item.CostDiscount.HasValue && item.CostDiscount.Value > 0 ? Math.Round((newValue * 100 / item.CostDiscount.Value), 2) : null,
                        Status = DebtDetailStatusEnum.WaitExecute,
                        CreatedBy = item.CreatedBy,
                        CreatedTime = DateTimeOffset.UtcNow,
                        BackPayTime = item.BackPayTime,
                        Code = item.Code,
                        CostDiscount = item.CostDiscount,
                        DistributionDiscount = item.DistributionDiscount,
                        Discount = item.Discount,
                        CreditId = item.CreditId,
                        DraftBillExpireDate = item.DraftBillExpireDate,
                        FinanceDiscount = item.FinanceDiscount,
                        IsInvoiceReceipt = item.IsInvoiceReceipt,
                        OrderNo = item.OrderNo,
                        ProbablyPayTime = item.ProbablyPayTime,
                        PurchaseCode = item.PurchaseCode,
                        ReceiveCode = item.ReceiveCode,
                        RecognizeReceiveCode = item.RecognizeReceiveCode,
                        Settletype = item.Settletype,
                        SpdDiscount = item.SpdDiscount,
                        TaxDiscount = item.TaxDiscount,
                    });
                    leftValue = 0;
                    break;
                }
                else if (item.Value == leftValue && leftValue > 0)
                {
                    item.Value = leftValue;
                    item.Status = DebtDetailStatusEnum.Completed;
                    item.DebtDetailExcutes.Add(new DebtDetailExcutePo()
                    {
                        DebtDetailId = item.Id,
                        CreatedBy = item.CreatedBy,
                        CreatedTime = DateTimeOffset.UtcNow,
                        ExcuteType = "payment",
                        PaymentCode = payment.Code,
                        PaymentDate = DateTime.Now,
                        Value = leftValue,
                    });
                    updateDebtDetails.Add(item.Adapt<DebtDetail>());
                    leftValue = 0;
                    break;
                }

                leftValue -= item.Value;
                item.Status = DebtDetailStatusEnum.Completed;
                item.DebtDetailExcutes.Add(new DebtDetailExcutePo()
                {
                    DebtDetailId = item.Id,
                    CreatedBy = item.CreatedBy,
                    CreatedTime = DateTimeOffset.UtcNow,
                    ExcuteType = "payment",
                    PaymentCode = payment.Code,
                    PaymentDate = DateTime.Now,
                    Value = item.Value,
                });
                updateDebtDetails.Add(item.Adapt<DebtDetail>());
            }

            var paymentSettlements = new List<PaymentSettlementInput>();
            foreach (var item in input)
            {
                paymentSettlements.Add(new PaymentSettlementInput
                {
                    mianbillno = debt.BillCode ?? "",
                    mianSettleAmt = item.Value,
                    asstbillno = string.IsNullOrEmpty(payment.OriginCode) ? item.BillCode : payment.OriginCode,
                    asstSettleAmt = item.Value,
                    singleSettle = singleSettle,
                });
            }
            var ret = await _kingdeeApiClient.PaymentSettlement(paymentSettlements);

            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }

            var abatements = new List<Abatement>();
            foreach (var item in input)
            {
                //写入冲销表abatement
                var abatement = new Abatement()
                {
                    CreditBillCode = debt.BillCode ?? "",
                    CreditType = "debt",
                    DebtBillCode = item.BillCode,
                    DebtType = "payment",
                    Value = item.Value,
                    Abtdate = DateTime.Now
                };
                abatement.CreateBy(userName);
                abatements.Add(abatement);
            }

            //应付单的冲销金额等于收款单的金额，将应付单的冲销状态改为已完成
            if ((Math.Abs(debt.Value) - hasAbatedAmount) == input.Sum(p => p.Value))
            {
                debt.AbatedStatus = AbatedStatusEnum.Abated;
                var debtPo = debt.Adapt<DebtPo>();
                _db.Debts.Update(debtPo);
            }
            if (addDebtDetails.Count > 0)
            {
                await _debtDetailRepository.AddManyAsync(addDebtDetails);
            }
            if (updateDebtDetails.Count > 0)
            {
                await _debtDetailRepository.UpdateManyAsync(updateDebtDetails);
            }
            await _abatementRepository.AddManyAsync(abatements);

            return await _unitOfWork.CommitAsync();
        }
        /// <summary>
        /// 收款冲付款单成功之后加入冲销信息
        /// </summary>
        /// <param name="details"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<int> ReceiveAbtPaymentSave(List<RefundDetailPo> details, string userName)
        {
            if (details == null || details.Count == 0)
            {
                return 0;
            }
            var abatements = new List<Abatement>();
            foreach (var item in details)
            {
                //写入冲销表abatement
                var abatement = new Abatement()
                {
                    CreditBillCode = item.ReceiveCode ?? "",
                    CreditType = "receive",
                    DebtBillCode = item.PaymentCode ?? "",
                    DebtType = "payment",
                    Value = item.PaymentAmount > item.ReceiveAmount ? item.ReceiveAmount.Value : item.PaymentAmount.Value,
                    Abtdate = DateTime.Now
                };
                abatement.CreateBy(userName);
                abatements.Add(abatement);
                //一个付款对一个收款
                if (details.Select(p => p.ReceiveCode).Distinct().Count() == 1 && details.Select(p => p.PaymentCode).Distinct().Count() >= 1)
                {
                    var paymentItem = await _db.Payments.Where(p => p.Code == item.PaymentCode).FirstOrDefaultAsync();
                    if (paymentItem != null)
                    {
                        var abateValue1 = await _db.Abatements.Where(x => x.DebtBillCode == paymentItem.Code).SumAsync(x=>x.Value);
                        var abateValue2 = await _db.Abatements.Where(x => x.CreditBillCode == paymentItem.Code).SumAsync(x=>x.Value);
                        var abateValue3 = abatements.Where(x => x.DebtBillCode == paymentItem.Code).Sum(x => x.Value);
                        if (abateValue1 + abateValue2 +abateValue3>= paymentItem.Value)
                        {
                            paymentItem.AbatedStatus = AbatedStatusEnum.Abated;
                            _db.Payments.Update(paymentItem);
                        }
                    }
                }

            }//多个收款对一个付款 
            if (details.Select(p => p.PaymentCode).Distinct().Count() == 1 && details.Select(p => p.ReceiveCode).Distinct().Count() > 1)
            {
                if (details.DistinctBy(p => p.PaymentCode).FirstOrDefault().PaymentAmount == details.Sum(p => p.ReceiveAmount))
                {
                    var paymentItem = await _db.Payments.Where(p => p.Code == details.FirstOrDefault().PaymentCode).FirstOrDefaultAsync();
                    if (paymentItem != null)
                    {
                        var abateValue1 = await _db.Abatements.Where(x => x.DebtBillCode == paymentItem.Code).SumAsync(x=>x.Value);
                        var abateValue2 = await _db.Abatements.Where(x => x.CreditBillCode == paymentItem.Code).SumAsync(x=>x.Value);
                        var abateValue3 = abatements.Where(x => x.DebtBillCode == paymentItem.Code).Sum(x => x.Value);
                        if (abateValue1 + abateValue2 + abateValue3 >= paymentItem.Value)
                        {
                            paymentItem.AbatedStatus = AbatedStatusEnum.Abated;
                            _db.Payments.Update(paymentItem);
                        }
                    }
                }
            }
            await _abatementRepository.AddManyAsync(abatements);
            return await _unitOfWork.CommitAsync();
        }


        /// <summary>
        /// 损失确认正负应付冲销
        /// </summary>
        /// <param name="inputs">入参</param>
        /// <param name="userName"></param>
        /// <returns></returns>
        /// <exception cref="AppServiceException"></exception>
        public (List<DebtDetailPo> AddDebtDetails, List<DebtDetailExcutePo> AddDebtDetailExcutes, List<AbatementPo> AddDebtAbatementDetails) LossGenerateAbtForDebtAsync(List<LossAbtForDebtInput> inputs, string userName)
        {
            var allAddDebtDetails = new List<DebtDetailPo>();
            var allDebtDetailExcutes = new List<DebtDetailExcutePo>();
            var allDebtAbatementDetails = new List<AbatementPo>();//所有正负应付的冲销记录
            foreach (var input in inputs)
            {
                var canUseAbtDebt = input.OldDebt.DebtDetails.Where(z => z.Status == DebtDetailStatusEnum.WaitExecute).OrderBy(z => z.AccountPeriodType).ToList();
                if (input.OldDebt.DebtDetails == null || input.OldDebt.DebtDetails.Count == 0)
                {
                    throw new ApplicationException($"未找到应付单{input.OldDebt.BillCode}的付款计划");
                }
                //负数应付冲销金额
                var leftAbtAmount = Math.Abs(input.NewDebt.Value);
                //按账期分组
                var groupPeriodType = canUseAbtDebt.GroupBy(z => z.AccountPeriodType).ToList();
                //遍历每一个账期
                foreach (var groupItem in groupPeriodType)
                {
                    if (leftAbtAmount <= 0)
                        break;
                    var orderDebtDetails = groupItem.OrderBy(z => z.Value).ToList();
                    foreach (var debtDetailItem in orderDebtDetails)
                    {
                        var codeIndex = 1;
                        //如果付款计划明细金额直接大于损失金额，全部扣光损失金额，拆分付款计划
                        if (debtDetailItem.Value > leftAbtAmount)
                        {
                            //生成单号
                            var newDebtDetailCode = $"{debtDetailItem.Code}-LOSS-{codeIndex.ToString("000")}";
                            //生成新的付款计划
                            var newDebtDetail = Data.Utilities.Utility.DeepCopyDebtDetail(debtDetailItem, debtDetailItem.Debt, leftAbtAmount, DebtDetailStatusEnum.Completed);
                            //生成执行计划
                            var newDebtDetailExecute = new DebtDetailExcutePo
                            {
                                DebtDetail = newDebtDetail,
                                PaymentDate = DateTime.Now,
                                CreatedBy = input.NewDebt.CreatedBy,
                                CreatedTime = DateTimeOffset.UtcNow,
                                UpdatedBy = input.NewDebt.UpdatedBy,
                                UpdatedTime = DateTimeOffset.UtcNow,
                                ExcuteType = DomainConstants.DebtExecuteType,
                                PaymentCode = input.NewDebt.BillCode,
                                Value = leftAbtAmount
                            };
                            newDebtDetail.Code = newDebtDetailCode;
                            newDebtDetail.CreatedBy = input.NewDebt.CreatedBy;
                            newDebtDetail.UpdatedBy = input.NewDebt.UpdatedBy;

                            //拆分原始付款计划，把剩余部分写预计付款日期(如果已经有预计付款日期，则不动)
                            if (!debtDetailItem.ProbablyPayTime.HasValue)
                                debtDetailItem.ProbablyPayTime = DateTime.Now;
                            debtDetailItem.Value -= leftAbtAmount;//原始付款计划金额剩余金额更新
                            //将负数应付剩余金额置0
                            leftAbtAmount = 0;
                            allAddDebtDetails.Add(newDebtDetail);
                            allDebtDetailExcutes.Add(newDebtDetailExecute);
                        }
                        else//付款计划明细如果小于等于损失金额，则付款计划全部完成，并且继续往下分配
                        {
                            leftAbtAmount -= debtDetailItem.Value;
                            //生成执行计划
                            var newDebtDetailExecute = new DebtDetailExcutePo
                            {
                                DebtDetail = debtDetailItem,
                                DebtDetailId = debtDetailItem.Id,
                                PaymentDate = DateTime.Now,
                                CreatedBy = input.NewDebt.CreatedBy,
                                CreatedTime = DateTimeOffset.UtcNow,
                                UpdatedBy = input.NewDebt.UpdatedBy,
                                UpdatedTime = DateTimeOffset.UtcNow,
                                ExcuteType = DomainConstants.DebtExecuteType,
                                PaymentCode = input.NewDebt.BillCode,
                                Value = debtDetailItem.Value
                            };
                            debtDetailItem.Status = DebtDetailStatusEnum.Completed;
                            allDebtDetailExcutes.Add(newDebtDetailExecute);
                        }
                        if (leftAbtAmount <= 0)
                            break;
                        codeIndex++;
                    }
                }
                if (leftAbtAmount != 0)
                {
                    throw new ApplicationException("冲销金额不足，请核对数据");
                }
                else
                {
                    //加入冲销表
                    allDebtAbatementDetails.Add(new AbatementPo()
                    {
                        DebtBillCode = input.NewDebt.BillCode,
                        DebtType = "debt",
                        CreditBillCode = input.OldDebt.BillCode,
                        CreditType = "debt",
                        Abtdate = DateTime.Now,
                        Value = Math.Abs(input.NewDebt.Value),
                        CreatedTime = DateTime.Now,
                        CreatedBy = "LossRecognition"
                    });
                }
                //判断原始正数应付是否都已经全部冲销完成，如果全部冲销完成，则更改原始应付状态
                if (!input.OldDebt.DebtDetails.Any(z => z.Status == DebtDetailStatusEnum.WaitExecute))
                {
                    input.OldDebt.AbatedStatus = AbatedStatusEnum.Abated;
                }
            }
            return (allAddDebtDetails, allDebtDetailExcutes, allDebtAbatementDetails);
        }

        #region 私有方法
        private async Task CheckForOngoingLossRecognition(List<string> debtNos)
        {
            var prohibitedStatuses = new HashSet<StatusEnum>() {
                StatusEnum.Refuse,
                StatusEnum.Complate,
            };

            var hasOngoingLossRecognition = await _db.LossRecognitionDetails
                .Where(p => p.Classify == LossRecognitionDetailTypeEnum.Debt && debtNos.Contains(p.BillCode))
                .AsNoTracking()
                .Select(p => p.LossRecognitionItemId)
                .AnyAsync(id => _db.LossRecognitionItem
                .Any(p => p.Id == id && p.Status.HasValue && !prohibitedStatuses.Contains(p.Status.Value)));

            // 如果存在正在进行的定损记录，抛出异常
            if (hasOngoingLossRecognition)
            {
                throw new AppServiceException($"应付在定损中，不允许再次冲销!");
            }
        }
        
        /// <summary>
        /// 盘点状态检查
        /// </summary>
        /// <param name="companyId"></param>
        private async Task CheckInventoryState(Guid companyId)
        {
            string sysMonth = DateTime.Now.ToString("yyyy-MM");
            var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
            if (inventory != null)
            {
                if (inventory.Status == 2)
                {
                    throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                }
                if (inventory.Status == 99)
                {
                    DateTime.TryParse(sysMonth, out DateTime billDate);
                    if (billDate.Year == DateTime.Now.Year && billDate.Month == DateTime.Now.Month)
                    {
                        throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                    }
                }
            }
        }
        #endregion
    }
}
