using Inno.CorePlatform.Finance.Application.DTOs.UserWorkExchange;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using static Inno.CorePlatform.Finance.Domain.Enums.FinanceEnum;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.UserWorkExchange
{
    /// <summary>
    /// 用户工作交换服务实现
    /// </summary>
    public class UserWorkExchangeService : IUserWorkExchangeService
    {
        private readonly FinanceDbContext _dbContext;
        private readonly ILogger<UserWorkExchangeService> _logger;

        public UserWorkExchangeService(
            FinanceDbContext dbContext,
            ILogger<UserWorkExchangeService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<TransferResultDto>> ProcessUserWorkExchangeAsync(UserWorkExchangeEventDto eventDto)
        {
            var results = new List<TransferResultDto>();
            var totalStopwatch = Stopwatch.StartNew();

            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            
            try
            {
                _logger.LogInformation("开始处理用户工作交换: {OriginalUser}({OriginalDisplayName}) -> {TargetUser}({TargetDisplayName})", 
                    eventDto.UserName, eventDto.DisplayName, eventDto.TargetUserName, eventDto.TargetDisplayName);

                // 依次处理各个业务单据类型
                var tasks = new List<Task<TransferResultDto>>
                {
                    UpdatePaymentAutoItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateRecognizeReceiveItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateRefundItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateLossRecognitionItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateInputBillCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateCustomizeInvoiceItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdatePreCustomizeInvoiceItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateAdvancePaymentItemCreatorAsync(eventDto.UserName, eventDto.TargetUserName),
                    UpdateAdvanceBusinessApplyCreatorAsync(eventDto.UserName, eventDto.TargetUserName)
                };

                results.AddRange(await Task.WhenAll(tasks));

                await transaction.CommitAsync();
                
                totalStopwatch.Stop();
                var totalUpdated = results.Sum(r => r.UpdatedCount);
                var failedCount = results.Count(r => !r.Success);
                
                _logger.LogInformation("用户工作交换完成，总计更新 {TotalCount} 条记录，失败 {FailedCount} 个类型，耗时 {ElapsedMs}ms", 
                    totalUpdated, failedCount, totalStopwatch.ElapsedMilliseconds);
                
                // 记录详细的转移日志
                await LogTransferDetailsAsync(eventDto, results, totalStopwatch.ElapsedMilliseconds);
                
                return results;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                totalStopwatch.Stop();
                
                _logger.LogError(ex, "用户工作交换失败: {OriginalUser} -> {TargetUser}, 耗时 {ElapsedMs}ms", 
                    eventDto.UserName, eventDto.TargetUserName, totalStopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        public async Task<TransferResultDto> UpdatePaymentAutoItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.PaymentAutoItems
                    .Where(x => x.CreatedBy == oldCreator &&
                                (x.Status == PaymentAutoItemStatusEnum.WaitSubmit ||
                                 x.Status == PaymentAutoItemStatusEnum.Auditing))
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("批量付款单创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms", 
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "批量付款单",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "批量付款单创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "批量付款单",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateRecognizeReceiveItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.RecognizeReceiveItems
                    .Where(x => x.CreatedBy == oldCreator &&
                                (x.Status == StatusEnum.waitSubmit ||
                                 x.Status == StatusEnum.waitAudit))
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("收款认领创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms", 
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "收款认领",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "收款认领创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "收款认领",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateRefundItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.RefundItem
                    .Where(x => x.CreatedBy == oldCreator &&
                                x.Status == RefundStatusEnum.waitSubmit)
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("退款处理创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "退款处理",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "退款处理创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "退款处理",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateLossRecognitionItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.LossRecognitionItem
                    .Where(x => x.CreatedBy == oldCreator && 
                                (x.Status == StatusEnum.waitSubmit || 
                                 x.Status == StatusEnum.waitAudit ||
                                 x.Status == StatusEnum.Refuse))
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("损失确认创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms", 
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "损失确认",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "损失确认创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "损失确认",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateInputBillCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.InputBills
                    .Where(x => x.CreatedBy == oldCreator &&
                                x.Status == InputBillStatusEnum.Temporary)
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("进项票创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "进项票",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "进项票创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "进项票",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateCustomizeInvoiceItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.CustomizeInvoiceItem
                    .Where(x => x.CreatedBy == oldCreator &&
                                (x.Status == CustomizeInvoiceStatusEnum.WaitSubmit ||
                                 x.Status == CustomizeInvoiceStatusEnum.Auditing))
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("运营制作开票明细创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "运营制作开票明细",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "运营制作开票明细创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "运营制作开票明细",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdatePreCustomizeInvoiceItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.PreCustomizeInvoiceItem
                    .Where(x => x.CreatedBy == oldCreator &&
                                x.Status == StatusEnum.waitSubmit)
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("预开票申请创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "预开票申请",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "预开票申请创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "预开票申请",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateAdvancePaymentItemCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.AdvancePaymentItem
                    .Where(x => x.CreatedBy == oldCreator &&
                                (x.Status == AdvancePaymentStatusEnum.WaitSubmit ||
                                 x.Status == AdvancePaymentStatusEnum.WaitAudit))
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("提前付款垫资申请创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "提前付款垫资申请",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "提前付款垫资申请创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "提前付款垫资申请",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        public async Task<TransferResultDto> UpdateAdvanceBusinessApplyCreatorAsync(string oldCreator, string newCreator)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var affectedRows = await _dbContext.AdvanceBusinessApply
                    .Where(x => x.CreatedBy == oldCreator &&
                                x.Status == StatusEnum.waitSubmit)
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(x => x.CreatedBy, newCreator)
                        .SetProperty(x => x.UpdatedTime, DateTimeOffset.UtcNow)
                        .SetProperty(x => x.UpdatedBy, "system"));

                stopwatch.Stop();
                _logger.LogInformation("垫资申请单创建人更新完成，影响 {Count} 条记录，耗时 {ElapsedMs}ms",
                    affectedRows, stopwatch.ElapsedMilliseconds);

                return new TransferResultDto
                {
                    BusinessType = "垫资申请单",
                    UpdatedCount = affectedRows,
                    Success = true,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "垫资申请单创建人更新失败，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                return new TransferResultDto
                {
                    BusinessType = "垫资申请单",
                    UpdatedCount = 0,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds
                };
            }
        }

        /// <summary>
        /// 记录转移详细日志
        /// </summary>
        private async Task LogTransferDetailsAsync(UserWorkExchangeEventDto eventDto, List<TransferResultDto> results, long totalElapsedMs)
        {
            try
            {
                var logDetails = new
                {
                    EventType = "用户工作交换",
                    OriginalUser = new { eventDto.UserId, eventDto.UserName, eventDto.DisplayName, eventDto.StaffId },
                    TargetUser = new { eventDto.TargetUserId, eventDto.TargetUserName, eventDto.TargetDisplayName, eventDto.TargetStaffId },
                    ProcessTime = DateTime.UtcNow,
                    TotalElapsedMs = totalElapsedMs,
                    TotalUpdatedCount = results.Sum(r => r.UpdatedCount),
                    SuccessCount = results.Count(r => r.Success),
                    FailedCount = results.Count(r => !r.Success),
                    Details = results.Select(r => new
                    {
                        r.BusinessType,
                        r.UpdatedCount,
                        r.Success,
                        r.ErrorMessage,
                        r.ElapsedMilliseconds
                    }).ToList()
                };

                _logger.LogInformation("用户工作交换详细日志: {@LogDetails}", logDetails);

                // 记录到数据库（如果有专门的日志表）
                // await _dbContext.UserWorkExchangeLog.AddAsync(new UserWorkExchangeLogPo { ... });
                // await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录转移详细日志失败");
            }
        }
    }
}
