﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Models.File;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 应付查询
    /// </summary>
    public interface IDebtQueryService
    {
        /// <summary>
        /// 应付查询
        /// </summary>
        Task<(List<DebtQueryListOutput>, int)> GetListAsync(DebtQueryInput query);

        /// <summary>
        /// 应付明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtDetailQueryListOutput>, int)> GetListDetailAsync(DebtDetailQueryInput query);

        /// <summary>
        /// 应付执行明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtDetailExcuteQueryListOutput>, int)> GetListDetailExcuteAsync(DebtDetailExcuteQueryInput query);

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="relateCode">关联单号</param>
        /// <returns></returns>
        Task<List<DebtQueryOutput>> GetDebtInfoListAsync(string relateCode);

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="purchaseCode">采购单号</param>
        /// <returns></returns>
        Task<List<DebtQueryOutput>> GetDebtInfoListByPurchaseCodeAsync(string purchaseCode);

        /// <summary>
        /// 按公司获取应付单计划明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtDetailBulkOutput>, int)> GetDetailsByCompanies(DebtDetailBulkQuery query);


        /// <summary>
        /// 获取某个应付的可冲销应付
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="billCode"></param>
        /// <param name="userId"></param>
        /// <param name="classify"></param>
        /// <param name="customer"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<AvailableAbatmentsOutput>>> GetAvailableAbatmentsAsync(Guid debtId, string billCode, Guid userId, string classify = "debt", string? customer = null, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// 付款计划查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtDetailQueryListOutput>, int)> GetListDetailQueryAsync(DebtDetailQueryInput query);

        /// <summary>
        /// 付款计划查询总数
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<int> GetDebtPayWarnningCount(DebtDetailQueryInput query);

        /// <summary>
        /// 获取公司、供应商在某个项目下的已使用额度
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="companyId"></param>
        /// <param name="agentId"></param>
        /// <param name="coinCode"></param>
        /// <returns></returns>
        Task<BaseResponseData<decimal>> GetUsedCredit(Guid projectId, Guid companyId, Guid agentId, string coinCode = "CNY");

        /// <summary>
        /// 根据订单号得到应付
        /// </summary>
        /// <param name="purchaseCodes"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<DebtOfProjectOutput>>> GetDebtByPurchaseCodes(List<string> purchaseCodes);
        /// <summary>
        /// 根据订单号得到应付(纯享版)
        /// </summary>
        /// <param name="purchaseCodes"></param>
        /// <returns></returns>
        Task<List<DebtPo>> GetDebtByPurchaseCode(List<string?>? purchaseCodes);
        /// <summary>
        /// 根据入库单号/应付单号得到应付(纯享版)
        /// </summary>
        /// <param name="storeInItemCodes"></param>
        /// <returns></returns>
        Task<List<DebtPo>> GetDebtByStoreInItemCode(List<string?>? storeInItemCodes);
        Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount(DebtQueryInput query);

        Task<MemoryStream> DebtHasInvoiceExport(DebtQueryInput query);

        Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(DebtQueryInput query);

        Task<MemoryStream> DownLoadDebtPayWarnningList(DebtDetailQueryInput query);
        /// <summary>
        /// 根据项目id查询应付单总额
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<DebtSumAmountQueryOutput>>> GetDebtSumAmountByProjectId(List<DebtSumAmountQueryInput> inputs);
        /// <summary>
        /// 获取应付单根据项目id
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="pageNo"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<BaseResponseData<BasePagedData<DebtQueryListOutput>>> GetDebtByProjectId(Guid projectId, DateTime beginTime, DateTime endTime, int pageNo, int pageSize);

        /// <summary>
        /// 更新oa审批状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> UpdateDebtDetailStatus(Guid id, StatusEnum statusEnum);

        /// <summary>
        /// 保存提交账期起始日申请，然后推到OA审批
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SaveAccountPeriodEdit(AccountPeroidEditInput input);
        /// <summary>
        /// 查询账期起始日申请列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtDetailAuditOutput>, int)> GetDebtDetailListQueryAsync(DebtDetailQueryInput query);
        /// <summary>
        /// 查看账期起始日申请附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BizFileUploadOutput>> GetAttachFile(AccountPeriodDateAttachFileInput input);

        /// <summary>
        /// 获取申请的tab 数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<DebtDetailListTableOutput>> GetTabCount(DebtDetailQueryInput query);
        /// <summary>
        /// 获取应付单
        /// </summary>
        /// <param name="debtId"></param>
        /// <returns></returns>
        Task<DebtPo> GetDebtByIdAsync(Guid debtId);
        Task<BaseResponseData<string>> UpdateDebtDiscount(UpdateDebtDiscount input);

        /// <summary>
        /// 取消冲销申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> CancelAbatmentAsync(CancelAbatmentInput input);

        /// <summary>
        /// 更换应付核算部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> ChangeBusinessDeptAsync(ChangeBusinessDeptInput input);
        Task<bool> RepaireDebtDiff(List<Guid> ids);

        /// <summary>
        /// 根据应收id获取不在批量付款里面的应付
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtQueryListOutput>, int)> GetDebtByCreditId(DebtQueryInput query);

        /// <summary>
        /// 创建付款计划清单导出任务（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<ExportTaskResDto>> AddDetailExportTaskAsync(DebtDetailQueryInput query);
    }
}
