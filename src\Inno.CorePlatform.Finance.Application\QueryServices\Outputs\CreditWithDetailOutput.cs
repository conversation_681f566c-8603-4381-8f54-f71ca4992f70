using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 应收查询，出参
    /// </summary>
    public class CreditWithDetailOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        /// <summary>
        /// 应收值
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value) - AbatmentAmount; } }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum? InvoiceType { get; set; }
        public SaleSourceEnum? SaleSource { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum? SaleType { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        
        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 应收明细
        /// </summary>
        public List<CreditDetailPo>? Details { get; set; }
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get;  set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? HospitalName { get;  set; }
    }

    /// <summary>
    /// 获取应收明细出参
    /// </summary>
    public class GetCreditDetailOutput : CreditDetailPo
    {
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }
    }
    
    
    /// <summary>
    /// 导出应收明细信息的输出类，用于封装导出数据的属性。
    /// </summary>
    public class CreditDetailExportOutput : GetCreditDetailOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get;  set; }
        
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        
        /// <summary>
        /// 销售子系统名称。
        /// </summary>
        public string SaleSystemName { get; set; }

        /// <summary>
        /// 销售子系统ID。
        /// </summary>
        public Guid? SaleSystemId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get;  set; }
        
        /// <summary>
        /// 部门
        /// </summary>
        public string DeptName { get; set; }
        
        /// <summary>
        /// 业务单元
        /// </summary>
        public string ServiceName { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        
        /// <summary>
        /// 应收类型
        /// </summary>
        public string CreditTypeStr { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string BusinessDeptFullName { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 应收值
        /// </summary>
        public decimal Value { get; set; }
        
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }
        
        /// <summary>
        /// 未开票金额
        /// </summary>
        public decimal NoInvoiceAmount { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 订货人
        /// </summary>
        public string CustomerPersonName { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value) - AbatmentAmount; } }
        
        /// <summary>
        /// 损失确认金额
        /// </summary>
        public decimal LossRecognitionValue { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public string IsSureIncomeStr
        {
            get { return IsSureIncome == 1 ? "是" : "否"; }
        }
        /// <summary>
        /// 签收时间
        /// </summary>
        public DateTime? IsSureIncomeDate { get; set; }
        
        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? ProbablyBackTime { get; set; }
        
        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? OverdueDay { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }
        
        /// <summary>
        /// 价格类型
        /// </summary>
        public string PriceSourceStr { get; set; }
    }
}
