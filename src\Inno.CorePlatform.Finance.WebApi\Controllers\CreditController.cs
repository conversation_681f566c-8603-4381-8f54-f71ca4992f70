﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 应收
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CreditController : BaseController
    {
        private ILogger<CreditController> _logger;
        private ICreditQueryService _creditQueryService;
        private ICreditAppService _creditAppService;
        private ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IBaseAppService _baseAppService;
        public override bool EnableParameterLogging { get; set; } = true;
        public CreditController(
            ILogger<CreditController> logger,
            ICreditAppService creditAppService,
            IEasyCachingProvider easyCaching, IBaseAppService baseAppService,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            ICreditQueryService creditQueryService,ISubLogService subLog):base(subLog)
        {
            this._logger = logger;
            this._creditQueryService = creditQueryService;
            this._creditAppService = creditAppService;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _easyCaching = easyCaching;
            _baseAppService = baseAppService;
        }

        /// <summary>
        /// 获取未冲销额度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost("GetTotalValue")]
        [SkipLogging]
        public async Task<BaseResponseData<decimal>> GetTotalValue(CreditQueryTotalInput input)
        {
            return await _creditQueryService.GetTotalValue(input);
        }

        /// <summary>
        /// 获取应收信息
        /// </summary>
        /// <param name="relateCode">关联单号</param>
        /// <returns></returns>
        [HttpGet("GetByRelateCode")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditQueryListOutput>>> GetByRelateCode(string relateCode)
        {
            var result = new BaseResponseData<List<CreditQueryListOutput>>();
            try
            {
                var credits = await _creditQueryService.GetByRelateCode(relateCode);
                result.Data = credits;
                result.Code = CodeStatusEnum.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取应付信息失败");
                result.Code = CodeStatusEnum.Failed;
                result.Message = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 获取应收信息
        /// </summary>
        /// <param name="relateCodes">关联单号</param>
        /// <returns></returns>
        [HttpPost("GetByRelateCodes")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditQueryListOutput>>> GetByRelateCodes(List<string> relateCodes)
        {
            var result = new BaseResponseData<List<CreditQueryListOutput>>();
            try
            {
                var credits = await _creditQueryService.GetByRelateCodes(relateCodes);
                result.Data = credits;
                result.Code = CodeStatusEnum.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取应付信息失败");
                result.Code = CodeStatusEnum.Failed;
                result.Message = ex.Message;
            }

            return result;
        }
        /// <summary>
        /// 获取个人应收（旺店通-集成中心）
        /// </summary>
        /// <param name="originOrderNos"></param>
        /// <returns></returns>
        [HttpPost("GetByOriginOrderNos")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditWithDetailOutput>>> GetByOriginOrderNos(List<string> originOrderNos)
        {
            var result = new BaseResponseData<List<CreditWithDetailOutput>>();
            try
            {
                var credits = await _creditQueryService.GetByOriginOrderNos(originOrderNos);
                result.Data = credits;
                result.Code = CodeStatusEnum.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取应付信息失败");
                result.Code = CodeStatusEnum.Failed;
                result.Message = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// 根据订单号得到应收
        /// </summary>
        /// <param name="orderNos"></param>
        /// <returns></returns> 
        [HttpPost("GetCreditByOrderNos")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByOrderNos(List<string> orderNos)
        {
            return await _creditQueryService.GetCreditByOrderNos(orderNos);
        }
        /// <summary>
        /// 根据发票得到应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("GetCreditByInvoiceNo")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByInvoiceNo(CreditOfDeliveryInput input)
        {
            return await _creditQueryService.GetCreditByInvoiceNo(input);
        }

        /// <summary>
        /// 取消应收（仅支持服务费应收）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("Cancel")]
        [SkipLogging]
        public async Task<BaseResponseData<string>> Cancel(CancelCreditInput input)
        {
            return await _creditAppService.Cancel(input);
        }

        /// <summary>
        /// 获取初始应收单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetInitCredits")]
        [SkipLogging]
        public async Task<BaseResponseData<List<CreditDto>>> GetInitCredits(GetInitCreditInput input)
        {
            return await _creditAppService.GetInitCredit(input);
        }

        /// <summary>
        /// 分批确认收入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("RevcfmbillBatchisConfirm")]
        public async Task<BaseResponseData<List<GetPartialIncomeOutput>>> RevcfmbillBatchisConfirm(List<GetPartialIncomeInput> input)
        {
            var ret = new BaseResponseData<List<GetPartialIncomeOutput>>();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "revcfmbillBatchisConfirm_" + input[0].OrderNo;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    _logger.LogInformation($"分批确认收入-销售调用:{jsonStr}");
                    await _baseAppService.CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "admin", "分批确认收入-销售调用");
                    ret = await _creditAppService.RevcfmbillBatchisConfirm(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, ex.Message);
            }
            return ret;
        }

        [HttpPost("ExportCreditTask")]
        [SkipLogging]
        public async Task<ResponseData<CreditListExportOutput>> ExportCreditTask([FromBody] CreditQueryInput query)
        {
            try
            {
                
                var (retList,count)= await _creditQueryService.CreditListExportAsync(query);
                return new ResponseData<CreditListExportOutput>
                {
                    Code = 200,
                    Data = new Data<CreditListExportOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        [HttpPost("ExportInvoiceCreditTask")]
        [SkipLogging]
        public async Task<ResponseData<InvoiceCreditExportListOutput>> ExportInvoiceCreditTask([FromBody] InvoiceCreditQueryInput query)
        {
            try
            {

                var (retList, count) = await _creditQueryService.InvoiceCreditListExportAsync(query);
                return new ResponseData<InvoiceCreditExportListOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceCreditExportListOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 应收更换核算部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreditBusinessDeptChangeTask")]
        public async Task<BaseResponseData<string>> CreditBusinessDeptChangeTask([FromBody] BusinessDeptInput input)
        {
            return await _creditAppService.CreditBusinessDeptChange(input);
        }

        /// <summary>
        /// 运营制作开票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ExportCustomizeInvoiceTask")]
        [SkipLogging]
        public async Task<ResponseData<CustomizeInvoiceExportOutput>> ExportCustomizeInvoiceTask([FromBody] CreditQueryInput query)
        {
            try
            {

                var (retList, count) = await _creditQueryService.CustomizeInvoiceListExportAsync(query);
                return new ResponseData<CustomizeInvoiceExportOutput>
                {
                    Code = 200,
                    Data = new Data<CustomizeInvoiceExportOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                return new ResponseData<CustomizeInvoiceExportOutput>
                {
                    Code = 500,
                    Data = new Data<CustomizeInvoiceExportOutput>
                    {
                        List = new List<CustomizeInvoiceExportOutput>(),
                        Total = 0,
                    },
                    Msg = ex.Message
                };
            }
        }
        
        /// <summary>
        /// 导出应收明细
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>导出的应收明细数据</returns>
        [HttpPost("ExportCreditDetail")]
        public async Task<ResponseData<CreditDetailExportOutput>> ExportCreditDetail([FromBody] CreditQueryInput query)
        {
            try
            {
                // 调用服务获取应收明细导出数据
                var (retList, count) = await _creditQueryService.CreditDetailExportAsync(query);
                return new ResponseData<CreditDetailExportOutput>
                {
                    Code = 200,
                    Data = new Data<CreditDetailExportOutput>
                    {
                        List = retList,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                // 记录异常日志
                _logService.LogAzure("ExportCreditDetail", ex.StackTrace, "导出应付明细");
                throw;
            }
        }
    }
}
