namespace Inno.CorePlatform.Finance.Application.DTOs.UserWorkExchange
{
    /// <summary>
    /// 转移结果DTO
    /// </summary>
    public class TransferResultDto
    {
        /// <summary>
        /// 业务单据类型
        /// </summary>
        public string BusinessType { get; set; }
        
        /// <summary>
        /// 更新的记录数
        /// </summary>
        public int UpdatedCount { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }
    }
}
