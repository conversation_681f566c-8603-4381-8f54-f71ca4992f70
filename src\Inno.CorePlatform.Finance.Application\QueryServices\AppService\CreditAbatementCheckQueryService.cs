using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 应收冲销状态检查查询服务
    /// </summary>
    public class CreditAbatementCheckQueryService : ICreditAbatementCheckQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly ILogger<CreditAbatementCheckQueryService> _logger;

        public CreditAbatementCheckQueryService(
            FinanceDbContext db,
            ILogger<CreditAbatementCheckQueryService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 查找应收但收款计划未冲销的订单
        /// </summary>
        public async Task<BaseResponseData<List<CreditAbatementCheckOutput>>> GetInconsistentAbatementOrdersAsync(CreditAbatementCheckInput input)
        {
            try
            {
                _logger.LogInformation("开始查询应收冲销状态不一致的订单，查询条件：{@Input}", input);

                var creditsQuery = _db.Credits.AsNoTracking();

                // 应用筛选条件
                if (input.SaleOrderNos?.Any() == true)
                {
                    creditsQuery = creditsQuery.Where(c => input.SaleOrderNos.Contains(c.OrderNo));
                }

                if (input.CreditType.HasValue)
                {
                    creditsQuery = creditsQuery.Where(c => c.CreditType == input.CreditType);
                }

                var credits = await creditsQuery.OrderByDescending(c => c.BillDate).ToListAsync();

                if (!credits.Any())
                {
                    _logger.LogInformation("未找到符合条件的应收记录");
                    return BaseResponseData<List<CreditAbatementCheckOutput>>.Success(new List<CreditAbatementCheckOutput>(), "查询成功");
                }

                // 第二步：查询这些应收的冲销金额
                var creditBillCodes = credits.Select(c => c.BillCode).ToList();
                var abatements = await _db.Abatements
                    .Where(a => a.DebtType == "credit" && creditBillCodes.Contains(a.DebtBillCode))
                    .GroupBy(a => a.DebtBillCode)
                    .Select(g => new { DebtBillCode = g.Key, AbatedAmount = g.Sum(x => x.Value) })
                    .ToListAsync();

                // 第三步：组装结果，找出冲销金额不足的记录
                var result = new List<CreditAbatementCheckOutput>();

                foreach (var credit in credits)
                {
                    var abatement = abatements.FirstOrDefault(a => a.DebtBillCode == credit.BillCode);
                    var abatedAmount = abatement?.AbatedAmount ?? 0;

                    // 只返回冲销金额不足或无冲销记录的应收 abatedAmount < credit.Value

                    result.Add(new CreditAbatementCheckOutput
                    {
                        BillCode = credit.BillCode,
                        BillDate = credit.BillDate,
                        AbatedStatus = credit.AbatedStatus,
                        Value = credit.Value,
                        AbatedAmount = abatedAmount,
                        CompanyId = credit.CompanyId,
                        CustomerId = credit.CustomerId,
                        OrderNo = credit.OrderNo,
                        CreditType = (CreditTypeEnum?)credit.CreditType,
                        RelateCode = credit.RelateCode
                    });

                }

                _logger.LogInformation("查询完成，找到 {Count} 条应收冲销状态不一致的记录", result.Count);

                return BaseResponseData<List<CreditAbatementCheckOutput>>.Success(result, "查询成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询应收冲销状态不一致的订单时发生错误");
                return BaseResponseData<List<CreditAbatementCheckOutput>>.Failed(500, $"查询失败：{ex.Message}");
            }
        }
    }
}
