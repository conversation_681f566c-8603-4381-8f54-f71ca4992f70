﻿using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Http;
using System.Text.Json.Serialization;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 发票查询，入参
    /// </summary>
    public class InvoiceQueryInput : BaseQuery
    {

        /// <summary>
        /// 相关联应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public string? CompanyId { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
    }
    /// <summary>
    /// SPD发票查询，入参
    /// </summary>
    public class SPDInvoiceQueryInput : BaseQuery
    {

        /// <summary>
        /// 相关联应收单Id
        /// </summary>
        public List<Guid?>? CreditId { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public List<string?>? InvoiceNos { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public List<string?>? CompanyIds { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public List<Guid?>? ServiceIds { get; set; }

    }
    /// <summary>
    /// 发票清单查询，入参
    /// </summary>
    public class InvoicesQueryInput : BaseQuery
    {
        /// <summary>
        /// 发票类型
        /// </summary> 
        public string? Type { get; set; }
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 发票号模糊查询
        /// </summary> 
        public string? InvoiceNoLike { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司id集合
        /// </summary>
        public List<Guid>? CompanyIdList { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        ///  编号  开票单
        /// </summary>

        public string? Code { get; set; }

        /// <summary>
        /// 单号   应收单
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateBeging { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateEnd { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 1=作废，反之全部
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        public List<Guid?>? ServiceIds { get; set; } = new List<Guid?>();

        public bool? IsgreaterThanZero { get; set; }

        public SPDStatusEnum? SPDStatus { get; set; }

        /// <summary>
        /// 是否需要大于0
        /// </summary>
        public int IsPassZero { get; set; }

        /// <summary>
        /// 三方单号
        /// </summary>
        public string? ShipmentCode { get; set; }

        /// <summary>
        /// 阳采状态
        /// </summary>
        public SunPurchaseStatusEnum? SunPurchaseStatus { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string? CustomerEmail { get; set; }

        /// <summary>
        /// 正负数发票
        /// </summary>
        public int? QuantityFilter { get; set; }

        /// <summary>
        /// 预开票单号
        /// </summary>
        public string? PreCustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string? department { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        public string? CurrentUserName { get; set; }
        /// <summary>
        /// 填报人
        /// </summary>
        public List<string>? UpdatedBy { get; set; }
        /// <summary>
        /// 简单模式（不计算可认款金额）
        /// </summary>
        public bool? IsSimple { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        public List<string>? ApplyUserName { get; set; }
        /// <summary>
        /// 是否是预开票
        /// </summary>
        public int? IsPreInvoice { get; set; }
    }

    /// <summary>
    /// SPD审核接口入参
    /// </summary>
    public class SPDApproveInput
    {
        [JsonPropertyName("invoice_num")]
        public string? invoice_num { get; set; }
        public string? InvoiceCode { get; set; }

        [JsonPropertyName("apply_status")]
        public SPDStatusEnum? apply_status { get; set; }

        [JsonPropertyName("remark")]
        public string? remark { get; set; }
    }

    /// <summary>
    /// 推送给SPD
    /// </summary>
    public class PushSPDInvoicesInput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public List<string>? InvoiceNos { get; set; }

    }

    /// <summary>
    /// 推送给安贞
    /// </summary>
    public class PushAzInvoicesInput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public List<string>? InvoiceNos { get; set; }
    } 
    /// <summary>
    /// 更换应收关系入参
    /// </summary>
    public class ChangeRelationshipInput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 新应收单号
        /// </summary> 
        public List<string?>? NewCreditBillCodes { get; set; }
        /// <summary>
        /// 旧应收单号
        /// </summary> 
        public string? OldCreditBillCode { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 更换应收关系入参
    /// </summary>
    public class ChangeRelationshipKingdeeInput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string invoiceNo { get; set; }
        /// <summary>
        /// 旧应收单号集合
        /// </summary> 
        public List<string?>? oldArBillNo { get; set; }
        /// <summary>
        /// 新应收单号集合
        /// </summary> 
        public List<NewCreditInfo>? arBillEntry { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string? user { get; set; }
    }

    /// <summary>
    /// 新应收集合
    /// </summary>
    public class NewCreditInfo
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? arBillNum { get; set; }
        /// <summary>
        /// 应收单金额
        /// </summary>
        public decimal amount { get; set; }
    }

    /// <summary>
    /// 更换应收关系入参给销售
    /// </summary>
    public class ChangeRelationshipInputOfSale
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 旧应收单号集合
        /// </summary> 
        public List<string?>? OldCreditCodes { get; set; }

        /// <summary>
        /// 新应收单号集合
        /// </summary> 
        public List<NewCreditSaleInfo>? NewCreditInfo { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 新应收集合
    /// </summary>
    public class NewCreditSaleInfo
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? NewCreditCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? NewOrderNo { get; set; }

        /// <summary>
        /// 应收单金额
        /// </summary>
        public decimal Amount { get; set; }
    }

    /// <summary>
    /// 编辑阳采发票详情入参
    /// </summary>
    public class EditSunPurchaseInvoiceDetailInput
    {
        public Guid? id { get; set; }
        /// <summary>
        /// 耗材统编代码
        /// </summary>
        public string? hctbdm { get; set; }
        /// <summary>
        /// 医院本地编码
        /// </summary>
        public string? qybddm { get; set; }
        /// <summary>
        /// 注册证号
        /// </summary>
        public string? zczh { get; set; }
        /// <summary>
        /// 采购单价
        /// </summary>
        public decimal? hsdj { get; set; }
        /// <summary>
        /// 采购数量
        /// </summary>
        public decimal? spsl { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? purchaseCode { get; set; }
        /// <summary>
        /// 阳采销售订单号
        /// </summary>
        public string? xsddh { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 生产批号
        /// </summary>
        public string? scph { get; set; }
        /// <summary>
        /// 关联明细编号
        /// </summary>
        public string? glmxbh { get; set; }
        /// <summary>
        /// 规格型号说明
        /// </summary>
        public string? ggxhsm { get; set; }
    }

    /// <summary>
    /// 提交发票填报入参
    /// </summary>
    public class SunPurchaseInvoiceSubmitInput
    {
        public string? invoiceNo { get; set; }
        public string? finishInvoiceNo { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? finishInvoiceCode { get; set; }
        /// <summary>
        /// 配送点
        /// </summary>
        public string? psdbm { get; set; }
        /// <summary>
        /// 配送单
        /// </summary>
        public string? psdh { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? purchaseCode { get; set; }
        public string? remark { get; set; }

        /// <summary>
        /// 采购类型
        /// </summary>
        public string? cglx { get; set; }

        /// <summary>
        /// 医院编码
        /// </summary>
        public string? yybm { get; set; }

        /// <summary>
        /// 填报人
        /// </summary>
        public string? updatedBy { get; set; }
    }

    /// <summary>
    /// 阳采发票提交部分参数（从PM获取）
    /// </summary>
    public class SunPurchaseInvoiceInfoForPmInput
    {
        /// <summary>
        /// 采购单号
        /// </summary>
        public string purchaseCode { get; set; }
        /// <summary>
        /// 阳采采购单类型
        /// </summary>
        public int purchaseOrderType { get; set; }
        /// <summary>
        /// 阳采采购单类型名
        /// </summary>
        public string purchaseOrderTypeName { get; set; }

        /// <summary>
        /// 阳采采购方式
        /// </summary>
        public int purchaseModel { get; set; }
        /// <summary>
        /// 阳采采购方式名称
        /// </summary>
        public string purchaseModelName { get; set; }
        /// <summary>
        /// 企业编码
        /// </summary>
        public string enterpriseCode { get; set; }
        /// <summary>
        /// 医院编码
        /// </summary>
        public string hospitalCode { get; set; }
        /// <summary>
        /// 配送点编码集合
        /// </summary>
        public List<BaseOutput> deliveryPoints { get; set; }
    }

    /// <summary>
    /// 获取阳采配送单
    /// </summary>
    public class ShycShipmentDetaillnput
    {
        /// <summary>
        /// 医院配送点编码
        /// </summary>
        public string? deliveryPointCode { get; set; }
        /// <summary>
        /// 医院编码
        /// </summary>
        public string? hospitalCode { get; set; }
        /// <summary>
        /// 货号id
        /// </summary>
        public string? productId { get; set; }
        /// <summary>
        /// 品名id
        /// </summary>
        public string? productNameId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 采购单编号
        /// </summary>
        public string? purchaseCode { get; set; }
    }

    /// <summary>
    /// 根据应收单获取冲销明细
    /// </summary>
    public class AbamentByCreditBillCodeQueryInput : BaseQuery
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
    }

    /// <summary>
    /// 批量下载发票入参
    /// </summary>
    public class BatchDownLoadInvoiceInput
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        public string TrueName { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string CustomerEmail { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public List<Guid>? CompanyIdList { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateBeging { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateEnd { get; set; }

        /// <summary>
        /// 公司发票信息
        /// </summary>
        public List<BatchDownLoadInvoiceCompanyInput> CompanyInvoices { get; set; }

        /// <summary>
        /// 发票
        /// </summary>
        public class BatchDownLoadInvoiceCompanyInput
        {
            /// <summary>
            /// 发票号
            /// </summary> 
            public string InvoiceNo { get; set; }

            /// <summary>
            /// 公司
            /// </summary>
            public Guid? CompanyId { get; set; }
        }

        /// <summary>
        /// 发票配送入参
        /// </summary>
        public class InvoiceShipmentInput
        {
            public string? InvoiceNo { get; set; }
        }

        /// <summary>
        /// 发票配送出参
        /// </summary>
        public class InvoiceShipmentOutput
        {
            public string? InvoiceShipmentId { get; set; }
            public string? InvoiceShipmentCode { get; set; }
            public int? Status { get; set; }
            public string? StatusDesc { get; set; }
            public string? InvoiceNo { get; set; }
            public string? InvoiceCode { get; set; }
            public string? InvoiceType { get; set; }
            public decimal? InvoiceAmount { get; set; }
            public decimal? InvoiceAmountNoTax { get; set; }
            public string? InvoiceTime { get; set; } // 或使用 DateTime?
            public string? InvoiceGetBy { get; set; }
            public string? InvoiceGetByName { get; set; }
            public string? InvoiceGetTime { get; set; } // 或使用 DateTime?
            public string? CustomerId { get; set; }
            public string? CustomerName { get; set; }
            public string? HospitalName { get; set; }
            public string? CompanyId { get; set; }
            public string? CompanyName { get; set; }
            public int? InvoiceStatus { get; set; }
            public string? InvoiceStatusDesc { get; set; }
            public string? ExpressType { get; set; }
            public string? ExpressTypeName { get; set; }
            public string? ExpressNo { get; set; }
            public string? SignerBy { get; set; }
            public string? InvoiceCheckCode { get; set; }
            public string? CustomizeInvoiceCode { get; set; }
            public string? InvoiceCreatedBy { get; set; }
            public string? InvoiceCreatedByName { get; set; }
            public string? SignerFileId { get; set; }
            public string? SignerPhotoId { get; set; }
            public decimal? TaxAmount { get; set; }
            public string? SignTime { get; set; } // 或使用 DateTime?
            public string? BusinessUnitId { get; set; }
            public string? BusinessUnitName { get; set; }
        }
    }
}
