﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Data;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using Inno.CorePlatform.Common.Utility.Expressions;
using MongoDB.Driver.Linq;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Mapster;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Common.Http;
using NPOI.SS.Formula.Functions;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Common.DTO;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class InventoryQueryService : QueryAppService, IInventoryQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IPCApiClient _pCApiClient;
        private readonly ICoordinateClient _coordinateclient;
        private readonly IConfiguration _configuration;
        private readonly ISellRecognizeApiClient _sellRecognizeApiClient;

        public InventoryQueryService(
           IAppServiceContextAccessor? contextAccessor,
           FinanceDbContext db, IPCApiClient pCApiClient,
           ICoordinateClient coordinateclient,
           ISellRecognizeApiClient sellRecognizeApiClient,
           IConfiguration configuration
         ) : base(contextAccessor)
        {
            this._db = db;
            _pCApiClient = pCApiClient;
            _coordinateclient = coordinateclient;
            _configuration = configuration;
            _sellRecognizeApiClient = sellRecognizeApiClient;
        }
        public async Task<(List<CreditRecordDetailDto>, int)> GetCreditRecordDetailListAsync(DetailRecordBaseQueryDto query)
        {
            Expression<Func<CreditRecordDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件

            exp = exp.And(z => z.CreditRecordItemId == query.ItemId);

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {

            }
            #endregion

            IQueryable<CreditRecordDetailPo> baseQuery = _db.CreditRecordDetails.Include(x => x.Credit).Where(exp).AsNoTracking();

            if (query.customerId.HasValue)
            {
                baseQuery = baseQuery.Where(x => x.Credit.CustomerId == query.customerId);
            }

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页查询主数据
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            // 如果没有数据，直接返回
            if (!list.Any())
            {
                return (new List<CreditRecordDetailDto>(), count);
            }

            // 获取所有CreditId用于批量查询
            var creditIds = list.Select(x => x.CreditId).Distinct().ToList();

            // 顺序执行两个聚合查询，避免 DbContext 并发问题
            var sureIncomeAmounts = await _db.CreditSureIncome
                .Where(x => creditIds.Contains(x.CreditId))
                .GroupBy(x => x.CreditId)
                .Select(g => new { CreditId = g.Key, TotalAmount = g.Sum(x => x.Value) })
                .Where(x => x.TotalAmount > 0) // 在数据库层面过滤
                .ToDictionaryAsync(x => x.CreditId, x => x.TotalAmount);

            var invoiceAmounts = await _db.InvoiceCredits
                .Where(x => x.CreditId.HasValue && creditIds.Contains(x.CreditId.Value) && x.IsCancel != true)
                .GroupBy(x => x.CreditId.Value)
                .Select(g => new { CreditId = g.Key, TotalAmount = g.Sum(x => x.CreditAmount ?? 0) })
                .Where(x => x.TotalAmount > 0) // 在数据库层面过滤
                .ToDictionaryAsync(x => x.CreditId, x => x.TotalAmount);

            // 转换为DTO并设置金额
            var result = list.Select(item =>
            {
                var dto = item.Adapt<CreditRecordDetailDto>();

                // 设置确认收入金额
                // 根据 ServiceConfirmRevenuePlanModeEnum 判断确认收入金额的计算方式
                if (item.Credit.ServiceConfirmRevenuePlanModeEnum == ServiceConfirmRevenuePlanModeEnum.InstallmentGeneration)
                {
                    // 分期生成：使用 CreditSureIncome 中的值
                    if (sureIncomeAmounts.TryGetValue(item.CreditId, out var sureAmount))
                    {
                        dto.SureIncomeAmount = sureAmount;
                    }
                }
                else
                {
                    // 一次性生成或未设置：根据IsSureIncome判断，未确认收入（IsSureIncome=0）确认收入金额为0
                    dto.SureIncomeAmount = item.Credit.IsSureIncome == 1 ? item.Credit.Value : 0;
                }

                // 设置开票金额
                if (invoiceAmounts.TryGetValue(item.CreditId, out var invoiceAmount))
                {
                    dto.InvoiceAmount = invoiceAmount;
                }

                return dto;
            }).ToList();

            return (result, count);
        }

        public async Task<(List<CreditRecordOutputDto>, int)> GetCreditRecordListAsync(RecordBaseQueryDto query)
        {
            Expression<Func<CreditRecordItemPo, bool>> exp = z => 1 == 1;
            exp = await InitCreditExp(query, exp);
            IQueryable<CreditRecordItemPo> baseQuery = _db.CreditRecordItems.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<CreditRecordOutputDto>>(), count);
        }

        private async Task<Expression<Func<CreditRecordItemPo, bool>>> InitCreditExp(RecordBaseQueryDto query, Expression<Func<CreditRecordItemPo, bool>> exp)
        {
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }

            #region 查询条件
            //query.saleSystemId = Guid.Parse("3f3c991c-f37a-4109-3d85-08dc156ec4ad");
            if (query.saleSystemId.HasValue)
            {
                // 先根据当前机构过滤数据
                if (query.customerId.HasValue)
                {
                    var ids = await _db.CreditRecordDetails.Include(x => x.Credit).Where(x => x.Credit.CustomerId == query.customerId).Select(x => x.CreditRecordItemId).Distinct().ToListAsync();
                    // 没有详情的数据公司范围内也需要展示
                    var ids2 = await _db.CreditRecordItems.Where(a => !_db.CreditRecordDetails.Any(b => b.CreditRecordItemId == a.Id)).Select(a => a.Id).ToListAsync();
                    ids.AddRange(ids2);
                    if (ids.Any())
                    {
                        exp = exp.And(z => ids.Any(p => p == z.Id));
                    }
                    else
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                }
                // 再根据销售子系统id获取公司，根据公司过滤数据
                var sellInput = new SaleSystemCompanyQueryInput
                {
                    PageIndex = 1,
                    PageSize = 10000,
                    Ids = query.saleSystemIds
                };
                var sellRet = await _sellRecognizeApiClient.GetCompanyListBySaleSystemId(sellInput);
                //var sellRet = await _sellRecognizeApiClient.GetCompanyBySaleSystemId(query.saleSystemId.Value.ToString());
                if (sellRet.Code == CodeStatusEnum.Success)
                {
                    if (sellRet.Data != null && sellRet.Data.list != null && sellRet.Data.list.Any())
                    {
                        var companyIds = sellRet.Data.list.Select(x => x.companyId).ToList();
                        if (companyIds != null && companyIds.Any())
                        {
                            query.companyId = companyIds.FirstOrDefault();
                            exp = exp.And(z => companyIds.Any(p => p == z.CompanyId));
                        }
                    }
                    else
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                }
                else
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateBeging)));
            }
            if (!string.IsNullOrWhiteSpace(query.code))
            {
                exp = exp.And(z => z.Code == query.code);
            }
            if (query.companyId.HasValue && query.companyId != Guid.Empty)
            {
                //春立系统使用多个查询
                if (!query.saleSystemId.HasValue)
                {
                    exp = exp.And(z => z.CompanyId == query.companyId);
                }
            }
            else
            {

                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                            break;
                        }
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.Code.Contains(query.searchKey) || z.CompanyName.Contains(query.searchKey));
            }
            if (query.Classify.HasValue)
            {
                exp = exp.And(z => z.Classify == query.Classify);
            }
            else
            {
                exp = exp.And(z => z.Classify != CreditRecordItemClassifyEnum.SignedNonInvoiced);
            }
            #endregion
            return exp;
        }

        /// <summary>
        /// 应收盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<CreditDownLoadOutput>>> CreditDownLoad(RecordBaseQueryDto query)
        {
            var ret = BaseResponseData<List<CreditDownLoadOutput>>.Success("操作成功！");
            Expression<Func<CreditRecordItemPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitCreditExp(query, exp);
            #endregion
            var itemQuery = _db.CreditRecordItems.Where(exp).AsNoTracking();
            var itemIds = await itemQuery.Select(p => p.Id).ToListAsync();
            if (itemIds != null && itemIds.Any())
            {
                // 获取基础数据
                var baseData = await _db.CreditRecordDetails.
                                Include(p => p.CreditRecordItem).
                                Include(p => p.Credit).
                                Where(p => itemIds.ToHashSet().Contains(p.CreditRecordItemId)).
                                OrderBy(p => p.CreditRecordItem.BillDate).
                                AsNoTracking().ToListAsync();

                if (baseData.Any())
                {
                    // 获取所有CreditId用于批量查询
                    var creditIds = baseData.Select(x => x.CreditId).Distinct().ToList();

                    // 批量查询确认收入金额
                    var sureIncomeAmounts = await _db.CreditSureIncome
                        .Where(x => creditIds.Contains(x.CreditId))
                        .GroupBy(x => x.CreditId)
                        .Select(g => new { CreditId = g.Key, TotalAmount = g.Sum(x => x.Value) })
                        .ToDictionaryAsync(x => x.CreditId, x => x.TotalAmount);

                    // 批量查询开票金额
                    var invoiceAmounts = await _db.InvoiceCredits
                        .Where(x => x.CreditId.HasValue && creditIds.Contains(x.CreditId.Value))
                        .GroupBy(x => x.CreditId.Value)
                        .Select(g => new { CreditId = g.Key, TotalAmount = g.Sum(x => x.CreditAmount ?? 0) })
                        .ToDictionaryAsync(x => x.CreditId, x => x.TotalAmount);

                    // 转换为输出对象并计算各种金额
                    ret.Data = baseData.Select(p => {
                        var output = new CreditDownLoadOutput
                        {
                            Id = p.Id,
                            AbatedVaule = p.AbatedValue,
                            CustomerName = p.Credit.CustomerName,
                            BillDate = p.CreditRecordItem.BillDate,
                            CreditBillDate = p.Credit.BillDate ?? DateTime.MinValue,
                            Code = p.CreditRecordItem.Code,
                            CompanyName = p.Credit.CompanyName,
                            CreditCode = p.Credit.BillCode,
                            Vaule = p.Credit.Value,
                            Balance = p.Value,
                            ServiceName = p.Credit.ServiceName,
                            OrderNo = p.Credit.OrderNo,
                            BusinessDeptFullName = p.Credit.BusinessDeptFullName,
                            BusinessDeptId = p.Credit.BusinessDeptId,
                            ProjectName = p.Credit.ProjectName,
                            ProjectCode = p.Credit.ProjectCode,
                            LossValue = p.Credit.LossRecognitionValue
                        };

                        // 计算确认收入金额
                        if (p.Credit.ServiceConfirmRevenuePlanModeEnum == ServiceConfirmRevenuePlanModeEnum.InstallmentGeneration)
                        {
                            // 分期生成：使用 CreditSureIncome 中的值
                            output.SureIncomeAmount = sureIncomeAmounts.GetValueOrDefault(p.CreditId, 0);
                        }
                        else
                        {
                            // 一次性生成或未设置：根据IsSureIncome判断
                            output.SureIncomeAmount = p.Credit.IsSureIncome == 1 ? p.Credit.Value : 0;
                        }

                        // 设置开票金额
                        output.InvoiceAmount = invoiceAmounts.GetValueOrDefault(p.CreditId, 0);

                        // 计算未冲销金额、未确认收入金额、未开票金额
                        output.UnAbatedAmount = p.Credit.Value - p.AbatedValue;
                        output.UnSureIncomeAmount = p.Credit.Value - output.SureIncomeAmount;
                        output.UnInvoiceAmount = p.Credit.Value - output.InvoiceAmount;

                        return output;
                    }).ToList();
                }
            }
            return ret;
        }

        /// <summary>
        /// 应收盘点下载（发票信息）- 已废弃，不再使用发票相关列
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Obsolete("已废弃，应收盘点导出不再包含发票相关列")]
        public Task<BaseResponseData<List<CreditDownLoadOutput>>> CreditDownLoadInvoiceInfo(List<Guid> ids)
        {
            // 返回空数据，因为发票相关列已被移除
            var ret = BaseResponseData<List<CreditDownLoadOutput>>.Success("操作成功！");
            ret.Data = new List<CreditDownLoadOutput>();
            return Task.FromResult(ret);
        }

        /// <summary>
        /// 订货系统明细专用查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditRecordDetailDto>, int)> GetCreditRecordDetailsListByIds(DetailRecordBaseQueryDto query)
        {
            Expression<Func<CreditRecordDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件

            if (query.ids == null)
            {
                return (new List<CreditRecordDetailDto>(), 0);
            }
            if (!query.customerId.HasValue)
            {
                return (new List<CreditRecordDetailDto>(), 0);
            }
            exp = exp.And(z => query.ids.Contains(z.CreditRecordItemId));

            #endregion

            IQueryable<CreditRecordDetailPo> baseQuery = _db.CreditRecordDetails.Include(x => x.Credit).Where(exp).AsNoTracking();

            if (query.customerId.HasValue)
            {
                baseQuery = baseQuery.Where(x => x.Credit.CustomerId == query.customerId);
            }

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<CreditRecordDetailDto>>(), count);
        }

        public async Task<(List<DebtRecordDetailDto>, int)> GetDebtRecordDetailListAsync(DetailRecordBaseQueryDto query)
        {
            Expression<Func<DebtRecordDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件

            exp = exp.And(z => z.DebtRecordItemId == query.ItemId);



            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {

            }
            #endregion

            IQueryable<DebtRecordDetailPo> baseQuery = _db.DebtRecordDetails.Include(x => x.Debt).Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<DebtRecordDetailDto>>(), count);
        }

        public async Task<(List<DebtRecordOutputDto>, int)> GetDebtRecordListAsync(RecordBaseQueryDto query)
        {
            Expression<Func<DebtRecordItemPo, bool>> exp = z => 1 == 1;
            exp = await InitDebtExp(query, exp);
            IQueryable<DebtRecordItemPo> baseQuery = _db.DebtRecordItems.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<DebtRecordOutputDto>>(), count);
        }

        private async Task<Expression<Func<DebtRecordItemPo, bool>>> InitDebtExp(RecordBaseQueryDto query, Expression<Func<DebtRecordItemPo, bool>> exp)
        {
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }
            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateBeging)));
            }
            if (!string.IsNullOrWhiteSpace(query.code))
            {
                exp = exp.And(z => z.Code == query.code);
            }
            if (query.companyId.HasValue && query.companyId != Guid.Empty)
            {
                exp = exp.And(z => z.CompanyId == query.companyId);
            }
            else
            {

                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                            break;
                        }
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.Code.Contains(query.searchKey) || z.CompanyName.Contains(query.searchKey));
            }
            #endregion
            if (query.ProjectCode != null)
            {
                var itemIds = await _db.DebtRecordDetails.Include(x => x.Debt).Where(x => x.Debt.ProjectCode == query.ProjectCode).Select(o => o.DebtRecordItemId).Distinct().ToListAsync();
                if (itemIds != null && itemIds.Count > 0)
                {
                    exp = exp.And(z => itemIds.ToHashSet().Contains(z.Id));
                }
                else
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }
            return exp;
        }

        /// <summary>
        /// 应付盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<DebtDownLoadOutput>>> DebtDownLoad(RecordBaseQueryDto query)
        {
            var ret = BaseResponseData<List<DebtDownLoadOutput>>.Success("操作成功！");
            Expression<Func<DebtRecordItemPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitDebtExp(query, exp);
            #endregion
            var itemQuery = _db.DebtRecordItems.Where(exp).AsNoTracking();
            var itemIds = await itemQuery.Select(p => p.Id).ToListAsync();
            if (itemIds != null && itemIds.Any())
            {
                ret.Data = await _db.DebtRecordDetails.
                                Include(p => p.DebtRecordItem).
                                Include(p => p.Debt).
                                Where(p => itemIds.ToHashSet().Contains(p.DebtRecordItemId)).
                                OrderBy(p => p.DebtRecordItem.BillDate).
                                AsNoTracking().Select(p => new DebtDownLoadOutput
                                {
                                    AbatedVaule = p.AbatedValue,
                                    AgentName = p.Debt.AgentName,
                                    BillDate = p.DebtRecordItem.BillDate,
                                    DebtBillDate = p.Debt.BillDate,
                                    Code = p.DebtRecordItem.Code,
                                    CompanyName = p.Debt.CompanyName,
                                    DebtCode = p.Debt.BillCode,
                                    Vaule = p.Debt.Value,
                                    Balance = p.Value,
                                    ServiceName = p.Debt.ServiceName,
                                    CustomerName = p.Debt.CustomerName,
                                    ProjectName = p.Debt.ProjectName,
                                    ProjectCode = p.Debt.ProjectCode,
                                }).ToListAsync();
            }
            return ret;
        }

        public async Task<(List<PaymentRecordOutputDto>, int)> GetPaymentRecordListAsync(RecordBaseQueryDto query)
        {
            Expression<Func<PaymentRecordItemPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitPaymentExp(query, exp);
            #endregion

            IQueryable<PaymentRecordItemPo> baseQuery = _db.PaymentRecordItems.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<PaymentRecordOutputDto>>(), count);
        }

        private async Task<Expression<Func<PaymentRecordItemPo, bool>>> InitPaymentExp(RecordBaseQueryDto query, Expression<Func<PaymentRecordItemPo, bool>> exp)
        {
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateBeging)));
            }
            if (!string.IsNullOrWhiteSpace(query.code))
            {
                exp = exp.And(z => z.Code.Equals(query.code));
            }
            if (query.companyId.HasValue && query.companyId != Guid.Empty)
            {
                exp = exp.And(z => z.CompanyId == query.companyId);
            }
            else
            {
                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                            break;
                        }
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.Code.Contains(query.searchKey) || z.CompanyName.Contains(query.searchKey));
            }

            return exp;
        }

        public async Task<(List<PaymentRecordDetailDto>, int)> GetPaymentRecordDetailListAsync(DetailRecordBaseQueryDto query)
        {
            Expression<Func<PaymentRecordDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件

            exp = exp.And(z => z.PaymentRecordItemId == query.ItemId);

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {

            }
            #endregion

            IQueryable<PaymentRecordDetailPo> baseQuery = _db.PaymentRecordDetails.Include(x => x.Payment).Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<PaymentRecordDetailDto>>(), count);
        }

        /// <summary>
        /// 付款盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<PaymentDownLoadOutput>>> PaymentDownLoad(RecordBaseQueryDto query)
        {
            var ret = BaseResponseData<List<PaymentDownLoadOutput>>.Success("操作成功！");
            Expression<Func<PaymentRecordItemPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitPaymentExp(query, exp);
            #endregion
            var itemQuery = _db.PaymentRecordItems.Where(exp).AsNoTracking();
            var itemIds = await itemQuery.Select(p => p.Id).ToListAsync();
            if (itemIds != null && itemIds.Any())
            {
                ret.Data = await _db.PaymentRecordDetails.
                                Include(p => p.Payment).
                                Include(p => p.PaymentRecordItem).
                                Where(p => itemIds.ToHashSet().Contains(p.PaymentRecordItemId)).
                                OrderBy(p => p.PaymentRecordItem.BillDate).
                                AsNoTracking().Select(p => new PaymentDownLoadOutput
                                {
                                    AbatedVaule = p.AbatedValue,
                                    AgentName = p.Payment.AgentName,
                                    BillDate = p.PaymentRecordItem.BillDate,

                                    PaymentBillDate = p.Payment.BillDate,
                                    Code = p.PaymentRecordItem.Code,
                                    CompanyName = p.PaymentRecordItem.CompanyName,
                                    PayCode = p.Payment.Code,
                                    Vaule = p.Payment.Value,
                                    Balance = p.Value
                                }).ToListAsync();
            }
            return ret;
        }
        #region

        /// <summary>
        /// 获得垫资盘点
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<AdvanceRecordOutputDto>, int)> GetAdvanceRecordListAsync(RecordBaseQueryDto query)
        {
            Expression<Func<AdvanceFundBusinessCheckItemPO, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitAdvanceExp(query, exp);
            #endregion

            IQueryable<AdvanceFundBusinessCheckItemPO> baseQuery = _db.AdvanceFundBusinessCheckItem.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();

            return (list.Adapt<List<AdvanceRecordOutputDto>>(), count);
        }
        private async Task<Expression<Func<AdvanceFundBusinessCheckItemPO, bool>>> InitAdvanceExp(RecordBaseQueryDto query, Expression<Func<AdvanceFundBusinessCheckItemPO, bool>> exp)
        {
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
            }
            if (!string.IsNullOrWhiteSpace(query.billDateBeging) && !string.IsNullOrWhiteSpace(query.billDateEnd))
            {
                exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(query.billDateBeging)));
            }
            if (!string.IsNullOrWhiteSpace(query.code))
            {
                exp = exp.And(z => z.Code.Equals(query.code));
            }
            if (query.companyId.HasValue && query.companyId != Guid.Empty)
            {
                exp = exp.And(z => z.CompanyId == query.companyId);
            }
            else
            {
                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                            break;
                        }
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.Code.Contains(query.searchKey) || z.CompanyName.Contains(query.searchKey));
            }

            return exp;
        }
        public async Task<(List<AdvanceRecordDetailDto>, int)> GetAdvanceRecordDetailListAsync(DetailRecordBaseQueryDto query)
        {
            Expression<Func<AdvanceFundBusinessCheckDetailPO, bool>> exp = z => 1 == 1;
            #region 查询条件

            exp = exp.And(z => z.AdvanceFundBusinessCheckItemId == query.ItemId);

            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {

            }
            #endregion

            IQueryable<AdvanceFundBusinessCheckDetailPO> baseQuery = _db.AdvanceFundBusinessCheckDetail.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).ToListAsync();
            var item = await _db.AdvanceFundBusinessCheckItem.Where(t => t.Id == query.ItemId).FirstOrDefaultAsync();
            var result = list.Adapt<List<AdvanceRecordDetailDto>>();
            result.ForEach(t =>
            {
                t.CompanyName = item?.CompanyName;
            });
            return (result, count);
        }
        /// <summary>
        /// 垫资盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<AdvanceRecordDetailDto>>> AdvanceDownLoad(RecordBaseQueryDto query)
        {
            var ret = BaseResponseData<List<AdvanceRecordDetailDto>>.Success("操作成功！");
            Expression<Func<AdvanceFundBusinessCheckItemPO, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = await InitAdvanceExp(query, exp);
            #endregion
            var itemList = await _db.AdvanceFundBusinessCheckItem.Where(exp).AsNoTracking().ToListAsync();
            var itemIds = itemList.Select(p => p.Id).ToList();
            if (itemIds != null && itemIds.Any())
            {
                var details = await _db.AdvanceFundBusinessCheckDetail.
                                      Where(p => itemIds.ToHashSet().Contains(p.AdvanceFundBusinessCheckItemId)).
                                      OrderBy(p => p.CreatedTime).
                                      AsNoTracking().ToListAsync();
                ret.Data = details.Select(t => t.Adapt<AdvanceRecordDetailDto>()).ToList();
                var advanceCodes = details.Select(p => p.AdvanceBusinessApplyCode).Distinct().ToList();
                var advances = await _db.AdvanceBusinessApply.Where(p => advanceCodes.Contains(p.Code)).Select(p => new { p.Code, p.IsInvoice }).ToListAsync();
                ret.Data.ForEach(t =>
                {
                    var item = itemList.FirstOrDefault(x => x.Id == t.AdvanceFundBusinessCheckItemId);
                    var ad = advances.FirstOrDefault(p => p.Code == t.AdvanceBusinessApplyCode);
                    t.CompanyName = item?.CompanyName;
                    t.IsInvoice = ad == null ? 0 : ad.IsInvoice ? 1 : 0;
                });
            }
            return ret;
        }

        /// <summary>
        /// 垫资盘点导出（按盘点单Ids）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<AdvanceRecordDetailDto>>> AdvanceDownLoadByIds(AdvanceRecordExportQueryDto query)
        {
            var ret = BaseResponseData<List<AdvanceRecordDetailDto>>.Success("操作成功！");

            if (query.InventoryIds == null || !query.InventoryIds.Any())
            {
                ret.Data = new List<AdvanceRecordDetailDto>();
                return ret;
            }

            // 直接根据盘点单Ids查询详情数据
            var details = await _db.AdvanceFundBusinessCheckDetail
                .Where(p => query.InventoryIds.Contains(p.AdvanceFundBusinessCheckItemId))
                .OrderBy(p => p.CreatedTime)
                .AsNoTracking()
                .ToListAsync();

            if (!details.Any())
            {
                ret.Data = new List<AdvanceRecordDetailDto>();
                return ret;
            }

            ret.Data = details.Select(t => t.Adapt<AdvanceRecordDetailDto>()).ToList();

            // 获取盘点单信息用于设置公司名称
            var itemList = await _db.AdvanceFundBusinessCheckItem
                .Where(x => query.InventoryIds.Contains(x.Id))
                .AsNoTracking()
                .ToListAsync();

            // 获取垫资申请单信息用于设置开票状态
            var advanceCodes = details.Select(p => p.AdvanceBusinessApplyCode).Distinct().ToList();
            var advances = await _db.AdvanceBusinessApply
                .Where(p => advanceCodes.Contains(p.Code))
                .Select(p => new { p.Code, p.IsInvoice })
                .ToListAsync();

            // 设置额外信息
            ret.Data.ForEach(t =>
            {
                var item = itemList.FirstOrDefault(x => x.Id == t.AdvanceFundBusinessCheckItemId);
                var ad = advances.FirstOrDefault(p => p.Code == t.AdvanceBusinessApplyCode);
                t.CompanyName = item?.CompanyName;
                t.IsInvoice = ad == null ? 0 : ad.IsInvoice ? 1 : 0;
            });

            return ret;
        }
        #endregion

        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(RecordBaseQueryDto query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_creditDownLoadDhExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("订货系统应付盘点导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }

        /// <summary>
        /// 确认应收盘点
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> ConfirmCreditRecordItem(string id, string userName)
        {
            Guid.TryParse(id, out var result);
            if (result == Guid.Empty)
            {
                return BaseResponseData<string>.Failed(500, "请选择一行数据");
            }
            var item = await _db.CreditRecordItems.FirstOrDefaultAsync(x => x.Id == Guid.Parse(id));
            if (item == null)
            {
                return BaseResponseData<string>.Failed(500, "盘点不存在或已被删除");
            }
            if (item.IsConfirm == 1)
            {
                return BaseResponseData<string>.Failed(500, "该盘点已确认，无需再次确认");
            }
            item.IsConfirm = 1;
            item.UpdatedBy = userName;
            item.UpdatedTime = DateTime.Now;
            _db.CreditRecordItems.Update(item);
            _db.SaveChanges();
            return BaseResponseData<string>.Success("确认成功");
        }
    }
}
