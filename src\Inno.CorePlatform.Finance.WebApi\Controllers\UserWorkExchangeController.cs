using Dapr;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.UserWorkExchange;
using Inno.CorePlatform.Finance.Application.MgmtServices.UserWorkExchange;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.WebApi.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 用户工作交换控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UserWorkExchangeController : BaseApiController<UserWorkExchangeController>
    {
        private readonly IUserWorkExchangeService _userWorkExchangeService;
        private readonly ILogger<UserWorkExchangeController> _logger;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly DaprClient _daprClient;

        public UserWorkExchangeController(
            IUserWorkExchangeService userWorkExchangeService,
            ILogger<UserWorkExchangeController> logger,
            IEasyCachingProvider easyCaching,
            DaprClient daprClient)
        {
            _userWorkExchangeService = userWorkExchangeService;
            _logger = logger;
            _easyCaching = easyCaching;
            _daprClient = daprClient;
        }

        /// <summary>
        /// 处理用户工作交换事件W
        /// </summary>
        /// <param name="eventDto">用户工作交换事件</param>
        /// <returns></returns>
        [HttpPost("ProcessUserWorkExchange")]
        [Topic("pubsub-default", "pc-backend-userWorkExchange")]
        public async Task<ActionResult> ProcessUserWorkExchange(UserWorkExchangeEventDto eventDto)
        {
            var jsonStr = JsonConvert.SerializeObject(eventDto);
            var cacheKey = $"user-work-exchange_{eventDto.UserId}_{eventDto.TargetUserId}_{DateTime.UtcNow:yyyyMMddHH}";

            try
            {
                _logger.LogInformation("接收到用户工作交换事件: {EventData}", jsonStr);

                // 防重复处理
                var hasOpt = await _easyCaching.GetAsync<string>(cacheKey);
                if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                {
                    _logger.LogInformation("事件已处理过，跳过: {CacheKey}", cacheKey);
                    return Ok();
                }

                await _easyCaching.SetAsync(cacheKey, jsonStr, TimeSpan.FromMinutes(30));

                // 参数验证
                if (string.IsNullOrEmpty(eventDto.UserName) || string.IsNullOrEmpty(eventDto.TargetUserName))
                {
                    _logger.LogWarning("用户工作交换事件参数无效: {EventData}", jsonStr);
                    return BadRequest("用户名不能为空");
                }

                if (eventDto.UserName == eventDto.TargetUserName)
                {
                    _logger.LogWarning("原用户和目标用户不能是同一人: {EventData}", jsonStr);
                    return BadRequest("原用户和目标用户不能是同一人");
                }

                if (eventDto.UserId == Guid.Empty || eventDto.TargetUserId == Guid.Empty)
                {
                    _logger.LogWarning("用户ID不能为空: {EventData}", jsonStr);
                    return BadRequest("用户ID不能为空");
                }

                // 处理工作交换
                var results = await _userWorkExchangeService.ProcessUserWorkExchangeAsync(eventDto);

                // 记录处理结果
                var totalUpdated = results.Sum(r => r.UpdatedCount);
                var failedCount = results.Count(r => !r.Success);
                var totalElapsedMs = results.Sum(r => r.ElapsedMilliseconds);

                _logger.LogInformation("用户工作交换处理完成 - 原用户: {OriginalUser}, 目标用户: {TargetUser}, 总更新: {TotalUpdated}, 失败: {FailedCount}, 总耗时: {TotalElapsedMs}ms", 
                    eventDto.UserName, eventDto.TargetUserName, totalUpdated, failedCount, totalElapsedMs);

                if (failedCount > 0)
                {
                    var failedTypes = string.Join(", ", results.Where(r => !r.Success).Select(r => r.BusinessType));
                    _logger.LogWarning("部分业务单据转移失败: {FailedTypes}", failedTypes);
                }

                return Ok(new { 
                    Success = failedCount == 0, 
                    TotalUpdated = totalUpdated, 
                    FailedCount = failedCount,
                    TotalElapsedMs = totalElapsedMs,
                    Results = results 
                });
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cacheKey);
                _logger.LogError(ex, "用户工作交换事件处理失败: {EventData}", jsonStr);

                // 发送失败消息到重试队列
                await SendEDAFailureMsg(jsonStr, "pc-backend-userWorkExchange", ex, 
                    "/api/UserWorkExchange/ProcessUserWorkExchange");

                return StatusCode(500, "处理失败，已加入重试队列");
            }
        }

        /// <summary>
        /// 手动触发用户工作交换（仅供管理员使用）
        /// </summary>
        /// <param name="request">工作交换请求</param>
        /// <returns></returns>
        [HttpPost("ManualExchange")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> ManualExchange([FromBody] ManualExchangeRequest request)
        {
            try
            {
                var eventDto = new UserWorkExchangeEventDto
                {
                    UserId = request.UserId,
                    UserName = request.UserName,
                    DisplayName = request.DisplayName,
                    StaffId = request.StaffId,
                    TargetUserId = request.TargetUserId,
                    TargetUserName = request.TargetUserName,
                    TargetDisplayName = request.TargetDisplayName,
                    TargetStaffId = request.TargetStaffId
                };

                var results = await _userWorkExchangeService.ProcessUserWorkExchangeAsync(eventDto);
                
                return Ok(new BaseResponseData<List<TransferResultDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = results,
                    Message = "手动工作交换完成"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动用户工作交换失败");
                return StatusCode(500, new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取用户工作交换统计信息
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        [HttpGet("GetUserWorkStatistics")]
        public async Task<ActionResult> GetUserWorkStatistics(string userName)
        {
            try
            {
                if (string.IsNullOrEmpty(userName))
                {
                    return BadRequest("用户名不能为空");
                }

                // 这里可以实现获取用户当前待处理单据的统计信息
                // 暂时返回简单的响应
                return Ok(new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new { Message = "功能开发中" },
                    Message = "获取成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户工作统计信息失败: {UserName}", userName);
                return StatusCode(500, new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                });
            }
        }
    }

    /// <summary>
    /// 手动工作交换请求
    /// </summary>
    public class ManualExchangeRequest
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string DisplayName { get; set; }
        public string StaffId { get; set; }
        public Guid TargetUserId { get; set; }
        public string TargetUserName { get; set; }
        public string TargetDisplayName { get; set; }
        public string TargetStaffId { get; set; }
    }
}
