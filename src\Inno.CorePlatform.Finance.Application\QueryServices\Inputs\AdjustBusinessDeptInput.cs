namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 业务部门调整查询参数类
    /// 用于封装调整业务部门操作所需的查询输入参数
    /// </summary>
    public class AdjustBusinessDeptInput
    {

    }

    /// <summary>
    /// 项目信息类
    /// </summary>
    public class AdjustProjectInfoInput
    {
        /// <summary>
        /// 项目唯一标识
        /// </summary>
        public Guid Id { get; set; } // 项目id

        /// <summary>
        /// 项目编号
        /// </summary>
        public string Number { get; set; } // 项目编号

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; } // 项目名称
    }
}