﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface ICreditAppService : IBaseAppService
    {
        /// <summary>
        /// 确认收入
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(int, string)> ConfirmReceipt(ConfirmReceiptInput query);

        /// <summary>
        /// 反向确认
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(int, string)> ReverseConfirm(ConfirmReceiptInput query);

        /// <summary>
        /// 根据签收的出库单标记确认收入
        /// </summary>
        /// <param name="storeOutCodes"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> ConfirmCreditByStoreOutCodes(List<StoreOutSignDTO> storeOutData);

        /// <summary>
        /// 拆分应付明细
        /// </summary>
        /// <param name="detailId"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SplitDebtDetail(Guid detailId, decimal amount);

        /// <summary>
        ///无需审批
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> NoNeedInvoice(NoNeedInvoiceInput input);

        /// <summary>
        /// 取消应收（仅支持服务费应收）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        Task<BaseResponseData<string>> Cancel(CancelCreditInput input);
        Task<BaseResponseData<List<CreditDto>>> GetInitCredit(GetInitCreditInput input);
        /// <summary>
        /// 分批确认收入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<GetPartialIncomeOutput>>> RevcfmbillBatchisConfirm(List<GetPartialIncomeInput> input);
        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportCreditTask(CreditQueryInput query);
        /// <summary>
        /// 协调服务导出，销项发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
       Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoiceCreditTask(InvoiceCreditQueryInput query);

        /// <summary>
        /// 应收更换核算部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> CreditBusinessDeptChange(BusinessDeptInput input);

        /// <summary>
        /// 导出应收明细任务
        /// </summary>
        /// <param name="query">查询条件输入参数</param>
        /// <returns>返回任务导出结果的响应数据，包含导出任务列表信息</returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportCreditDetailTask(CreditQueryInput query);
    }
}
