using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 调整业务部门相关操作的应用服务接口
    /// </summary>
    public interface IAdjustBusinessDeptAppService
    {
        /// <summary>
        /// 根据项目ID集合获取识别应收在途认款单
        /// </summary>
        /// <param name="projectIds">项目唯一标识集合</param>
        /// <returns>识别应收项数据集合</returns>
        Task<List<InTransitOrderOutput>> GetRecognizeReceiveItemsByProjectIdsAsync(List<Guid> projectIds);
        
        /// <summary>
        /// 根据项目ID集合获取应付批量付款在途项数据
        /// </summary>
        /// <param name="projectIds">项目唯一标识集合</param>
        /// <returns>自动付款项数据集合</returns>
        Task<List<InTransitOrderOutput>> GetPaymentAutoItemsByProjectIdsAsync(List<Guid> projectIds);
    }
}