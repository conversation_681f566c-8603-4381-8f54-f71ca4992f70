﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class RecognizeReceiveItemInput : BaseQuery
    {
        public string? Code { get; set; }
        public string? Receivecode { get; set; }
        public string? Receivedetailcode { get; set; }
        public string? Username { get; set; }
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public List<string>? customers { get; set; }



        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        public Guid? UserId { get; set; }
        public string? department { get; set; }
        public int? Status { get; set; }
        /// <summary>
        /// 明细类型 1=发票，2=订单，3=初始应收
        /// </summary>
        public int? DetailType { get; set; }
        public List<string>? DetailCodes { get; set; } = new List<string>();
        public RecognizeReceiveClassifyEnum? ItemClassify { get; set; }
        public Guid? ServiceId { get; set; }

        public List<Guid?>? ServiceIds { get; set; }
        public int? Source { get; set; }

        /// <summary>
        /// 收款日期 开始
        /// </summary>
        public long? ReceiveDateS { get; set; }
        /// <summary>
        /// 收款日期 开始
        /// </summary>
        public DateTime? ReceiveDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return ReceiveDateS != null ? new DateTime(tricks_1970 + long.Parse(ReceiveDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 收款日期 结束
        /// </summary>
        public long? ReceiveDateE { get; set; }
        /// <summary>
        /// 收款日期 结束
        /// </summary>
        public DateTime? ReceiveDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return ReceiveDateE != null ? new DateTime(tricks_1970 + long.Parse(ReceiveDateE.Value + "0000")) : null;
            }
        }

        public List<string?>? CreatedBy { get; set; }


        /// <summary>
        /// 项目单号 
        /// </summary> 
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 实际客户 
        /// </summary> 
        public string? CustomerName { get; set; }
        /// <summary>
        /// 终端客户 
        /// </summary> 
        public string? HospitalName { get; set; }
        /// <summary>
        /// 收款类型 
        /// </summary> 
        public string? Classify { get; set; }

        /// <summary>
        /// 创建人 
        /// </summary> 
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 结算方式 
        /// </summary> 
        public string? Settletype { get; set; }
        
        /// <summary>
        /// 创建日期 开始
        /// </summary>
        public long? CreateDateS { get; set; }
        /// <summary>
        /// 创建日期 开始
        /// </summary>
        public DateTime? CreateDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return CreateDateS != null ? new DateTime(tricks_1970 + long.Parse(CreateDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 创建日期 结束
        /// </summary>
        public long? CreateDateE { get; set; }
        /// <summary>
        /// 创建日期 结束
        /// </summary>
        public DateTime? CreateDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return CreateDateE != null ? new DateTime(tricks_1970 + long.Parse(CreateDateE.Value + "0000")) : null;
            }
        }

        /// <summary>
        /// 转货款状态 1=已转货款，0=未转货款
        /// </summary>
        public int? TransferStatus { get; set; }

    }
    public class RecognizeItemAttachFileInput
    {
        public Guid RecognizeReceiveItemId { get; set; }

        public string? AttachFileIds { get; set; }
        public string? AttachFileId { get; set; }

    }

    public class RecognizeReceiveDetailsByTypeInput : BaseQuery
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }

        public string? StartDate { get; set; }

        public string? EndDate { get; set; }

        public string? CustomerId { get; set; }

        /// <summary>
        /// 认款类型
        /// </summary>
        public int Type { get; set; }

        public Guid? CompanyId { get; set; }

        public string? Code { get; set; }

        /// <summary>
        /// 项目Id筛选条件
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称筛选条件
        /// </summary>
        public string? ProjectName { get; set; }
    }

    /// <summary>
    /// 组件初始化数据入参
    /// </summary>
    public class RecognizeReceiveInitDataInput
    {
        public List<RecognizeReceiveDetailOutput?>? list { get; set; }
    }

    public class IsExistsInput
    {
        public string? OrderNo { get; set; }
    }

    /// <summary>
    /// 认款应收金额分配辅助校验dto
    /// </summary>
    public class CreditSurplusBoxDto
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 剩余金额
        /// </summary>
        public decimal? CreditSurplusTotalValue { get; set; }
    }

    /// <summary>
    /// 认款应收金额分配辅助校验dto
    /// </summary>
    public class CreditAllocationBoxDto
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 已分配金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 对应单号
        /// </summary>
        public string? Code { get; set; }
    }

    /// <summary>
    /// 批量查询认款单入参根据Code
    /// </summary>
    public class RecognizeReceiveBatchQueryByCodeInput
    {
        /// <summary>
        /// 认款单Code
        /// </summary>
        public List<string> Code { get; set; }
    }
}
