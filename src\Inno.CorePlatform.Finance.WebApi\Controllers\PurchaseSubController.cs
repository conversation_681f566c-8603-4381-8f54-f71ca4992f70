﻿using Dapr;
using Dapr.Actors.Client;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 订阅采购Sub
    /// </summary>
    [ApiController]
    [Route("api/PurchaseSub")]
    public class PurchaseSubController : BaseController
    {
        private ILogger<PurchaseSubController> _logger;
        private IPurchaseAppService _purchaseAppService;
        private IServiceProvider _serviceProvider;
        private DaprClient _daprClient;
        private IEasyCachingProvider _easyCaching;
        private readonly IActorProxyFactory _actorProxyFactory;
        private readonly IGeneratePurchaseCreatedNotifyHandlerFactory _generatePurchaseCreatedNotifyHandlerFactory;
        private readonly ISubLogService _subLogService;
        public override bool EnableParameterLogging { get; set; } = true;
        public PurchaseSubController(
             IServiceProvider serviceProvider,
             DaprClient daprClient,
             IEasyCachingProvider easyCaching,
             ILogger<PurchaseSubController> logger,
             IActorProxyFactory actorProxyFactory, 
             ISubLogService subLogService,
             IGeneratePurchaseCreatedNotifyHandlerFactory generatePurchaseCreatedNotifyHandlerFactory, ISubLogService subLog) : base(subLog)
        {
            this._easyCaching = easyCaching;
            this._serviceProvider = serviceProvider;
            this._logger = logger;
            this._daprClient = daprClient;
            this._subLogService = subLogService;
            _actorProxyFactory = actorProxyFactory;
            _generatePurchaseCreatedNotifyHandlerFactory = generatePurchaseCreatedNotifyHandlerFactory;
        }
        /// <summary>
        /// 采购能力中心创建采购订单通过审批后发布的事件
        /// </summary>
        /// <returns></returns>
        [HttpPost("Created")]
        [Topic("pubsub-default", "purchase-all-created")]
        public async Task<ActionResult> Created(CreatedInput input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "purchase-all-created_" + input.Code;
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            try
            {
                #region 注释原因：Actor方法await不会等待，暂时注释
                //使用Actor处理并发
                //var actorId = new ActorId("PurchasePaymentActorId");
                //var purchasePaymentActor = ActorProxy.Create<IPurchasePaymentActor>(actorId, "PurchasePaymentActor");
                //var result = await purchasePaymentActor.Created(input.Adapt<PurchasePaymentActorCreatedInput>());
                //if (ret.Code != CodeStatusEnum.Success)
                //{
                //    throw new ApplicationException(ret.Message);
                //}
                //return Ok(result);
                #endregion
                #region 注释原因：改为Actor处理并发问题
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    var eventBus = new EventBusDTO
                    {
                        BusinessCode = input.Code,
                        BusinessId = input.PurchaseOrderId,
                        BusinessType = "采购",
                        BusinessSubType = input.PurchaseTypeStr,
                        RelateId = input.RelateId,
                        PaymentCodes = input.PaymentCodes,
                        NegativeDiffAmount = input.NegativeDiffAmount,
                        ReviseRange=input.ReviseRange,
                        PuacOrderCode = input.PuacOrderCode
                    };
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(120));
                    if (input.PurchaseTypeStr.Equals("采购定单"))
                    {
                        //注释原因：更改实现方式
                        //_purchaseAppService = _serviceProvider.GetService<IPurchaseOrderAppService>();
                        ret = BaseResponseData<int>.Success("执行完成");
                        var purchaseCreatedNotifyHandler = new HandPurchaseCreatedNotify(_generatePurchaseCreatedNotifyHandlerFactory.GetInstance(eventBus));
                        await purchaseCreatedNotifyHandler.Hand(eventBus, _serviceProvider);
                    }
                    else if (input.PurchaseTypeStr.Equals("寄售转购货"))
                    {
                        _purchaseAppService = _serviceProvider.GetService<IConsignmentToPurchaseService>();
                    }
                    else if (input.PurchaseTypeStr.Equals("订单修订"))
                    {
                        _purchaseAppService = _serviceProvider.GetService<IPurchaseReviseAppService>();
                    }
                    else if (input.PurchaseTypeStr.Equals("经销购货修订"))
                    {
                        _purchaseAppService = _serviceProvider.GetService<IPurchaseSelfReviseAppService>();
                    }

                    if (_purchaseAppService != null)
                    {
                        //await _purchaseAppService.CreateSubLog(SubLogSourceEnum.Purchase, jsonStr, "admin", "订阅消息");
                        ret = await _purchaseAppService.PullIn(eventBus);
                        if (ret.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(ret.Message);
                        }
                    }
                    _easyCaching.Remove(cachekey);
                }
                return Ok(ret);
                #endregion
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "purchase-all-created",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/PurchaseSub/Created"  //重试的回调方法路由 
                });
                return Ok();
            }
        }
        /// <summary>
        /// 采购订单强制结束后发布的事件
        /// </summary>
        /// <returns></returns>
        [HttpPost("Forceend")]
        [Topic("pubsub-default", "purchase-finance-forceend")]
        public async Task<ActionResult> Forceend(CreatedInput input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var cachekey = "purchase-finance-forceend_" + input.Code;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    var _purchaseAppService = _serviceProvider.GetService<IPurchaseOrderAppService>();
                    if (_purchaseAppService != null)
                    {
                        var eventBus = new EventBusDTO
                        {
                            BusinessCode = input.Code,
                            BusinessId = input.PurchaseOrderId,
                            BusinessType = "采购订单强制结束",
                            BusinessSubType = input.PurchaseTypeStr,
                            RelateId = input.RelateId,
                        };
                        //await _purchaseAppService.CreateSubLog(SubLogSourceEnum.Purchase, jsonStr, "admin", "订阅消息");
                        ret = await _purchaseAppService.PurchaseForceend(eventBus);
                        if (ret.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(ret.Message);
                        }
                    }
                    _easyCaching.Remove(cachekey);
                }

                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logger.LogError(ex, $"Error from DaigId: {daigId}");
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "purchase-finance-forceend",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/PurchaseSub/Forceend"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 入库申请进口业务海关缴费完成事件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GeneralCustomsDebt")]
        [Topic("pubsub-default", "sia-fam-finishPay")]
        public async Task<ActionResult> GeneralCustomsDebt(CustomsEventInput input)
        {
            var jsonStr = JsonConvert.SerializeObject(input);
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var cachekey = "sia-fam-finishPay" + input.CustomsPaymentId;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    var _purchaseAppService = _serviceProvider.GetService<IPurchaseOrderAppService>();
                    if (_purchaseAppService != null)
                    {
                        await _purchaseAppService.CreateSubLog(SubLogSourceEnum.Purchase, jsonStr, "admin", "入库申请进口业务海关缴费完成事件");
                        ret = await _purchaseAppService.GeneralCustomsPayment(Guid.Parse(input.CustomsPaymentId), input.CustomsPayment, input.RequestId);
                        if (ret.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(ret.Message);
                        }
                    }
                    _easyCaching.Remove(cachekey); 
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "sia-fam-finishPay",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/PurchaseSub/GeneralCustomsDebt"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 服务采购订单创建完成事件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("servicepurchasecreated")]
        [Topic("pubsub-default", "purchase-all-servicepurchasecreated")]
        public async Task<ActionResult> ServicePurchaseCreated(ServicePurchaseCreatedInput input)
        {
            var jsonStr = JsonConvert.SerializeObject(input);
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            var cachekey = "purchase-all-servicepurchasecreated" + input.Id;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    var _purchaseAppService = _serviceProvider.GetService<IPurchaseOrderAppService>();
                    if (_purchaseAppService != null)
                    {
                        await _purchaseAppService.CreateSubLog(SubLogSourceEnum.Purchase, jsonStr, "admin", "服务采购订单创建完成事件");
                        ret = await _purchaseAppService.ServicePurchaseCreated(input.Id, input.Code); 
                        if (ret.Code != CodeStatusEnum.Success)
                        {
                            throw new Exception(ret.Message);
                        }
                    }
                    _easyCaching.Remove(cachekey);
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "purchase-all-servicepurchasecreated",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/PurchaseSub/servicepurchasecreated"  //重试的回调方法路由 
                });
                return Ok();
            }
        }
    }
    public class CreatedInput
    {
        /// <summary>
        /// 标识位
        /// </summary>
        public Guid? RelateId { get; set; }
        /// <summary>
        /// 采购订单
        /// </summary>
        public Guid? PurchaseOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 采购类型 1-采购定单，2-寄售转购货，3经销购货修订
        /// </summary>
        public int? PurchaseType { get; set; }
        /// <summary>
        /// 付款单号集合，已逗号隔开
        /// </summary>
        public string? PaymentCodes { get; set; }
        public string PurchaseTypeStr
        {
            get
            {
                var ret = string.Empty;
                switch (PurchaseType)
                {
                    case 1:
                        ret = "采购定单";
                        break;
                    case 2:
                        ret = "寄售转购货";
                        break;
                    case 3:
                        ret = "经销购货修订";
                        break;
                    case 4:
                        ret = "订单修订";
                        break;
                    case 5:
                        ret = "寄售购货修订";
                        break;
                    default:
                        break;
                }
                return ret;
            }
        }

        /// <summary>
        /// 负数金额
        /// </summary>
        public decimal? NegativeDiffAmount { get; set; }
        /// <summary>
        /// 修订范围
        /// </summary>
        public ReviseRangeEnum? ReviseRange { get; set; }

        /// <summary>
        /// 版本修订单号
        /// </summary>
        public string? PuacOrderCode { get; set; }
    }

    public class CustomsEventInput
    {
        public string CustomsPaymentId { get; set; }
        public string CustomsPayment { get; set; }
        /// <summary>
        /// RequestId
        /// </summary>
        public string? RequestId { get; set; }
    }

    public class ServicePurchaseCreatedInput
    {
        /// <summary>
        /// 服务采购单Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }
    }
}

