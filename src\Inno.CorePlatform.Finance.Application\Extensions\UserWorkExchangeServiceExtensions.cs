using Inno.CorePlatform.Finance.Application.MgmtServices.UserWorkExchange;
using Microsoft.Extensions.DependencyInjection;

namespace Inno.CorePlatform.Finance.Application.Extensions
{
    /// <summary>
    /// 用户工作交换服务扩展
    /// </summary>
    public static class UserWorkExchangeServiceExtensions
    {
        /// <summary>
        /// 添加用户工作交换服务
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddUserWorkExchangeServices(this IServiceCollection services)
        {
            services.AddScoped<IUserWorkExchangeService, UserWorkExchangeService>();
            
            return services;
        }
    }
}
