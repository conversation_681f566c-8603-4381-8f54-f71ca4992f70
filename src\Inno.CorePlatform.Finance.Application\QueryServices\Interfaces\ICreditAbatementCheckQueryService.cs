using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 应收冲销状态检查查询服务接口
    /// </summary>
    public interface ICreditAbatementCheckQueryService
    {
        /// <summary>
        /// 查找应收已冲销但收款计划未冲销的订单
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>查询结果列表</returns>
        Task<BaseResponseData<List<CreditAbatementCheckOutput>>> GetInconsistentAbatementOrdersAsync(CreditAbatementCheckInput input);
    }
}
