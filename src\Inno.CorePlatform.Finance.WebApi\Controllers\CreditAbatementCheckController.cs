using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.WebApi.Filter;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 应收冲销状态检查控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CreditAbatementCheckController : ControllerBase
    {
        private readonly ICreditAbatementCheckQueryService _queryService;
        private readonly ILogger<CreditAbatementCheckController> _logger;

        public CreditAbatementCheckController(
            ICreditAbatementCheckQueryService queryService,
            ILogger<CreditAbatementCheckController> logger)
        {
            _queryService = queryService;
            _logger = logger;
        }

        /// <summary>
        /// 查找应收已冲销但收款计划未冲销的订单
        /// </summary>
        /// <param name="input">查询条件：销售订单号数组，应收类型</param>
        /// <returns>返回应收单号，单据日期，冲销状态，应收金额，冲销金额，公司ID，客户ID，销售订单号，应收类型，关联单号</returns>
        [HttpPost("GetInconsistentOrders")]
        public async Task<BaseResponseData<List<CreditAbatementCheckOutput>>> GetInconsistentOrders([FromBody] CreditAbatementCheckInput input)
        {
            try
            {
                _logger.LogInformation("查询应收冲销状态不一致的订单，销售订单号：{@OrderNos}，应收类型：{CreditType}", 
                    input.SaleOrderNos, input.CreditType);
                
                return await _queryService.GetInconsistentAbatementOrdersAsync(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询应收冲销状态不一致的订单时发生错误");
                return BaseResponseData<List<CreditAbatementCheckOutput>>.Failed(500, $"查询失败：{ex.Message}");
            }
        }
    }
}
