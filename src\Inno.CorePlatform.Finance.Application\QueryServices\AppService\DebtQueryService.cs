﻿using Dapr.Client;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Models.File;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Linq;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 应付查询
    /// </summary>
    public class DebtQueryService : QueryAppService, IDebtQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        /// <summary>
        /// 外部能力中心实例
        /// </summary>
        protected readonly IPCApiClient _pcApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IConfiguration _configuration;
        private readonly ICoordinateClient _coordinateclient;
        private readonly IRecognizeReceiveQueryService _recognizeReceiveQueryService;
        private readonly IInputBillQueryService _inputBillQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IDebtRepository _debtRepository;
        private readonly ILogger<DebtQueryService> _logger;
        private readonly IPurchaseExcuteApiClient _purchaseExcuteApiClient;
        private readonly DaprClient _daprClient;

        public DebtQueryService(IAppServiceContextAccessor? contextAccessor,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
            FinanceDbContext db, PortInterfaces.Clients.
            IBDSApiClient iBDSApiClient,
            IPCApiClient pcApiClient,
            IUnitOfWork unitOfWork,
            IWeaverApiClient weaverApiClient,
            ICoordinateClient coordinateclient,
            IFileGatewayClient fileGatewayClient,
            IRecognizeReceiveQueryService recognizeReceiveQueryService,
            IInputBillQueryService inputBillQueryService,
            IConfiguration configuration,
            IKingdeeApiClient kingdeeApiClient,
            IDebtRepository debtRepository,
            ILogger<DebtQueryService> logger,
            IPurchaseExcuteApiClient purchaseExcuteApiClient,
            DaprClient daprClient,
            IProjectMgntApiClient projectMgntApiClient) :
            base(contextAccessor)
        {
            this._db = db;
            this._pcApiClient = pcApiClient;
            this._bDSApiClient = iBDSApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._inventoryQueryService = inventoryQueryService;
            this._unitOfWork = unitOfWork;
            this._weaverApiClient = weaverApiClient;
            this._fileGatewayClient = fileGatewayClient;
            this._configuration = configuration;
            this._recognizeReceiveQueryService = recognizeReceiveQueryService;
            this._coordinateclient = coordinateclient;
            this._inputBillQueryService = inputBillQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._debtRepository = debtRepository;
            this._logger = logger;
            this._daprClient = daprClient;
            _purchaseExcuteApiClient = purchaseExcuteApiClient;
        }


        /// <summary>
        /// 获取应付单列表 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtQueryListOutput>, int)> GetListAsync(DebtQueryInput query)
        {
            Expression<Func<DebtPo, bool>> exp = z => 1 == 1;

            #region 查询条件 
            exp = await InitExp(query, exp);
            #endregion

            IQueryable<DebtPo> baseQuery = _db.Debts.Where(exp).AsNoTracking();
            //总条数
            var count = await baseQuery.CountAsync();
            #region 排序
            baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime).ThenByDescending(z => z.BillCode);
            #endregion

            
           
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<DebtQueryListOutput>()).ToListAsync();
            var names = list.Select(x => x.CreatedBy)?.Distinct().ToList();
            var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
            {
                Names = names,
                Limit = names.Count()
            });

            //获取已冲销金额
            var debtCodes = list.Select(p => p.BillCode);
            var abatments = await _db.Abatements.Where(p => debtCodes.ToHashSet().Contains(p.DebtBillCode) || debtCodes.ToHashSet().Contains(p.CreditBillCode)).Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();

            List<InputBillForDebtOutput> invoices = new List<InputBillForDebtOutput>();
            List<InputBillForDebtOutput> mergeInvoices = new List<InputBillForDebtOutput>();
            if (query.IsExport != true)
            {
                //进项发票
                var relateCodes = list.Select(p => p.RelateCode);
                var storeInItemCodes = new HashSet<string?>(debtCodes.Concat(relateCodes));
                invoices = await GetInvoiceNumbersAsync(storeInItemCodes);

                var ids = list.Select(p => p.Id);
                mergeInvoices = await (from mr in _db.MergeInputBillRelations
                                       join i in _db.InputBills on mr.InputBillId equals i.Id
                                       join mi in _db.MergeInputBills on mr.MergeInputBillId equals mi.Id
                                       join md in _db.MergeInputBillDebts on mi.Id equals md.MergeInputBillId
                                       where mi.Status == 99 && i.Status == 2 && ids.ToHashSet().Contains(md.DebtId)
                                       select new InputBillForDebtOutput()
                                       {
                                           DebtId = md.DebtId,
                                           InvoiceNumber = i.InvoiceNumber
                                       }).ToListAsync();
            }

            foreach (var debt in list)
            {
                debt.AbatmentAmount = abatments.Where(t => t.DebtBillCode == debt.BillCode || t.CreditBillCode == debt.BillCode).Sum(t => t.Value);
                if (userInfos.Data == null || userInfos.Data.List.Count() == 0 || userInfos.Data.List.Where(z => z.Name == debt.CreatedBy).Count() == 0)
                {
                    debt.CreatedByName = debt.CreatedBy;
                }
                else
                {
                    var user = userInfos.Data.List.FirstOrDefault(z => z.Name == debt.CreatedBy);
                    if (user != null)
                    {
                        debt.CreatedByName = user.DisplayName;
                    }
                    else
                    {
                        debt.CreatedByName = debt.CreatedBy;
                    }
                }

                var _mergeCodes = mergeInvoices?.Where(i => i.DebtId == debt.Id)?.Select(i => i.InvoiceNumber).Distinct().ToList();
                if ((debt.DebtType == (int)DebtTypeEnum.selforder || debt.DebtType == (int)DebtTypeEnum.selfreturn) && query.IsExport != true)
                {
                    var _codes = invoices?.Where(p => debt.RelateCode == p.StoreInItemCode)?.Select(o => o.InvoiceNumber).Distinct().ToList();
                    if (_mergeCodes != null && _mergeCodes.Count > 0)
                    {
                        _codes.AddRange(_mergeCodes);
                    }
                    debt.InvoiceCode = string.Join(',', _codes.DefaultIfEmpty(""));
                }
                else if ((debt.DebtType == (int)DebtTypeEnum.order || debt.DebtType == (int)DebtTypeEnum.revise || debt.DebtType == (int)DebtTypeEnum.selfrevise || debt.DebtType == (int)DebtTypeEnum.servicefee) && query.IsExport != true)
                {
                    var _codes = invoices?.Where(p => debt.BillCode == p.StoreInItemCode)?.Select(o => o.InvoiceNumber).Distinct().ToList();
                    if (_mergeCodes != null && _mergeCodes.Count > 0)
                    {
                        _codes.AddRange(_mergeCodes);
                    }
                    debt.InvoiceCode = string.Join(',', _codes.DefaultIfEmpty(""));
                }
            }

            return (list, count);
        }

        /// <summary>
        /// 根据 storeInItemCodes 批量查询发票信息
        /// </summary>
        /// <param name="storeInItemCodes">入库单号集合</param>
        /// <returns>发票信息列表</returns>
        private async Task<List<InputBillForDebtOutput>> GetInvoiceNumbersAsync(IEnumerable<string?> storeInItemCodes)
        {
            var codeList = storeInItemCodes.Where(x => x != null).ToList(); // 去掉 null
            if (!codeList.Any()) return new List<InputBillForDebtOutput>();

            return await (
                from d in _db.InputBillSubmitDetails.AsNoTracking()
                join i in _db.InputBills.AsNoTracking() on d.InputBillId equals i.Id
                where codeList.ToHashSet().Contains(d.StoreInItemCode) && i.Status == 2
                select new InputBillForDebtOutput()
                {
                    StoreInItemCode = d.StoreInItemCode,
                    InvoiceNumber = i.InvoiceNumber
                }).ToListAsync();
        }

        private async Task<Expression<Func<DebtPo, bool>>> InitExp(DebtQueryInput query, Expression<Func<DebtPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            //获取用户数据策略
            var input = new StrategyQueryInput() { userId = query.userId, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }

                Expression<Func<DebtPo, bool>> projectCondition = null;
                Expression<Func<DebtPo, bool>> accountingDeptCondition = null;

                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "company")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                        }
                    }
                    else if (key.ToLower() == "project")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.Value));
                        }
                    }
                    else if (key == "service")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => strategList.Contains(t.ServiceId.Value));
                        }
                    }
                    else if (key == "accountingDept")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToHashSet();
                            exp = exp.And(t => strategList.Contains(t.BusinessDeptId));

                        }
                    }
                    else if (key == "customer")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => !t.CustomerId.HasValue || strategList.Contains(t.CustomerId.Value));
                        }
                    }
                }
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        exp = exp.And(t => serviceIds.Contains(t.ServiceId));
                    }
                }
                //if (query.BillDateStart == null && query.BillDateEnd == null)
                //{
                //    DateTime firstDayOfMonth = DateTime.Now.AddDays(1 - DateTime.Now.Day);
                //    DateTime lastDayOfNow = DateTime.Now.AddDays(1);
                //    exp = exp.And(z => (z.BillDate != null && z.BillDate.Value >= firstDayOfMonth && z.BillDate.Value <= lastDayOfNow));
                //}
                if (!string.IsNullOrEmpty(query.CreatedByName))
                {
                    var userRet = await _bDSApiClient.GetSmallUsersByDisplayNames(new List<string> { query.CreatedByName.Trim() });
                    var userNames = userRet.Select(t => t.Name).ToList();
                    if (userNames != null && userNames.Any())
                    {
                        exp = exp.And(z => userNames.ToHashSet().Contains(z.CreatedBy));
                    }
                    else
                    {
                        exp = exp.And(z => false);
                        return exp;
                    }
                }
            }
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.ServiceName, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.CompanyName, $"%{query.searchKey}%"));
            }
            if (query.BillDateStart != null && query.BillDateEnd != null)
            {
                exp = exp.And(z => (z.BillDate != null && z.BillDate.Value >= query.BillDateStart && z.BillDate.Value <= query.BillDateEnd));
            }
            if (query.ServicesId != null)
            {
                exp = exp.And(z => z.ServiceId == query.ServicesId);
            }
            if (!string.IsNullOrEmpty(query.ServicesName))
            {
                exp = exp.And(z => z.ServiceName != null && z.ServiceName.Contains(query.ServicesName));
            }
            if (query.CompanyId != null)
            {
                exp = exp.And(z => z.CompanyId == query.CompanyId);
            }
            if (query.AgentId != null)
            {
                exp = exp.And(z => z.AgentId == query.AgentId);
            }
            if (query.AbatedStatus != null && (int)query.AbatedStatus > -1)
            {
                if (query.AbatedStatus == AbatedStatusEnum.NonAbate)
                {
                    exp = exp.And(z => z.AbatedStatus == AbatedStatusEnum.NonAbate || !z.AbatedStatus.HasValue);
                }
                else
                {
                    exp = exp.And(z => z.AbatedStatus == AbatedStatusEnum.Abated);
                }
            }
            if (!string.IsNullOrWhiteSpace(query.BillCode))
            {
                if (!string.IsNullOrWhiteSpace(query.billCodeQueryType) && query.billCodeQueryType == "2")
                {
                    exp = exp.And(z => !EF.Functions.Like(z.BillCode ?? "", $"%{query.BillCode}%"));
                }
                else
                {
                    exp = exp.And(z => EF.Functions.Like(z.BillCode ?? "", $"%{query.BillCode}%"));
                }
            }
            if (query.DebtType.HasValue)
            {
                exp = exp.And(z => z.DebtType == query.DebtType);
            }
            if (query.CreatedBy != null && query.CreatedBy.Any())
            {
                exp = exp.And(z => query.CreatedBy.Contains(z.CreatedBy));
            }
            //核算部门
            if (query.department != null && query.department != "")
            {
                exp = exp.And(z => EF.Functions.Like(z.BusinessDeptFullPath, $"%{query.department}%"));
            }
            if (!string.IsNullOrEmpty(query.businessDeptId))
            {
                exp = exp.And(z => z.BusinessDeptId == query.businessDeptId);
            }
            if (!string.IsNullOrWhiteSpace(query.producerOrderNo))
            {
                exp = exp.And(z => z.ProducerOrderNo.Contains(query.producerOrderNo));
            }
            if (!string.IsNullOrWhiteSpace(query.relateCode))
            {
                exp = exp.And(z => z.RelateCode.Contains(query.relateCode));
            }
            if (!string.IsNullOrWhiteSpace(query.purchaseCode))
            {
                exp = exp.And(z => z.PurchaseCode.Contains(query.purchaseCode));
            }
            if (!string.IsNullOrEmpty(query.OrderNo))
            {
                var ids = from dd in _db.DebtDetails where dd.OrderNo == query.OrderNo select dd.DebtId;
                exp = exp.And(z => ids.Contains(z.Id));
            }
            if (!string.IsNullOrEmpty(query.ReceiveCode))
            {
                var ids = from dd in _db.DebtDetails where dd.ReceiveCode == query.ReceiveCode select dd.DebtId;
                exp = exp.And(z => ids.Contains(z.Id));
            }
            if (query.CustomerId.HasValue)
            {
                exp = exp.And(z => z.CustomerId == query.CustomerId);
            }
            if (!string.IsNullOrEmpty(query.ProjectNo))
            {
                exp = exp.And(z => z.ProjectCode == query.ProjectNo);
            }
            if (query.DebtTypes != null && query.DebtTypes.Count > 0)
            {
                exp = exp.And(z => z.DebtType.HasValue && query.DebtTypes.Contains(z.DebtType.Value));
            }
            if (query.IsNegative != null && query.IsNegative.Value)
            {
                exp = exp.And(z => z.RMBAmount < 0 || z.Value < 0);
            }
            if (query.IsNegative != null && !query.IsNegative.Value)
            {
                exp = exp.And(z => z.RMBAmount >= 0 && z.Value >= 0);
            }
            if (!string.IsNullOrEmpty(query.ProjectCode))
            {
                exp = exp.And(z => z.ProjectCode != null && z.ProjectCode.Contains(query.ProjectCode));
            }
            if (!string.IsNullOrEmpty(query.PurchaseContactNo))
            {
                exp = exp.And(z => z.PurchaseContactNo != null && z.PurchaseContactNo.Contains(query.PurchaseContactNo));
            }
            if (query.IsChangeDebt != null && query.IsChangeDebt.Value)
            {
                exp = exp.And(z => z.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE || z.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE);
            }
            if (query.IsChangeDebt != null && !query.IsChangeDebt.Value)
            {
                exp = exp.And(z => string.IsNullOrEmpty(z.AutoType));
            }
            if (query.IsLossAgent != null && query.IsLossAgent.Value)
            {
                exp = exp.And(z => z.LossAgentBearValue > 0);
            }
            if (query.IsLossAgent != null && !query.IsLossAgent.Value)
            {
                exp = exp.And(z => z.LossAgentBearValue == 0 || z.LossAgentBearValue == null);
            }
            if (!string.IsNullOrEmpty(query.InvoiceNumber))
            {
                var _inputBillIds = await (from i in _db.InputBills
                                           where i.Status == 2 && i.InvoiceNumber.Equals(query.InvoiceNumber)
                                           select i.Id).ToListAsync();

                var _codes = await (from d in _db.InputBillSubmitDetails
                                    where _inputBillIds.ToHashSet().Contains(d.InputBillId)
                                    select d.StoreInItemCode).ToListAsync();

                var _ids = await (from md in _db.MergeInputBillDebts
                                  join mi in _db.MergeInputBills
                                  on md.MergeInputBillId equals mi.Id
                                  join mr in _db.MergeInputBillRelations
                                  on mi.Id equals mr.MergeInputBillId
                                  where mi.Status == 99 && _inputBillIds.ToHashSet().Contains(mr.InputBillId)
                                  select md.DebtId).ToListAsync();

                if (_codes.Any())
                {
                    exp = exp.And(z => _codes.Contains(z.BillCode) || _codes.Contains(z.RelateCode));
                }

                if (_ids.Any())
                {
                    exp = exp.And(z => _ids.Contains(z.Id));
                }
            }
            return exp;
        }
        /// <summary>
        /// 应付单列表查询Tab数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount(DebtQueryInput query)
        {
            var ret = BaseResponseData<CreditQueryListTabOutput>.Success("操作成功");

            Expression<Func<DebtPo, bool>> expAll = z => 1 == 1;
            Expression<Func<DebtPo, bool>> expAbated = z => 1 == 1;
            Expression<Func<DebtPo, bool>> expNonAbated = z => 1 == 1;

            var data = new CreditQueryListTabOutput();
            #region 查询条件
            query.AbatedStatus = null;
            expAll = await InitExp(query, expAll);
            data.AllCount = await _db.Debts.Where(expAll).CountAsync();
            query.AbatedStatus = AbatedStatusEnum.Abated;
            expAbated = await InitExp(query, expAbated);

            data.AbatedCount = await _db.Debts.Where(expAbated).CountAsync();
            query.AbatedStatus = AbatedStatusEnum.NonAbate;
            expNonAbated = await InitExp(query, expNonAbated);

            data.NonAbatedCount = await _db.Debts.Where(expNonAbated).CountAsync();
            #endregion


            ret.Data = data;
            return ret;
        }
        /// <summary>
        /// 应付明细(计划)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtDetailQueryListOutput>, int)> GetListDetailAsync(DebtDetailQueryInput query)
        {
            Expression<Func<DebtDetailPo, bool>> exp = z => z.Value != 0;
            if (query.DebtId.HasValue)
            {
                exp = exp.And(z => z.DebtId == query.DebtId);
            }
            else if (query.CreditId.HasValue)
            {
                exp = exp.And(z => z.CreditId == query.CreditId);
            }
            else
            {
                exp = exp.And(z => false);
            }
            var input = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;

                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "customer")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => t.Credit == null || !t.Credit.CustomerId.HasValue || strategList.Contains(t.Credit.CustomerId.Value));
                        }
                    }
                }

            }
            IQueryable<DebtDetailPo> baseQuery = _db.DebtDetails.Include(z => z.Credit).Include(z => z.Debt).Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.DebtId).ThenBy(p => p.AccountPeriodType).ThenByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = baseQuery.Count();

            //分页
            var list = await baseQuery.Select(z => z.Adapt<DebtDetailQueryListOutput>()).ToListAsync();
            var debtPurchaseCodes = list.Where(x => x.AccountPeriodType == AccountPeriodTypeEnum.Repayment).Select(x => x.PurchaseCode).ToList();
            var purchasePayPlans = _db.PurchasePayPlans.Where(x => debtPurchaseCodes.ToHashSet().Contains(x.PurchaseCode) && x.AccountPeriodType == AccountPeriodTypeEnum.Repayment).ToList();
            var debtDetailIds = list.Select(p => p.Id).ToList();
            var paymentAutoDetails = _db.PaymentAutoDetails.Where(p => debtDetailIds.Contains(p.DebtDetilId)).AsNoTracking().ToList();
            var creditIds = list.Select(p => p.CreditId).ToList();


            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            //采购子系统屏蔽销售账期认款信息控制项的名称叫：HiddenSaleAccountInfo，布尔类型，默认true。
            var credits = new List<CreditPo>();
            var partInfo = new List<CreditPartInfo>();
            if (creditIds != null && creditIds.Any())
            {
                credits = await _db.Credits.Include(c => c.CreditDetails).Where(x => creditIds.ToHashSet().Contains(x.Id)).AsNoTracking().ToListAsync();
                if (credits != null && credits.Any())
                {
                    foreach (var c in credits)
                    {
                        if (c.CreditDetails != null && c.CreditDetails.Any())
                        {
                            foreach (var item in c.CreditDetails)
                            {
                                partInfo.Add(new CreditPartInfo
                                {
                                    Id = c.Id,
                                    ServiceId = c.ServiceId.HasValue ? c.ServiceId.Value.ToString() : string.Empty,
                                    CompanyId = c.CompanyId.HasValue ? c.CompanyId.Value.ToString() : string.Empty,
                                    CustomerId = c.CustomerId.HasValue ? c.CustomerId.Value.ToString() : string.Empty,
                                    CreditCode = c.BillCode,
                                    HiddenSaleAccountInfo = false
                                });
                            }
                        }
                    }
                    partInfo = partInfo.Distinct().ToList();
                    //查询是否采购子系统配置
                    foreach (var item in partInfo)
                    {
                        var puaInput = new SubSysRelaQueryInput()
                        {
                            ServiceId = item.ServiceId,
                            CompanyId = item.CompanyId,
                            CustomerId = item.CustomerId,
                            UserId = _appServiceContextAccessor.Get().UserId
                        };
                        var puaRet = await _purchaseExcuteApiClient.GetSubSysRelaConfig(puaInput);
                        if (puaRet.Code == CodeStatusEnum.Success && puaRet.Data != null && puaRet.Data.List != null && puaRet.Data.List.Any())
                        {
                            item.HiddenSaleAccountInfo = puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.HasValue && puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.Value ? true : false;
                        }
                    }
                }
            }
            foreach (var item in list)
            {
                // 回款账期且回款日期不为空
                if (item.AccountPeriodType == AccountPeriodTypeEnum.Repayment && item.BackPayTime.HasValue)
                {
                    int accountPeriodDays = 0;
                    if (item.AccountPeriodDays.HasValue)
                    {
                        accountPeriodDays = (int)item.AccountPeriodDays.Value;
                    }
                    else
                    {
                        var purchasePayPlan = purchasePayPlans.FirstOrDefault(x => x.PurchaseCode == item.PurchaseCode);
                        accountPeriodDays = purchasePayPlan != null && purchasePayPlan.AccountPeriodDays.HasValue ? purchasePayPlan.AccountPeriodDays.Value : 0;
                    }

                    var backPayTime = item.BackPayTime.Value.Date;
                    var currentBackPayTime = backPayTime.AddDays(-accountPeriodDays);
                    if (currentBackPayTime > DateTime.Now)
                    {
                        //未超过则不显示
                        item.ProbablyPayTime = null;
                        item.ReceiveCode = string.Empty;
                    }
                    else
                    {
                        //超过当前日期则显示回款日期
                        item.ProbablyPayTime = backPayTime;
                    }
                }
                if (paymentAutoDetails.Where(p => p.DebtDetilId == item.Id).Count() > 0) //关联到批量付款明细
                {
                    item.IsRelateBatchPayment = true;
                }
                else
                {
                    item.IsRelateBatchPayment = false;
                }
                var currentCredit = credits.FirstOrDefault(x => x.Id == item.CreditId);
                if (currentCredit != null)
                {
                    item.HospitalName = currentCredit.HospitalName;
                }
                var config = partInfo.FirstOrDefault(x => x.Id == item.CreditId);
                //业务单元客户端，在应收清单-付款计划中，不显示销售账期的应付的收款单号
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {

                }
                else if (item.AccountPeriodType == AccountPeriodTypeEnum.Sale && config != null && config.HiddenSaleAccountInfo && user.Data.List.First().InstitutionType == 4)
                {
                    item.ReceiveCode = string.Empty;
                }

            }
            return (list, count);
        }
        /// <summary>
        /// 应付执行明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtDetailExcuteQueryListOutput>, int)> GetListDetailExcuteAsync(DebtDetailExcuteQueryInput query)
        {
            Expression<Func<DebtDetailExcutePo, bool>> exp = z => 1 == 1;
            if (query.DebtDetailIds != null && query.DebtDetailIds.Any())
            {
                exp = exp.And(z => query.DebtDetailIds.Contains(z.DebtDetailId));
            }
            else if (query.DebtId != null)
            {
                query.DebtDetailIds = _db.DebtDetails.Where(z => z.DebtId == query.DebtId).AsNoTracking().Select(z => z.Id).ToList();
                exp = exp.And(z => query.DebtDetailIds.Contains(z.DebtDetailId));
            }
            else { exp = exp.And(z => 1 != 1); }

            IQueryable<DebtDetailExcutePo> baseQuery = _db.DebtDetailExcutes.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = baseQuery.Count();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<DebtDetailExcuteQueryListOutput>()).ToListAsync();

            //获取计划单号和账期
            IQueryable<DebtDetailPo> baseQuery_debtDetail = _db.DebtDetails.Where(d => query.DebtDetailIds.Contains(d.Id)).AsNoTracking();
            foreach (var l in list)
            {
                l.Code = baseQuery_debtDetail.Where(d => d.Id == l.DebtDetailId).FirstOrDefault().Code;
                l.AccountPeriodType = baseQuery_debtDetail.Where(d => d.Id == l.DebtDetailId).FirstOrDefault()?.AccountPeriodType;
            }

            return (list, count);
        }

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="relateCode">关联单号</param>
        /// <returns></returns>
        public async Task<List<DebtQueryOutput>> GetDebtInfoListAsync(string relateCode)
        {
            List<DebtQueryOutput> debtQueryOutputList = new List<DebtQueryOutput>();

            var debtList = await _db.Debts.Where(c => c.RelateCode == relateCode).AsNoTracking().ToListAsync();
            if (!debtList.Any()) return debtQueryOutputList;
            await GetAndFillDebtInfoList(debtQueryOutputList, debtList);

            return debtQueryOutputList;
        }

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="purchaseCode">采购单号</param>
        /// <returns></returns>
        public async Task<List<DebtQueryOutput>> GetDebtInfoListByPurchaseCodeAsync(string purchaseCode)
        {
            List<DebtQueryOutput> debtQueryOutputList = new List<DebtQueryOutput>();

            var debtDetails = await _db.DebtDetails.Where(c => c.PurchaseCode == purchaseCode).AsNoTracking().ToListAsync();
            var deptIdList = debtDetails.Select(c => c.DebtId).ToList();
            var debtList = await _db.Debts.Where(c => deptIdList.Contains(c.Id)).AsNoTracking().ToListAsync();
            if (!debtList.Any()) return debtQueryOutputList;

            await GetAndFillDebtInfoList(debtQueryOutputList, debtList);

            return debtQueryOutputList;
        }

        /// <summary>
        /// 获取并组装应付信息
        /// </summary>
        /// <param name="debtQueryOutputList"></param>
        /// <param name="debtList"></param>
        /// <returns></returns>
        private async Task GetAndFillDebtInfoList(List<DebtQueryOutput> debtQueryOutputList, List<DebtPo> debtList)
        {
            var deptIdList = debtList.Select(c => c.Id).ToList();
            var debtDetailList = await _db.DebtDetails.Where(c => c.DebtId.HasValue && deptIdList.Contains(c.DebtId.Value)).AsNoTracking().ToListAsync();
            var debtDetailIdList = debtDetailList.Select(c => c.Id).ToList();
            var debtDetailExecuteList = await _db.DebtDetailExcutes.Where(c => debtDetailIdList.Contains(c.DebtDetailId)).AsNoTracking().ToListAsync();

            foreach (var debt in debtList)
            {
                var debtQueryOutput = debt.Adapt<DebtQueryOutput>();
                debtQueryOutputList.Add(debtQueryOutput);
                var curDebtDetailList = debtDetailList.FindAll(c => c.DebtId == debt.Id);
                if (!curDebtDetailList.Any()) continue;

                foreach (var debtDetail in curDebtDetailList)
                {
                    var debtDetailQueryOutput = debtDetail.Adapt<DebtDetailQueryOutput>();
                    debtQueryOutput.DebtDetailList.Add(debtDetailQueryOutput);
                    var curDebtDetailExecuteList = debtDetailExecuteList.FindAll(c => c.DebtDetailId == debtDetail.Id);
                    if (!curDebtDetailExecuteList.Any()) continue;
                    debtDetailQueryOutput.DebtDetailExecuteList = curDebtDetailExecuteList.Adapt<List<DebtDetailExecuteQueryOutput>>();
                }
            }
        }

        /// <summary>
        /// 获取某个应付的可冲销应付
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="billCode"></param>
        /// <param name="classify"></param>
        /// <param name="userId"></param>
        /// <param name="customer"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<AvailableAbatmentsOutput>>> GetAvailableAbatmentsAsync(Guid debtId, string billCode, Guid userId, string classify = "debt", string? customer = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var debt = await _db.Debts.Include(i => i.DebtDetails).Where(p => p.Id == debtId).FirstOrDefaultAsync();
            if (debt == null || debt.AbatedStatus == Domain.AbatedStatusEnum.Abated)
            {
                return new BaseResponseData<List<AvailableAbatmentsOutput>>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "该应付单不存在或已冲销完成"
                };
            }
            var sysMonth = await _bDSApiClient.GetSystemMonth(debt.CompanyId.Value.ToString());
            await CheckSysMonth(debt.CompanyId.Value, sysMonth);
            var abatmentAmount = await _db.Abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).SumAsync(p => p.Value);
            var thisDebtAbateValue = Math.Abs(debt.Value) - abatmentAmount;
            if (thisDebtAbateValue <= 0)
            {
                return new BaseResponseData<List<AvailableAbatmentsOutput>>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "该应付单冲销余额为0，无法冲销",
                };
            }
            var availableDebts = new List<AvailableAbatmentsOutput>();
            var originalDebtIds = new List<Guid>();
            if (classify == "debt")
            {
                var availableDebtsQuery = _db.Debts.Where(p =>
                p.AbatedStatus != Domain.AbatedStatusEnum.Abated
                && p.CompanyId == debt.CompanyId
                && p.AgentId == debt.AgentId
                && p.ProjectId== debt.ProjectId
                && p.BusinessDeptId == debt.BusinessDeptId
                && p.CoinCode == debt.CoinCode
                && p.ServiceId == debt.ServiceId).AsNoTracking();
                if (!string.IsNullOrEmpty(billCode))
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => EF.Functions.Like(p.BillCode, $"%{billCode}%"));
                }
                if (!string.IsNullOrEmpty(customer))
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.CustomerName != null && p.CustomerName.Contains(customer));
                }
                if (startDate.HasValue && endDate.HasValue)
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.BillDate >= startDate.Value.AddHours(8) && p.BillDate <= endDate.Value.AddHours(8));
                }
                if (debt.Value > 0)
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.Value < 0);
                    if (debt.DebtType == DebtTypeEnum.order && debt.DebtDetails?.Any(p => p.CreditId.HasValue) == true)
                    {
                        var creditIds = debt.DebtDetails.Where(p => p.CreditId != null).Select(p => p.CreditId.Value).ToHashSet();
                        var orderNos = (await _db.Credits.Where(p => creditIds.Contains(p.Id) && !string.IsNullOrEmpty(p.OrderNo)).Select(o => o.OrderNo!).ToListAsync());
                        if (orderNos != null && orderNos.Count > 0)
                        {
                            foreach (var no in orderNos)
                            {
                                if (no.Contains(","))
                                {
                                    orderNos.Remove(no);
                                    orderNos.AddRange(no.Split(",", StringSplitOptions.RemoveEmptyEntries));
                                }
                            }
                            originalDebtIds = await _db.Debts
                                                     .Where(p => orderNos.Any(orderNo => EF.Functions.Like(p.OrderNo, "%" + orderNo + "%")) && p.Value < 0 && p.DebtType == DebtTypeEnum.selfreturn)
                                                     .Select(p => p.Id)
                                                     .ToListAsync();
                        }
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(debt.OrderNo) && debt.DebtType == DebtTypeEnum.selfreturn)
                    {
                        List<string> orderNos = new List<string>();
                        if (debt.OrderNo.Contains(","))
                        {
                            orderNos.AddRange(debt.OrderNo.Split(",", StringSplitOptions.RemoveEmptyEntries));
                        }
                        else
                        {
                            orderNos.Add(debt.OrderNo);
                        }
                        originalDebtIds = await (
                                             from dd in _db.DebtDetails
                                             join c in _db.Credits on dd.CreditId equals c.Id
                                             where orderNos.Any(orderNo => c.OrderNo.Contains(orderNo)) && dd.Value > 0 && dd.DebtId.HasValue
                                             select dd.DebtId.Value
                                         ).Distinct().ToListAsync();
                    }
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.Value > 0);
                }

                if (debt.DebtType == DebtTypeEnum.servicefee)
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.DebtType == DebtTypeEnum.servicefee);
                }
                else
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.DebtType != DebtTypeEnum.servicefee);
                }

                availableDebts = await availableDebtsQuery.Select(p => new AvailableAbatmentsOutput
                {
                    BillCode = p.BillCode,
                    AgentName = p.AgentName,
                    BillDate = p.BillDate,
                    CompanyName = p.CompanyName,
                    DebtType = p.DebtType,
                    Value = p.Value,
                    PurchaseCode = p.PurchaseCode,
                    CustomerName = p.CustomerName,
                    IsOriginal = originalDebtIds.Any(c => c == p.Id) && (p.DebtType == DebtTypeEnum.selfreturn || p.DebtType == DebtTypeEnum.order)
                }).OrderByDescending(o => o.IsOriginal).ToListAsync();

                var debtCodes = availableDebts.Select(p => p.BillCode).ToHashSet();

                var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode))
                                         .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();

                foreach (var p in availableDebts)
                {
                    var thisHasAbatmentAmount = abatments.Where(t => t.DebtBillCode == p.BillCode || t.CreditBillCode == p.BillCode).Sum(t => t.Value);
                    var thisAbatmentAmount = Math.Abs(p.Value) - thisHasAbatmentAmount;
                    p.ThisAbatmentAmount = thisAbatmentAmount > thisDebtAbateValue ? thisDebtAbateValue : thisAbatmentAmount;
                    p.AbatmentAmount = thisHasAbatmentAmount;
                    if (debt.Value < 0 && debt.DebtType == DebtTypeEnum.selfrevise)
                    {
                        //负数修订应付查找默认对应单号
                        if (debt.PurchaseCode == p.PurchaseCode && debt.DebtType != p.DebtType)
                        {
                            p.DefaultCheck = true;
                        }
                    }

                }
            }
            else if (classify == "credit")
            {
                var agent = await _bDSApiClient.GetAgentInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { id = debt.AgentId.ToString() });

                var availableCreditsQuery = _db.Credits.Where(p => p.AbatedStatus != Domain.AbatedStatusEnum.Abated
                                          && p.CompanyId == debt.CompanyId 
                                          && p.ProjectId == debt.ProjectId
                                          && p.CreditType == CreditTypeEnum.servicefee
                                          && p.InvoiceStatus == InvoiceStatusEnum.invoiced
                                          && p.LatestUniCode == agent[0].socialCode).AsNoTracking();

                if (!string.IsNullOrEmpty(billCode))
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => EF.Functions.Like(p.BillCode, $"%{billCode}%"));
                }
                if (!string.IsNullOrEmpty(customer))
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => p.CustomerName != null && p.CustomerName.Contains(customer));
                }
                if (startDate.HasValue && endDate.HasValue)
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => p.BillDate >= startDate.Value.AddHours(8) && p.BillDate <= endDate.Value.AddHours(8));
                }
                if (debt.Value > 0)
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => p.Value > 0);
                }
                else
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => p.Value < 0);
                }
                if (debt.CoinCode != "CNY")
                {
                    availableCreditsQuery = availableCreditsQuery.Where(p => 1 != 1);
                }
                availableDebts = await availableCreditsQuery.Select(p => new AvailableAbatmentsOutput
                {
                    BillCode = p.BillCode,
                    AgentName = p.CustomerName,
                    BillDate = p.BillDate,
                    CompanyName = p.CompanyName,
                    CreditType = p.CreditType,
                    Value = p.Value,
                }).ToListAsync();

                var creditCodes = availableCreditsQuery.Select(p => p.BillCode);

                var debtPaymentUses = await _db.DebtPaymentUseDetails.Where(p => creditCodes.Contains(p.DebtCode)).AsNoTracking().ToListAsync();
                availableDebts.ForEach(p =>
                {
                    p.UseAmount = debtPaymentUses.Where(t => t.DebtCode == p.BillCode).Sum(t => t.UseAmount);
                });

                var abatments = await _db.Abatements.Where(p => creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode))
                                         .Select(p => p.Adapt<AbatmentDTO>()).AsNoTracking().ToListAsync();

                availableDebts.ForEach(p =>
                {
                    var thisHasAbatmentAmount = abatments.Where(t => t.DebtBillCode == p.BillCode || t.CreditBillCode == p.BillCode).Sum(t => t.Value);
                    var thisAbatmentAmount = Math.Abs(p.Value) - thisHasAbatmentAmount - (p.UseAmount ?? 0);
                    p.ThisAbatmentAmount = thisAbatmentAmount > thisDebtAbateValue ? thisDebtAbateValue : thisAbatmentAmount;
                    p.AbatmentAmount = thisHasAbatmentAmount;
                });


            }
            else if (classify == "receive")
            {
                var input = new Inno.CorePlatform.Finance.Application.DTOs.RecognizeReceiveInput();
                var strategryInput = new StrategyQueryInput() { userId = userId, functionUri = "metadata://fam" };
                var strategys = await _pcApiClient.GetStrategyAsync(strategryInput);

                var companyNameCodes = new List<string>();
                if (strategys != null)
                {
                    var rowStrategies = strategys.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        return new BaseResponseData<List<AvailableAbatmentsOutput>>()
                        {
                            Code = CodeStatusEnum.Success,
                            Data = availableDebts,
                            Message = "查询成功"
                        };
                    }
                    foreach (var key in strategys.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategys.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var companyIds = strategys.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                                var daprCompanies = await _bDSApiClient.GetCompanyInfoAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                                {
                                    ids = companyIds
                                });
                                companyNameCodes = daprCompanies.Select(p => p.nameCode).ToList();
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategys.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategys.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                                if (strategList.Count() > 0 && strategList.Any())
                                {
                                    foreach (var customerId in strategList)
                                    {
                                        input.casRecbillCustomer.Add(new Inno.CorePlatform.Finance.Application.DTOs.QueryCasRecbillCustomerModel
                                        {
                                            customer = customerId
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
                if (input.company == null || !input.company.Any())
                {
                    input.company = companyNameCodes;
                }
                input.payerType = "bd_supplier";//客商名称(客户:bd_customer,供应商:bd_supplier)
                input.type = new List<string>(); //退采购付款
                input.type.Add("102");
                var result = await _recognizeReceiveQueryService.GetKdReceiveBills(input);
                if (!string.IsNullOrEmpty(billCode))
                {
                    result = result.Where(p => p.billno.Contains(billCode)).ToList();
                }
                if (!string.IsNullOrEmpty(customer))
                {
                    result = result.Where(p => p.payerName.Contains(customer)).ToList();
                }
                if (debt.CompanyId.HasValue)
                {
                    result = result.Where(p => p.orgName.Contains(debt.CompanyName)).ToList();
                }
                if (!string.IsNullOrEmpty(debt.AgentName))
                {
                    result = result.Where(p => p.payerName.Contains(debt.AgentName)).ToList();
                }
                if (startDate.HasValue && endDate.HasValue)
                {
                    result = result.Where(p => !string.IsNullOrEmpty(p.payeedate) && Convert.ToDateTime(p.payeedate) >= startDate.Value.AddHours(8) && Convert.ToDateTime(p.payeedate) <= endDate.Value.AddHours(8)).ToList();
                }
                var resultPage = result.OrderByDescending(p => p.payeedate).ToList();
                var hasAbatedAmount = _db.Abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).Sum(p => p.Value);
                foreach (var item in resultPage)
                {
                    availableDebts.Add(new AvailableAbatmentsOutput()
                    {
                        AgentName = item.payerName,
                        BillCode = item.billno,
                        BillDate = Convert.ToDateTime(item.payeedate),
                        CompanyName = item.orgName,
                        Value = item.namountclaimed,
                        ThisAbatmentAmount = (Math.Abs(debt.Value) - hasAbatedAmount) > item.namountclaimed ? item.namountclaimed : (Math.Abs(debt.Value) - hasAbatedAmount)
                    });
                }
            }
            else if (classify == "payment")
            {
                var availableDebtsQuery = _db.Payments.Where(p =>
                p.AbatedStatus != Domain.AbatedStatusEnum.Abated
                && p.CompanyId == debt.CompanyId
                && p.AgentId == debt.AgentId
                && p.ProjectId == debt.ProjectId
                && p.CoinCode == debt.CoinCode
                && p.Type == PaymentTypeEnum.Prepay
                && string.IsNullOrEmpty(p.PurchaseCode)).AsNoTracking();
                if (!string.IsNullOrEmpty(billCode))
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => EF.Functions.Like(p.Code, $"%{billCode}%"));
                }
                if (!string.IsNullOrEmpty(customer))
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.AgentName != null && p.AgentName.Contains(customer));
                }
                if (startDate.HasValue && endDate.HasValue)
                {
                    availableDebtsQuery = availableDebtsQuery.Where(p => p.BillDate >= startDate.Value.AddHours(8) && p.BillDate <= endDate.Value.AddHours(8));
                }
                availableDebts = await availableDebtsQuery.Select(p => new AvailableAbatmentsOutput
                {
                    BillCode = p.Code,
                    AgentName = p.AgentName,
                    BillDate = p.BillDate,
                    CompanyName = p.CompanyName,
                    DebtType = null,
                    Value = p.Value,
                    PurchaseCode = p.PurchaseCode
                }).ToListAsync();

                var refundDetails = await _db.RefundDetails.Where(p => availableDebts.Select(p => p.BillCode).Contains(p.PaymentCode)).ToListAsync();
                var refundItems = new List<RefundItemPo>();
                if (refundDetails != null && refundDetails.Count > 0)
                {
                    var ids = refundDetails.Select(p => p.RefundItemId).ToList();
                    refundItems = await _db.RefundItem.Where(p => ids.Contains(p.Id)).ToListAsync();
                }
                for (int i = 0; i < availableDebts.Count; i++)
                {
                    var refundDetail = refundDetails.Where(p => p.PaymentCode == availableDebts[i].BillCode).FirstOrDefault();
                    if (refundDetail != null)
                    {
                        if (refundItems.Where(p => p.Id == refundDetail.RefundItemId).FirstOrDefault().Status == RefundStatusEnum.waitSubmit)
                        {
                            availableDebts.Remove(availableDebts[i]);
                            i--;
                        }
                    }
                }

                var debtCodes = availableDebtsQuery.Select(p => p.Code);

                var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode))
                                         .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();

                foreach (var p in availableDebts)
                {
                    var thisHasAbatmentAmount = abatments.Where(t => t.DebtBillCode == p.BillCode || t.CreditBillCode == p.BillCode).Sum(t => t.Value);
                    var thisAbatmentAmount = Math.Abs(p.Value) - thisHasAbatmentAmount;
                    p.ThisAbatmentAmount = thisAbatmentAmount > thisDebtAbateValue ? thisDebtAbateValue : thisAbatmentAmount;
                    p.AbatmentAmount = thisHasAbatmentAmount;
                }

                availableDebts = availableDebts.Where(p => Math.Abs(p.Value) - p.AbatmentAmount > 0).ToList();
            }
            return new BaseResponseData<List<AvailableAbatmentsOutput>>()
            {
                Code = CodeStatusEnum.Success,
                Data = availableDebts,
                Message = "查询成功"
            };
        }
        private async Task<string> CheckSysMonth(Guid companyId, string sysMonth)
        {
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
            {
                var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
                if (inventory != null)
                {
                    if (inventory.Status == 2)
                    {
                        throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                    }
                    if (inventory.Status == 99)
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }
                }
            }
            else
            {
                DateTime.TryParse(sysMonth, out DateTime billDate);
                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                {
                    throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                }
            }
            return sysMonth;
        }

        /// <summary>
        /// 按公司获取应付单计划明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtDetailBulkOutput>, int)> GetDetailsByCompanies(DebtDetailBulkQuery query)
        {
            var existId = new List<Guid?>();
            if (!query.CompanyId.HasValue)
            {
                return (new List<DebtDetailBulkOutput>(), 0);
            }
            if (query.CompanyId.HasValue)
            {
                var companyExistId = _db.PaymentAutoItems.Include(c => c.PaymentAutoDetails).Where(t => t.CompanyId == query.CompanyId && t.Status != PaymentAutoItemStatusEnum.Completed).SelectMany(t => t.PaymentAutoDetails).Select(t => (Guid?)t.DebtDetilId);
                existId.AddRange(companyExistId);
                if (query.PaymentAutoItemId.HasValue && query.CreatedType == 1)
                {
                    var removeIds = _db.PaymentAutoDetails.Where(t => t.PaymentAutoItemId == query.PaymentAutoItemId).Select(t => (Guid?)t.DebtDetilId).ToList();
                    existId.RemoveAll(t => removeIds.Contains(t));
                }
            }

            var receiveCodes = new List<string>();
            if (query.ReceiveDateStart.HasValue && query.ReceiveDateEnd.HasValue)
            {
                receiveCodes = await _db.RecognizeReceiveItems.AsNoTracking().Where(x => x.ReceiveDate >= query.ReceiveDateStart && x.ReceiveDate <= query.ReceiveDateEnd).Select(x => x.ReceiveCode).Distinct().ToListAsync();
            }

            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(input);
            int accountPeriodType = (int)query.AccountPeriodType;
            IQueryable<DebtDetailBulkOutput> baseQuery = (from d in _db.DebtDetails
                                                          join c in _db.Credits on d.CreditId equals c.Id into temp
                                                          from t in temp.DefaultIfEmpty()
                                                          join i in _db.Debts on d.DebtId equals i.Id
                                                          where d.Status == DebtDetailStatusEnum.WaitExecute && i.BusinessDeptId == query.DepartId
                                                          && i.AbatedStatus != AbatedStatusEnum.Abated
                                                          && (string.IsNullOrEmpty(query.PurchaseCode) ? true : i.PurchaseCode == query.PurchaseCode)
                                                          && (string.IsNullOrEmpty(query.BusinessUnitName) ? true : i.ServiceName == query.BusinessUnitName)
                                                          && (!query.customerId.HasValue ? true : t.CustomerId == query.customerId)
                                                          && (string.IsNullOrEmpty(query.StorIncode) ? true : EF.Functions.Like(i.RelateCode, $"%{query.StorIncode}%"))
                                                          && (string.IsNullOrEmpty(query.BillCode) ? true : EF.Functions.Like(i.BillCode, $"%{query.BillCode}%"))
                                                          && (string.IsNullOrEmpty(query.AgentName) ? true : EF.Functions.Like(i.AgentName, $"%{query.AgentName}%"))
                                                          && (query.AgentIds != null && query.AgentIds.Any() ? query.AgentIds.Contains(i.AgentId) : true)
                                                          && (string.IsNullOrEmpty(query.ProjectName) ? true : EF.Functions.Like(i.ProjectName, $"%{query.ProjectName}%"))
                                                          && i.Value != 0 && d.Value > 0
                                                          && (string.IsNullOrEmpty(query.InvoiceReceiptStatus) ? true : (query.InvoiceReceiptStatus == "已入账" ? d.IsInvoiceReceipt == true : d.IsInvoiceReceipt == null || d.IsInvoiceReceipt == false))
                                                          && (query.ProbablyPayTimeStart.HasValue ? d.ProbablyPayTime >= query.ProbablyPayTimeStart!.Value : true)
                                                          && (query.ProbablyPayTimeEnd.HasValue ? d.ProbablyPayTime < query.ProbablyPayTimeEnd!.Value.AddDays(1) : true)
                                                          && (query.HospitalId != null && query.HospitalId.Count > 0 ? query.HospitalId.Contains(t.HospitalId) : true)
                                                          && d.AccountPeriodType == accountPeriodType
                                                          && d.ProbablyPayTime.HasValue
                                                          && (string.IsNullOrEmpty(query.ReceiveCode) ? true : EF.Functions.Like(d.ReceiveCode, $"%{query.ReceiveCode}%"))
                                                          && (query.IsInnerAgent.HasValue && query.IsInnerAgent == 1 ? (i.IsInnerAgent == 2 || i.IsInnerAgent == 11 || i.IsInnerAgent == 12) : true)
                                                          && (query.IsInnerAgent.HasValue && query.IsInnerAgent == 0 ? i.IsInnerAgent == 0 || i.IsInnerAgent == null : true)
                                                          && i.CompanyId == query.CompanyId
                                                           && (receiveCodes.Count > 0 ? d.ReceiveCode != null && receiveCodes.ToHashSet().Contains(d.ReceiveCode) : true)
                                                          select new DebtDetailBulkOutput
                                                          {
                                                              DebtDetilId = d.Id,
                                                              AccountPeriodType = (AccountPeriodTypeEnum)d.AccountPeriodType,
                                                              DebtId = d.DebtId,
                                                              Code = d.Code,
                                                              DebtCode = i.BillCode,
                                                              RelateCode = i.RelateCode,
                                                              BillDate = i.BillDate,
                                                              AgentName = i.AgentName,
                                                              CompanyName = i.CompanyName,
                                                              CoinName = i.CoinName,
                                                              CompanyId = i.CompanyId,
                                                              ServiceName = i.ServiceName,
                                                              Value = d.Value,
                                                              CustomerName = t.CustomerName,
                                                              ProbablyPayTime = d.ProbablyPayTime,
                                                              BusinessDeptId = i.BusinessDeptId,
                                                              BusinessDeptFullPath = i.BusinessDeptFullPath,
                                                              BusinessDeptFullName = i.BusinessDeptFullName,
                                                              PurchaseContactNo = i.PurchaseContactNo,
                                                              ProjectCode = i.ProjectCode,
                                                              ProjectId = i.ProjectId,
                                                              ProjectName = i.ProjectName,
                                                              CreditCode = t.BillCode,
                                                              OrderNo = t.OrderNo,
                                                              ReceiveCode = d.ReceiveCode,
                                                              BackPayTime = d.BackPayTime,
                                                              InvoiceReceiptStatus = d.IsInvoiceReceipt.HasValue && d.IsInvoiceReceipt.Value ? 99 : 0,
                                                              Settletype = d.Settletype,
                                                              DraftBillExpireDate = d.DraftBillExpireDate,
                                                              HospitalId = t.HospitalId,
                                                              HospitalName = t.HospitalName,
                                                          }
                                                         ).AsNoTracking();
            Expression<Func<DebtDetailBulkOutput, bool>> exp = z => 1 == 1;
            if (strategry != null && strategry.RowStrategies.Any())
            {
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key == "project")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.Value));
                        }
                        break;
                    }
                }
            }
            baseQuery = baseQuery.Where(exp);
            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                for (int i = query.sort.Count - 1; i >= 0; i--)
                {
                    var ss = query.sort[i].Split(',');
                    if (ss.Length > 1 && ss[0] == "debtCode")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.DebtCode);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.DebtCode); }
                    }
                    if (ss.Length > 1 && ss[0] == "probablyPayTime")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.ProbablyPayTime);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.ProbablyPayTime); }
                    }
                }
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.ProbablyPayTime);
            }
            #endregion

            if (query.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment) //回款
            {
                baseQuery = baseQuery.Where(t => ((t.Settletype == "商业承兑汇票" || t.Settletype == "银行承兑汇票") && t.DraftBillExpireDate != null && t.DraftBillExpireDate <= DateTime.Now) || (t.Settletype != "商业承兑汇票" && t.Settletype != "银行承兑汇票")); //到期了就显示出来
            }

            var alls = await baseQuery.ToListAsync();
            if (existId.Count > 0)
            {
                alls = alls.Where(p => !existId.ToHashSet().Contains(p.DebtDetilId)).ToList();
            }
            //不能存在损失确认里面
            var lossRecognitions = await _db.LossRecognitionItem.Where(p => p.Status != StatusEnum.Complate && p.Status != StatusEnum.Refuse).AsNoTracking().Select(p => p.Id).ToListAsync();
            var lossRecognitionDebtCodes = await _db.LossRecognitionDetails.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Debt && lossRecognitions.ToHashSet().Contains(p.LossRecognitionItemId)).Select(p => p.BillCode).AsNoTracking().ToListAsync();
            if (lossRecognitionDebtCodes.Count > 0)
            {
                alls = alls.Where(p => !lossRecognitionDebtCodes.Contains(p.DebtCode)).ToList();
            }

            if (query.InvoiceStatus.HasValue && alls.Count > 0)
            {
                var _debtCodes = alls.Select(o => o.DebtCode).Distinct().ToHashSet();
                var _relateCodes = alls.Select(o => o.RelateCode).Distinct().ToHashSet();

                var _inputBillDebtCodes = await (_db.InputBills.Where(ib => ib.Status == 2)
                    .Join(_db.InputBillSubmitDetails.Where(ibs => _debtCodes.Contains(ibs.StoreInItemCode)),
                        ib => ib.Id,
                        ibsd => ibsd.InputBillId,
                        (ib, ibsd) => new { ib, ibsd })
                    .Select(x =>
                        x.ibsd.StoreInItemCode
                        )
                    .Union(
                        _db.InputBills.Where(ib => ib.Status == 2)
                        .Join(_db.InputBillSubmitDetails.Where(ibs => _relateCodes.Contains(ibs.StoreInItemCode)),
                            ib => ib.Id,
                            ibsd => ibsd.InputBillId,
                            (ib, ibsd) => new { ib, ibsd })
                        .Join(_db.Debts,
                    temp => temp.ibsd.StoreInItemCode,
                    d => d.RelateCode,
                    (temp, d) => new { temp.ib, temp.ibsd, d })
                        .Select(x =>
                        x.d.BillCode)
                    )).ToListAsync();

                var _mergeInputDebtCodes = await (from md in _db.MergeInputBillDebts
                                                  join mr in _db.MergeInputBillRelations on md.MergeInputBillId equals mr.MergeInputBillId
                                                  join ib in _db.InputBills on mr.InputBillId equals ib.Id
                                                  where _debtCodes.Contains(md.DebtCode) && ib.Status == (int)InputBillStatusEnum.Submitted
                                                  select md.DebtCode).ToListAsync();
                if (_mergeInputDebtCodes.Any())
                {
                    _inputBillDebtCodes.AddRange(_mergeInputDebtCodes);
                }

                if (query.InvoiceStatus.Value)
                {
                    alls = alls.Where(x => _inputBillDebtCodes.ToHashSet().Contains(x.DebtCode)).ToList();
                }
                else
                {
                    alls = alls.Where(x => !_inputBillDebtCodes.ToHashSet().Contains(x.DebtCode)).ToList();
                }
            }
            var totalCount = alls.Count();
            var res = alls.Skip((query.page - 1) * query.limit).Take(query.limit).ToList();

            var debtDetailIds = res.Select(x => x.DebtDetilId).ToList();
            var debtDetails = await _db.DebtDetails.Where(x => debtDetailIds.Contains(x.Id)).AsNoTracking().ToListAsync();
            var creditIds = debtDetails.Select(x => x.CreditId).Distinct().ToList();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).AsNoTracking().ToListAsync();
            var orderNos = credits.Select(x => x.OrderNo).ToList();
            var invoiceCredits = await _db.InvoiceCredits.Where(x => creditIds.Contains(x.CreditId)).AsNoTracking().ToListAsync();
            var invoiceNos = invoiceCredits.Select(x => x.InvoiceNo).ToList();
            var debtCodes = res.Select(x => x.DebtCode).ToList();
            var debtReceiveCodes = res.Select(x => x.ReceiveCode).Distinct().ToList();  
            var noTaxAmountList = new List<InputBillQueryByDebtCodesOutput>();

            if (query.AccountPeriodType == AccountPeriodTypeEnum.Repayment || query.AccountPeriodType == AccountPeriodTypeEnum.Sale || query.AccountPeriodType == AccountPeriodTypeEnum.StoreIn) //回款或者销售、入库
            {
                noTaxAmountList = await _inputBillQueryService.GetListInputBillByDebtCodesAsync(debtCodes);

                var mergeInputList = await (from md in _db.MergeInputBillDebts
                                            join mr in _db.MergeInputBillRelations on md.MergeInputBillId equals mr.MergeInputBillId
                                            join ib in _db.InputBills on mr.InputBillId equals ib.Id
                                            where debtCodes.Contains(md.DebtCode) && ib.Status == (int)InputBillStatusEnum.Submitted
                                            select new InputBillQueryByDebtCodesOutput()
                                            {
                                                BillCode = md.DebtCode,
                                                InvoiceNumber = ib.InvoiceNumber,
                                                NoTaxAmount = md.DebtAmount,
                                                Status = ib.Status,
                                                Value = md.DebtAmount,
                                                Id = md.Id
                                            }).ToListAsync();
                if (mergeInputList.Any())
                {
                    noTaxAmountList.AddRange(mergeInputList);
                }
            }

            List<DebtDetailBankInfo> bankInfos = new List<DebtDetailBankInfo>();
            if (query.AccountPeriodType == AccountPeriodTypeEnum.Repayment)
            {
                bankInfos =  await (from  ri in _db.RecognizeReceiveItems 
                    where debtReceiveCodes.Contains(ri.ReceiveCode)
                    select new DebtDetailBankInfo()
                    {
                        ReceiveCode = ri.ReceiveCode,
                        Id = ri.Id,
                        BankName = ri.BankName,
                        BankNum = ri.BankNum
                    }).ToListAsync();
            }
            var codes = new List<string?>();
            codes.AddRange(orderNos);
            codes.AddRange(invoiceNos);
            codes.AddRange(credits.Select(p => p.BillCode));
            var creditsNos = credits.Select(p => p.BillCode);
            var rrds = await _db.RecognizeReceiveDetails.Include(x=>x.RecognizeReceiveItem).AsNoTracking().Where(x => codes.Contains(x.Code)).ToListAsync();
            var rriIds = rrds.Select(x => x.RecognizeReceiveItemId).ToList();
            var rris = await _db.RecognizeReceiveItems.AsNoTracking().Where(x => rriIds.Contains(x.Id)).ToListAsync();

            var abas = await _db.Abatements.AsNoTracking().Where(x => creditsNos.Any(p => p == x.CreditBillCode) || creditsNos.Any(p => p == x.DebtBillCode)).ToListAsync();

            foreach (var item in res)
            {
                //是否有进项票
                if (debtCodes != null && debtCodes.Any() && query.AccountPeriodType == AccountPeriodTypeEnum.Repayment || query.AccountPeriodType == AccountPeriodTypeEnum.Sale || query.AccountPeriodType == AccountPeriodTypeEnum.StoreIn) //回款或者销售、入库
                {
                    var _debtInvoices = noTaxAmountList?.Where(x => x.BillCode == item.DebtCode).ToList();
                    item.IsHaveInvoice = _debtInvoices?.Any();
                    item.InvoiceCodes = string.Join(',', _debtInvoices?.Select(o => o.InvoiceNumber)?.Distinct() ?? []);
                    item.NoTaxAmount = noTaxAmountList?.Where(p => p.BillCode == item.DebtCode).GroupBy(g => g.Id).Sum(p => p.FirstOrDefault()?.NoTaxAmount);
                }

                var currentCodes = new List<string?>();
                var currentDebtDetails = debtDetails.Where(x => x.DebtId == item.DebtId && x.ReceiveCode == item.ReceiveCode).ToList();
                if (currentDebtDetails != null && currentDebtDetails.Any())
                {
                    var currentCreditId = currentDebtDetails[0].CreditId;
                    var currentCredit = credits.FirstOrDefault(x => x.Id == currentCreditId);
                    if (currentCredit != null)
                    {
                        var aba = abas.FirstOrDefault(x => x.DebtBillCode == currentCredit.BillCode || x.CreditBillCode == currentCredit.BillCode);
                        item.BackPayTime = aba != null ? aba.Abtdate : item.BackPayTime;
                        currentCodes.Add(currentCredit.OrderNo);
                        currentCodes.Add(currentCredit.BillCode);
                    }
                    var currentInvoiceCredit = invoiceCredits.Where(x => x.CreditId == currentCreditId).ToList();
                    if (currentInvoiceCredit != null && currentInvoiceCredit.Any())
                    {
                        var currentInvoiceNos = currentInvoiceCredit.Select(x => x.InvoiceNo).ToList();
                        currentCodes.AddRange(currentInvoiceNos);
                    }

                    var single = rrds?.FirstOrDefault(x => currentCodes.Contains(x.Code) && x.RecognizeReceiveItem.ReceiveCode == item.ReceiveCode);
                    if (single != null)
                    {
                        item.RecognizeDate = single.RecognizeDate.ToString("yyyy-MM-dd");
                        var rri = rris.FirstOrDefault(x => x.Id == single.RecognizeReceiveItemId && x.ReceiveCode == item.ReceiveCode);
                        item.ReceiveDate = rri != null ? rri.ReceiveDate.ToString("yyyy-MM-dd") : string.Empty;
                    }
                }

                item.BankName = bankInfos.FirstOrDefault(x => x.ReceiveCode == item.ReceiveCode)?.BankName;
                item.BankNum = bankInfos.FirstOrDefault(x => x.ReceiveCode == item.ReceiveCode)?.BankNum;
            }

            return (res, totalCount);

        }

        /// <summary>
        /// 付款计划查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtDetailQueryListOutput>, int)> GetListDetailQueryAsync(DebtDetailQueryInput query)
        {
            Expression<Func<DebtDetailPo, bool>> exp = z => 1 == 1;
            exp = await InitExp(query, exp);
            IQueryable<DebtDetailPo> baseQuery = _db.DebtDetails.Include(z => z.Credit).Include(z => z.Debt).Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();

            if (query.Warnning.HasValue && query.Warnning.Value)
            {
                baseQuery = baseQuery.Where(p => p.ProbablyPayTime.HasValue && p.ProbablyPayTime.Value.AddDays(-5) <= DateTime.Now.Date);
            }

            #region 排序

            baseQuery = baseQuery.OrderBy(p => p.Status).ThenBy(z => z.ProbablyPayTime);

            #endregion

            //总条数
            var count = baseQuery.Count();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<DebtDetailQueryListOutput>()).ToListAsync();
            foreach (DebtDetailQueryListOutput item in list)
            {
                item.PurchaseContactNo = item.Debt?.PurchaseContactNo;
                item.BillCode = item.Debt?.BillCode;
                item.BusinessDeptFullName = item.Debt?.BusinessDeptFullName;
                item.CompanyName = item.Debt?.CompanyName;
                item.AgentName = item.Debt?.AgentName;
                item.ServiceName = item.Debt?.ServiceName;
                item.ProjectName = item.Debt?.ProjectName;
                item.CustomerName = item.Credit?.CustomerName;
                item.StatusStr = item.Status == DebtDetailStatusEnum.WaitExecute ? "待执行" : "已完成";
            }
            return (list, count);
        }

        /// <summary>
        /// 付款计划总数查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<int> GetDebtPayWarnningCount(DebtDetailQueryInput query)
        {
            Expression<Func<DebtDetailPo, bool>> exp = z => 1 == 1;
            exp = await InitExp(query, exp);
            IQueryable<DebtDetailPo> baseQuery = _db.DebtDetails.Include(z => z.Credit).Include(z => z.Debt).Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();

            if (query.Warnning.HasValue && query.Warnning.Value)
            {
                baseQuery = baseQuery.Where(p => p.ProbablyPayTime.HasValue && p.ProbablyPayTime.Value.AddDays(-5) <= DateTime.Now.Date);
            }

            //总条数
            var count = baseQuery.Count();

            return count;
        }

        private async Task<Expression<Func<DebtDetailPo, bool>>> InitExp(DebtDetailQueryInput query, Expression<Func<DebtDetailPo, bool>> exp)
        {
            #region 查询条件
            //获取用户数据策略
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        exp = exp.And(t => serviceIds.Contains(t.Debt.ServiceId));
                    }
                }
            }
            else
            {
                var strategry = query.StrategyQuery != null ? await _pcApiClient.GetStrategyAsync(query.StrategyQuery) : null;
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(z => z.Debt != null && strategList.Contains(z.Debt.CompanyId.Value));
                            }
                        }
                        if (key == "project")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => !t.Debt.ProjectId.HasValue || strategList.Contains(t.Debt.ProjectId.Value));
                            }
                        }
                        if (key == "accountingDept")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(z => z.Debt != null && strategList.Contains(z.Debt.BusinessDeptId));
                            }
                        }
                    }
                }
            }
            exp = exp.And(z => z.ProbablyPayTime != null);
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.Debt != null && (EF.Functions.Like(z.Debt.BillCode ?? "", $"%{query.searchKey}%")
                        || EF.Functions.Like(z.Debt.CompanyName, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.Code, $"%{query.searchKey}%")));
            }
            if (query.BillDateStart != null && query.BillDateEnd != null)
            {
                exp = exp.And(z => (z.ProbablyPayTime != null && z.ProbablyPayTime.Value >= query.BillDateStart && z.ProbablyPayTime.Value <= query.BillDateEnd));
            }
            if (query.CompanyId != null)
            {
                exp = exp.And(z => z.Debt != null && z.Debt.CompanyId == query.CompanyId);
            }
            if (query.AgentId != null)
            {
                exp = exp.And(z => z.Debt != null && z.Debt.AgentId == query.AgentId);
            }
            //核算部门
            if (query.department != null && !string.IsNullOrEmpty(query.department))
            {
                exp = exp.And(z => z.Debt != null && EF.Functions.Like(z.Debt.BusinessDeptFullPath ?? "", $"%{query.department}%"));
            }
            if (!string.IsNullOrWhiteSpace(query.BillCode))
            {
                exp = exp.And(z => z.Debt != null && EF.Functions.Like(z.Debt.BillCode ?? "", $"%{query.BillCode}%"));
            }
            if (query.AccountPeriodType != null)
            {
                exp = exp.And(z => z.AccountPeriodType == (int)query.AccountPeriodType);
            }
            if (query.Status != null)
            {
                exp = exp.And(z => z.Status == query.Status);
            }
            if (query.DebtId != null)
            {
                exp = exp.And(z => z.DebtId == query.DebtId);
            }
            if (!string.IsNullOrEmpty(query.PurchaseCode))
            {
                exp = exp.And(z => z.PurchaseCode == query.PurchaseCode);
            }
            if (!string.IsNullOrEmpty(query.StoreInItemCode))
            {
                exp = exp.And(z => z.Debt != null && z.Debt.RelateCode == query.StoreInItemCode);
            }
            if (query.ProjectId != null)
            {
                exp = exp.And(z => z.Debt != null && z.Debt.ProjectId == query.ProjectId);
            }
            if (query.AbatedStatus != null)
            {
                exp = exp.And(z => z.Debt != null && z.Debt.AbatedStatus == query.AbatedStatus);
            }
            if (query.AuditStatus != null && query.AuditStatus != StatusEnum.all)
            {
                exp = exp.And(z => z.AuditStatus == query.AuditStatus);
            }
            if (query.Id != null)
            {
                exp = exp.And(z => z.Id == query.Id);
            }
            #endregion
            return exp;
        }

        /// <summary>
        /// 获取公司、供应商在某个项目下的已使用上游额度
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="companyId"></param>
        /// <param name="agentId"></param>
        /// <param name="coinCode"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<decimal>> GetUsedCredit(Guid projectId, Guid companyId, Guid agentId, string coinCode = "CNY")
        {
            var debtQuery = _db.Debts.Where(p => p.CompanyId == companyId &&
            p.AgentId == agentId &&
            p.ProjectId == projectId &&
            p.AbatedStatus == AbatedStatusEnum.NonAbate &&
            p.CoinCode == coinCode).AsNoTracking();

            var minusDebtCodes = debtQuery.Where(p => p.Value < 0).Select(p => p.BillCode);//正数应付
            var debtCodes = debtQuery.Where(p => p.Value > 0).Select(p => p.BillCode);//负数应付
            var minusAbtAmount = _db.Abatements.Where(p => minusDebtCodes.Contains(p.DebtBillCode) || minusDebtCodes.Contains(p.CreditBillCode)).Sum(q => q.Value);
            var abtAmount = _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode)).Sum(q => q.Value);
            var minusAmount = debtQuery.Where(p => p.Value <= 0).Sum(q => q.Value);
            var Amount = debtQuery.Where(p => p.Value > 0).Sum(q => q.Value);
            //预付使用额度支付的数据也属于额度占用
            var preCreditQuery = _db.Payments.Where(p => p.CompanyId == companyId && p.AgentId == agentId && p.AdvancePayMode == AdvancePayModeEnum.UseQuota && string.IsNullOrEmpty(p.Code) && p.CreditAmount >= 0);
            var preCreditAmount = await preCreditQuery.Select(p => new { Amount = p.Value - p.CreditAmount }).ToListAsync();
            var usedAmount = preCreditAmount.Sum(q => q.Amount.Value) + minusAmount + minusAbtAmount + (Amount - abtAmount);
            return new BaseResponseData<decimal>()
            {
                Code = CodeStatusEnum.Success,
                Data = usedAmount,
                Message = "获取成功"
            };
        }

        /// <summary>
        /// 根据订单号得到应付
        /// </summary>
        /// <param name="purchaseCodes"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<DebtOfProjectOutput>>> GetDebtByPurchaseCodes(List<string> purchaseCodes)
        {
            var ret = BaseResponseData<List<DebtOfProjectOutput>>.Success("操作成功！");
            if (purchaseCodes != null && purchaseCodes.Any())
            {
                var debts = await _db.Debts.Where(x => purchaseCodes.ToHashSet().Contains(x.PurchaseCode)).ToListAsync();
                var debtCodes = debts.Select(p => p.BillCode);
                var abatements = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode)).ToListAsync();
                var data = new List<DebtOfProjectOutput>();
                foreach (var item in debts)
                {
                    var pos = abatements.Where(x => x.DebtBillCode == item.BillCode).ToList();
                    data.Add(new DebtOfProjectOutput
                    {
                        DebtCode = item.BillCode,
                        AgentId = item.AgentId,
                        AgentName = item.AgentName,
                        TotalValue = item.Value,
                        ActualTotalValue = pos != null && pos.Any() ? pos.Sum(p => p.Value) : 0,
                        AbatementTime = pos != null && pos.Any() ? pos.Max(p => p.CreatedTime) : DateTimeOffset.UtcNow
                    });
                }
                var payments = await _db.Payments.Where(x => purchaseCodes.ToHashSet().Contains(x.PurchaseCode)).ToListAsync();
                foreach (var item in payments)
                {
                    data.Add(new DebtOfProjectOutput
                    {
                        DebtCode = item.Code,
                        AgentId = item.AgentId,
                        AgentName = item.AgentName,
                        TotalValue = item.Value,
                        ActualTotalValue = item.Value,
                        AbatementTime = item.PaymentDate.Value
                    });
                }
                ret.Data = data;
            }
            else
            {
                ret = BaseResponseData<List<DebtOfProjectOutput>>.Failed(500, "操作失败，原因：参数不能为空");
            }
            return ret;
        }

        /// <summary>
        /// 根据订单号得到应付(纯享版)
        /// </summary>
        /// <param name="purchaseCodes"></param>
        /// <returns></returns>
        public async Task<List<DebtPo>> GetDebtByPurchaseCode(List<string?>? purchaseCodes)
        {
            if (purchaseCodes == null || !purchaseCodes.Any())
            {
                return new List<DebtPo>();
            }
            return await _db.Debts.Where(x => !string.IsNullOrEmpty(x.PurchaseCode) && purchaseCodes.ToHashSet().Contains(x.PurchaseCode)).ToListAsync();
        }

        /// <summary>
        /// 根据入库单号/应付单号得到应付(纯享版)
        /// </summary>
        /// <param name="storeInItemCodes"></param>
        /// <returns></returns>
        public async Task<List<DebtPo>> GetDebtByStoreInItemCode(List<string?>? storeInItemCodes)
        {
            if (storeInItemCodes == null || !storeInItemCodes.Any())
            {
                return new List<DebtPo>();
            }
            return await _db.Debts.Where(x => !string.IsNullOrEmpty(x.BillCode) && storeInItemCodes.ToHashSet().Contains(x.BillCode)).ToListAsync();
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>        
        public async Task<MemoryStream> DebtHasInvoiceExport(DebtQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                var (list, count) = await GetListAsync(query);
                if (list == null || list.Count <= 0)
                {
                    return stream;
                }
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("应付清单查询导出数据");
                    worksheet.SetValue(1, columnIndex++, "应付单号");
                    worksheet.SetValue(1, columnIndex++, "单据日期");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "供应商");
                    worksheet.SetValue(1, columnIndex++, "核算部门");
                    worksheet.SetValue(1, columnIndex++, "采购单号");
                    worksheet.SetValue(1, columnIndex++, "采购合同单号");
                    worksheet.SetValue(1, columnIndex++, "项目名称");
                    worksheet.SetValue(1, columnIndex++, "厂家单号");
                    worksheet.SetValue(1, columnIndex++, "关联单号");
                    worksheet.SetValue(1, columnIndex++, "币种");
                    worksheet.SetValue(1, columnIndex++, "币种金额");
                    worksheet.SetValue(1, columnIndex++, "人民币金额");
                    worksheet.SetValue(1, columnIndex++, "已冲销");
                    worksheet.SetValue(1, columnIndex++, "余额");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "应付类型");
                    worksheet.SetValue(1, columnIndex++, "创建人");
                    worksheet.SetValue(1, columnIndex++, "客户");

                    var index = 1;
                    list.ForEach(p =>
                    {
                        index++;
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.BillCode);
                        worksheet.SetValue(index, columnIndex++, p.BillDate.HasValue ? p.BillDate.Value.ToString("yyyy-MM-dd") : string.Empty);
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.AgentName);
                        worksheet.SetValue(index, columnIndex++, p.BusinessDeptFullName);
                        worksheet.SetValue(index, columnIndex++, p.PurchaseCode);
                        worksheet.SetValue(index, columnIndex++, p.PurchaseContactNo);
                        worksheet.SetValue(index, columnIndex++, p.ProjectName);
                        worksheet.SetValue(index, columnIndex++, p.ProducerOrderNo);
                        worksheet.SetValue(index, columnIndex++, p.RelateCode);
                        worksheet.SetValue(index, columnIndex++, p.CoinName);
                        worksheet.SetValue(index, columnIndex++, p.Value);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.RMBAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.AbatmentAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.Value > 0 ? p.LeftAmount : -p.LeftAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, p.DebtType.HasValue ? ((DebtTypeEnum)p.DebtType.Value).GetDescription() : string.Empty);
                        worksheet.SetValue(index, columnIndex++, p.CreatedBy);
                        worksheet.SetValue(index, columnIndex++, p.CustomerName);
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 异步导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(DebtQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.userId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_debtExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("应付清单导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }

        public async Task<MemoryStream> DownLoadDebtPayWarnningList(DebtDetailQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                var (list, count) = await GetListDetailQueryAsync(query);
                if (list == null || list.Count <= 0)
                {
                    return stream;
                }

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("付款预警查询导出数据");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "账期类型");
                    worksheet.SetValue(1, columnIndex++, "应付日期");
                    worksheet.SetValue(1, columnIndex++, "供应商");
                    worksheet.SetValue(1, columnIndex++, "应付单号");
                    worksheet.SetValue(1, columnIndex++, "应付金额");
                    worksheet.SetValue(1, columnIndex++, "逾期天数");

                    var index = 1;
                    list.ForEach(p =>
                    {
                        index++;
                        columnIndex = 1;

                        worksheet.SetValue(index, columnIndex++, p.Debt != null ? p.Debt.CompanyName : string.Empty);
                        worksheet.SetValue(index, columnIndex++, p.AccountPeriodTypeStr);
                        worksheet.SetValue(index, columnIndex++, p.ProbablyPayTime.HasValue ? p.ProbablyPayTime.Value.ToString("yyyy-MM-dd") : "");
                        worksheet.SetValue(index, columnIndex++, p.Debt != null ? p.Debt.AgentName : string.Empty);
                        worksheet.SetValue(index, columnIndex++, p.Debt != null ? p.Debt.BillCode : string.Empty);
                        worksheet.SetValue(index, columnIndex++, p.Value);
                        worksheet.SetValue(index, columnIndex++, p.OverdueDays);
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 根据项目id查询应付单总额
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<DebtSumAmountQueryOutput>>> GetDebtSumAmountByProjectId(List<DebtSumAmountQueryInput> inputs)
        {
            var ret = BaseResponseData<List<DebtSumAmountQueryOutput>>.Success("操作成功！");
            if (inputs.Count > 0)
            {
                var debtSumAmountList = new List<DebtSumAmountQueryOutput>();
                var projectIdAndDebtKv = new Dictionary<Guid, List<DebtPo>>();
                decimal debtDetailAmount = 0;
                decimal debtDetailAbatmentAmount = 0;
                var allProjectIds = inputs.Select(p => p.ProjectId).ToList();
                var allDebtDetails = await _db.Debts.Include(x => x.DebtDetails).Where(x => allProjectIds.ToHashSet().Contains(x.ProjectId.Value)).AsNoTracking().ToListAsync();


                foreach (var item in inputs)
                {
                    projectIdAndDebtKv[item.ProjectId] = allDebtDetails.Where(x => x.ProjectId == item.ProjectId && x.BillDate >= DateTimeHelper.LongToDateTime(item.BeginTime) && x.BillDate <= DateTimeHelper.LongToDateTime(item.EndTime) && x.CoinCode == "CNY" && x.DebtType == DebtTypeEnum.selforder).ToList();

                    foreach (var itemKv in projectIdAndDebtKv[item.ProjectId])
                    {
                        //变更核算部门导致的负数记录要做特殊处理
                        decimal MinusTypeValue = 0.0M;
                        if (itemKv.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE)
                        {
                            MinusTypeValue = itemKv.Value;
                        }
                        debtDetailAmount += itemKv.DebtDetails.Count > 0 ? itemKv.DebtDetails.Where(p => p.AccountPeriodType == (int)AccountPeriodTypeEnum.StoreIn || p.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment || p.AccountPeriodType == (int)AccountPeriodTypeEnum.Sale).Sum(p => p.Value) : 0;
                        debtDetailAmount += MinusTypeValue;
                        debtDetailAbatmentAmount += itemKv.DebtDetails.Count > 0 ? itemKv.DebtDetails.Where(p => p.Status == DebtDetailStatusEnum.Completed && (p.AccountPeriodType == (int)AccountPeriodTypeEnum.StoreIn || p.AccountPeriodType == (int)AccountPeriodTypeEnum.Repayment || p.AccountPeriodType == (int)AccountPeriodTypeEnum.Sale)).Sum(p => p.Value) : 0;
                    }
                    debtSumAmountList.Add(new DebtSumAmountQueryOutput()
                    {
                        ProjectId = item.ProjectId,
                        AllDebtAmount = debtDetailAmount,
                        AllAbatmentAmount = debtDetailAbatmentAmount,
                        AllBalanceAmount = debtDetailAmount - debtDetailAbatmentAmount,
                        ProjectName = projectIdAndDebtKv[item.ProjectId].FirstOrDefault() == null ? "" : projectIdAndDebtKv[item.ProjectId].FirstOrDefault().ProjectName,
                        ProjectCode = projectIdAndDebtKv[item.ProjectId].FirstOrDefault() == null ? "" : projectIdAndDebtKv[item.ProjectId].FirstOrDefault().ProjectCode,
                        BeginTime = item.BeginTime,//增加时间返回
                        EndTime = item.EndTime
                    });
                    debtDetailAmount = 0;
                    debtDetailAbatmentAmount = 0;
                }
                ret.Data = debtSumAmountList;
            }
            else
            {
                ret = BaseResponseData<List<DebtSumAmountQueryOutput>>.Failed(500, "操作失败，原因：参数不能为空");
            }
            return ret;
        }
        /// <summary>
        /// 获取应付单根据项目id
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="pageNo"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<BasePagedData<DebtQueryListOutput>>> GetDebtByProjectId(Guid projectId, DateTime beginTime, DateTime endTime, int pageNo, int pageSize)
        {
            var ret = BaseResponseData<BasePagedData<DebtQueryListOutput>>.Success("操作成功");
            try
            {
                if (pageNo <= 0)
                {
                    pageNo = 1;
                }
                if (pageSize <= 0)
                {
                    pageSize = 20;
                }
                IQueryable<DebtPo> baseQuery = _db.Debts.Include(p => p.DebtDetails).Where(p => p.ProjectId == projectId && p.BillDate >= beginTime && p.BillDate <= endTime && p.CoinCode == "CNY" && p.DebtType == DebtTypeEnum.selforder).AsNoTracking();
                var sql = baseQuery.ToQueryString();
                //总条数
                var count = await baseQuery.CountAsync();
                //分页
                var list = await baseQuery.Skip((pageNo - 1) * pageSize).Take(pageSize).Select(z => z.Adapt<DebtQueryListOutput>()).ToListAsync();
                foreach (var debt in list)
                {
                    //变更核算部门导致的负数记录要做特殊处理
                    decimal MinusTypeValue = 0.0M;
                    if (debt.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE)
                    {
                        MinusTypeValue = debt.Value;
                    }
                    // 获取应付明细里面的账期类型为入库+回款+销售的金额
                    debt.RMBAmount = debt.DebtDetails.Count > 0 ? debt.DebtDetails.Where(p => p.AccountPeriodType == AccountPeriodTypeEnum.StoreIn || p.AccountPeriodType == AccountPeriodTypeEnum.Repayment || p.AccountPeriodType == AccountPeriodTypeEnum.Sale).Sum(p => p.Value) : 0;
                    //已冲销金额等于明细里面已经完成的
                    debt.RMBAmount += MinusTypeValue;
                    debt.Value = debt.RMBAmount.Value;
                    debt.AbatmentAmount = debt.DebtDetails.Count > 0 ? debt.DebtDetails.Where(p => p.Status == DebtDetailStatusEnum.Completed && (p.AccountPeriodType == AccountPeriodTypeEnum.StoreIn || p.AccountPeriodType == AccountPeriodTypeEnum.Repayment || p.AccountPeriodType == AccountPeriodTypeEnum.Sale)).Sum(p => p.Value) : 0;
                    debt.BillDateTimestamp = DateTimeHelper.DateTimeToUnixTimestamp(debt.BillDate.Value);
                }
                ret.Code = CodeStatusEnum.Success;
                ret.Data = new BasePagedData<DebtQueryListOutput>
                {
                    Total = count,
                    List = list,
                    PageNum = pageNo,
                    PageSize = pageSize,
                    TotalPage = (count + pageSize - 1) / pageSize
                };

            }
            catch (Exception ex)
            {
                ret.Code = CodeStatusEnum.Failed;
                ret.Message = ex.Message;
                return ret;
            }
            return ret;
        }
        /// <summary>
        /// 保存提交账期起始日申请，然后推到OA审批
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SaveAccountPeriodEdit(AccountPeroidEditInput input)
        {
            try
            {
                if (!input.Id.HasValue)
                {
                    return BaseResponseData<string>.Failed(500, "请传入正确的付款计划Id");
                }
                var debtDetail = await _db.DebtDetails.Include(p => p.Debt).Where(p => p.Id == input.Id).AsNoTracking().FirstOrDefaultAsync();
                if (debtDetail == null)
                {
                    return BaseResponseData<string>.Failed(500, "没有找到当前付款计划单号数据");
                }
                //时间限制
                if (debtDetail.ProbablyPayTime == null)
                {
                    return BaseResponseData<string>.Failed(500, "预计付款日期不能为空");
                }
                if (debtDetail.Status == DebtDetailStatusEnum.Completed)
                {
                    return BaseResponseData<string>.Failed(500, "已完成状态不能调整预计付款日期");
                }
                var paymentAutoDetails = await _db.PaymentAutoDetails.Where(p => p.DebtDetilId == input.Id).AsNoTracking().ToListAsync();
                if (paymentAutoDetails.Count() > 0)
                {
                    return BaseResponseData<string>.Failed(500, "当前付款计划已被关联到批量付款明细，不能调整预计付款日期！");
                }
                var debtDetailAudits = await _db.DebtDetailAudit.Where(p => p.DebtDetailId == input.Id && p.Status == StatusEnum.waitAudit).AsNoTracking().ToListAsync();
                if (debtDetailAudits != null && debtDetailAudits.Count() > 0)
                {
                    return BaseResponseData<string>.Failed(500, "该预计付款日期调整申请还在审核中，请等待审核完成");
                }
                debtDetail.Remark = input.Remark;
                debtDetail.AccountingPeriodDate = input.AccountingPeriodDateEdit.Value.AddHours(8);//时区问题
                debtDetail.AttachFileIds = string.Join(',', input.AttachFileIds.ToArray());
                debtDetail.AuditStatus = StatusEnum.waitAudit;
                debtDetail.UpdatedBy = input.CurrentUser;
                debtDetail.UpdatedTime = DateTime.Now;
                var auditId = Guid.NewGuid();//审核单的id
                var code = DateTime.Now.ToString("yyyyMMddHHmmssffff") + new Random().Next(0, 100); //审核单的code
                #region 提交oa审核
                var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                {
                    BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                    {
                        Operator = input.CurrentUser,
                        RequestName = $"【财务-账期起始日调整申请】-[{code}]-{debtDetail?.Debt?.CompanyName}-{debtDetail?.Debt?.ProjectName}-{debtDetail?.Debt?.AgentName}-{debtDetail?.Debt?.CustomerName}",
                        Remark = input.Remark,
                        RequestId = 0,
                        RequestLevel = 1,
                    },
                    MainData = new Gateway.Common.WeaverOA.MainData
                    {
                        FCreatorID = debtDetail.CreatedBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/accountPeriodDateApplyOA?id={auditId}", //PC的Iframe地址,, //PC的Iframe地址,
                        Height_m = 500,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/finance/startingDateOfAccountingPeriodEmbedPage?id={auditId}",//手机地址
                        CpDepartment = debtDetail.Debt == null ? "" : debtDetail.Debt.BusinessDeptId,
                        CPcompanyCode = debtDetail.Debt == null ? "" : debtDetail.Debt.NameCode,
                        Condition = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                        Business_id = auditId.ToString(),
                    },
                    OtherParams = new Gateway.Common.WeaverOA.OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.AccountingPeriodDateForm);
                if (!oaRet.Status)
                {
                    return BaseResponseData<string>.Failed(500, oaRet.Msg);
                }
                debtDetail.OARequestId = oaRet.Data.Requestid.ToString();
                //成功提交
                var auditDebtDetail = new DebtDetailAuditPo()
                {
                    Id = auditId,
                    Code = code,
                    CreatedBy = input.CurrentUser,
                    AttachFileIds = string.Join(',', input.AttachFileIds.ToArray()),
                    DebtDetailId = input.Id,
                    OARequestId = oaRet.Data.Requestid.ToString(),
                    OriginProbablyPayTime = debtDetail.ProbablyPayTime,
                    CurrentProbablyPayTime = input.AccountingPeriodDateEdit.Value.AddHours(8),//时区问题
                    CreatedTime = DateTime.Now,
                    Remark = input.Remark,
                    Status = StatusEnum.waitAudit,
                };
                #endregion
                _db.DebtDetailAudit.Add(auditDebtDetail);
                _db.Update(debtDetail);
                await _unitOfWork.CommitAsync();
                return BaseResponseData<string>.Success("操作成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }
        /// <summary>
        /// 更新oa审批状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> UpdateDebtDetailStatus(Guid id, StatusEnum statusEnum)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var detailAudit = await _db.DebtDetailAudit.FirstOrDefaultAsync(p => p.Id == id);
            if (detailAudit != null)
            {
                var detail = await _db.DebtDetails.FirstOrDefaultAsync(p => p.Id == detailAudit.DebtDetailId);
                if (detail != null)
                {
                    detail.AuditStatus = statusEnum;
                    detail.UpdatedTime = DateTime.Now;
                    detailAudit.Status = statusEnum;
                    detailAudit.UpdatedTime = DateTime.Now;
                    if (statusEnum == StatusEnum.Complate)
                    {
                        if (detail.BackPayTime != null)
                        {
                            detail.BackPayTime = null;
                        }
                        detail.ProbablyPayTime = detailAudit.CurrentProbablyPayTime;//修改后的账期起始日直接赋值到预计付款日期（11·14会议）
                    }
                    _db.DebtDetails.Update(detail);
                }


                _db.DebtDetailAudit.Update(detailAudit);
            }
            else
            {
                return BaseResponseData<string>.Failed(500, "获取预计付款时间审核失败!预计付款时间审核Id:" + id);
            }
            await _unitOfWork.CommitAsync();
            return ret;
        }
        /// <summary>
        /// 预计付款申请列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <param name="exp"></param>
        /// <returns></returns>
        private async Task<Expression<Func<DebtDetailAuditPo, bool>>> InitExpAudit(DebtDetailQueryInput query, Expression<Func<DebtDetailAuditPo, bool>> exp)
        {
            #region 查询条件
            //获取用户数据策略
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        exp = exp.And(t => t.DebtDetail.Debt != null && serviceIds.Contains(t.DebtDetail.Debt.ServiceId));
                    }
                }
            }
            else
            {
                var strategry = query.StrategyQuery != null ? await _pcApiClient.GetStrategyAsync(query.StrategyQuery) : null;
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(z => z.DebtDetail.Debt != null && strategList.Contains(z.DebtDetail.Debt.CompanyId.Value));
                            }
                        }
                        if (key == "project")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => !t.DebtDetail.Debt.ProjectId.HasValue || strategList.Contains(t.DebtDetail.Debt.ProjectId.Value));
                            }
                        }
                        if (key == "accountingDept")
                        {
                            if (!strategry.RowStrategies[key].Any(s => s == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(z => z.DebtDetail.Debt != null && strategList.Contains(z.DebtDetail.Debt.BusinessDeptId));
                            }
                        }
                    }
                }
            }
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && (EF.Functions.Like(z.DebtDetail.Debt.BillCode ?? "", $"%{query.searchKey}%")
                        || EF.Functions.Like(z.DebtDetail.Debt.CompanyName, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.Code, $"%{query.searchKey}%")));
            }
            if (!string.IsNullOrEmpty(query.Code))
            {
                exp = exp.And(z => z.Code == query.Code);
            }
            if (query.CompanyId != null)
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && z.DebtDetail.Debt.CompanyId == query.CompanyId);
            }
            if (query.AgentId != null)
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && z.DebtDetail.Debt.AgentId == query.AgentId);
            }

            if (!string.IsNullOrWhiteSpace(query.BillCode))
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && EF.Functions.Like(z.DebtDetail.Debt.BillCode ?? "", $"%{query.BillCode}%"));
            }
            if (!string.IsNullOrEmpty(query.PurchaseCode))
            {
                exp = exp.And(z => z.DebtDetail.PurchaseCode == query.PurchaseCode);
            }
            if (!string.IsNullOrEmpty(query.StoreInItemCode))
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && z.DebtDetail.Debt.RelateCode == query.StoreInItemCode);
            }
            if (query.ProjectId != null)
            {
                exp = exp.And(z => z.DebtDetail.Debt != null && z.DebtDetail.Debt.ProjectId == query.ProjectId);
            }
            if (query.AuditStatus != null && query.AuditStatus != StatusEnum.all)
            {
                exp = exp.And(z => z.Status == query.AuditStatus);
            }
            if (query.Id != null)
            {
                exp = exp.And(z => z.DebtDetailId == query.Id);
            }
            if (query.AuditId != null)
            {
                exp = exp.And(z => z.Id == query.AuditId);
            }
            #endregion
            return exp;
        }

        /// <summary>
        /// 查询账期起始日申请列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtDetailAuditOutput>, int)> GetDebtDetailListQueryAsync(DebtDetailQueryInput query)
        {
            Expression<Func<DebtDetailAuditPo, bool>> exp = z => 1 == 1;
            exp = await InitExpAudit(query, exp);
            IQueryable<DebtDetailAuditPo> baseQuery = _db.DebtDetailAudit.Include(z => z.DebtDetail).Include(z => z.DebtDetail.Debt).Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();
            baseQuery = baseQuery.OrderByDescending(p => p.CreatedTime);
            //总条数
            var count = baseQuery.Count();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<DebtDetailAuditOutput>()).ToListAsync();

            return (list, count);
        }
        /// <summary>
        /// 查看账期起始日申请附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BizFileUploadOutput>> GetAttachFile(AccountPeriodDateAttachFileInput input)
        {
            var debtDetail = await _db.DebtDetailAudit.Where(c => c.Id == input.DebtDetailAuditItemId).AsNoTracking().FirstOrDefaultAsync();
            var bizFiles = new List<BizFileUploadOutput>();
            if (debtDetail != null && !string.IsNullOrEmpty(debtDetail.AttachFileIds))
            {
                var fileIds = debtDetail.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                return bizFiles;
            }
            return bizFiles;
        }
        /// <summary>
        /// 获取申请的tab 数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<DebtDetailListTableOutput>> GetTabCount(DebtDetailQueryInput query)
        {
            var ret = BaseResponseData<DebtDetailListTableOutput>.Success("操作成功");
            //查询需要修改的单据
            Expression<Func<DebtDetailAuditPo, bool>> expAll = (z => 1 == 1);
            Expression<Func<DebtDetailAuditPo, bool>> expWaitAudit = (z => 1 == 1);
            Expression<Func<DebtDetailAuditPo, bool>> expRefuse = (z => 1 == 1);
            Expression<Func<DebtDetailAuditPo, bool>> expComplate = (z => 1 == 1);
            Expression<Func<DebtDetailAuditPo, bool>> expMy = (z => 1 == 1);

            var data = new DebtDetailListTableOutput();
            #region 查询条件
            query.AuditStatus = StatusEnum.all;
            expAll = await InitExpAudit(query, expAll);
            data.AllCount = await _db.DebtDetailAudit.Where(expAll).CountAsync();

            query.AuditStatus = StatusEnum.waitAudit;
            expWaitAudit = await InitExpAudit(query, expWaitAudit);
            data.WaitAuditCount = await _db.DebtDetailAudit.Where(expWaitAudit).CountAsync();

            query.AuditStatus = StatusEnum.Refuse;
            expRefuse = await InitExpAudit(query, expRefuse);
            data.RefuseCount = await _db.DebtDetailAudit.Where(expRefuse).CountAsync();

            query.AuditStatus = StatusEnum.Complate;
            expComplate = await InitExpAudit(query, expComplate);
            data.ComplateCount = await _db.DebtDetailAudit.Where(expComplate).CountAsync();

            query.AuditStatus = StatusEnum.My;
            expMy = await InitExpAudit(query, expMy);
            data.MyCount = await _db.DebtDetailAudit.Where(expMy).CountAsync();
            #endregion


            ret.Data = data;
            return ret;
        }
        /// <summary>
        /// 获取应付单
        /// </summary>
        /// <param name="debtId"></param>
        /// <returns></returns>
        public async Task<DebtPo> GetDebtByIdAsync(Guid debtId)
        {
            var debt = await _db.Debts.AsNoTracking().SingleOrDefaultAsync(t => t.Id == debtId && t.AbatedStatus == AbatedStatusEnum.Abated && t.DebtType == DebtTypeEnum.servicefee);
            return debt;
        }

        /// <summary>
        /// 修改折扣
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> UpdateDebtDiscount(UpdateDebtDiscount input)
        {
            var ret = BaseResponseData<string>.Failed(500, "操作失败！");
            var debts = await _db.Debts.Include(p => p.DebtDetails).Where(p => p.PurchaseCode == input.PurchaseCode && p.AbatedStatus == AbatedStatusEnum.NonAbate).ToListAsync();
            if (debts == null || debts.Count() > 1)
            {
                ret.Message = "操作失败，原因：没有找到对应的应付数据或者该应付已冲销";
                return ret;
            }
            var debtDetails = debts.First().DebtDetails;
            var debtDetailIds = debts.First().DebtDetails.Select(p => p.Id);

            var PaymentAutoDetailCount = await _db.PaymentAutoDetails.Where(p => debtDetailIds.Contains(p.DebtDetilId)).CountAsync();
            if (PaymentAutoDetailCount > 0)
            {
                ret.Message = "操作失败，原因：应付明细已经加入批量付款中不允许修改";
                return ret;
            }
            if (debtDetails.Any())
            {
                debts.First().Value = input.UpdateDebtDiscountDetails.Sum(d => d.Value);
                debts.First().RMBAmount = input.UpdateDebtDiscountDetails.Sum(d => d.Value);
                var groupTempCount = debtDetails.GroupBy(p => p.AccountPeriodType).Count();
                if (groupTempCount != debtDetails.Count())
                {
                    ret.Message = $"操作失败，原因：应付单【{debts.First().BillCode}】明细中，有多个相同账期数据！";
                    return ret;
                }
                foreach (var debtDetail in debtDetails)
                {
                    var debtDiscountDetail = input.UpdateDebtDiscountDetails.Where(p => p.AccountPeriodType == debtDetail.AccountPeriodType).FirstOrDefault();
                    debtDetail.CostDiscount = input.CostDiscount;
                    debtDetail.DistributionDiscount = input.DistributionDiscount;
                    debtDetail.FinanceDiscount = input.FinanceDiscount;
                    debtDetail.SpdDiscount = input.SpdDiscount;
                    debtDetail.TaxDiscount = input.TaxDiscount;
                    if (debtDiscountDetail != null)
                    {
                        debtDetail.Value = debtDiscountDetail.Value;
                        debtDetail.OriginValue = debtDiscountDetail.OriginValue;
                    }
                    else
                    {
                        ret = BaseResponseData<string>.Success("操作失败，原因：没有找到对应的账期数据");
                    }
                }

                if ((await _db.SaveChangesAsync()) > 0)
                {
                    ret = BaseResponseData<string>.Success("操作成功");
                }
            }
            else
            {
                ret.Message = "操作失败，原因：没有找到对应的应付明细数据";
                return ret;
            }
            return ret;
        }

        /// <summary>
        /// 取消冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> CancelAbatmentAsync(CancelAbatmentInput input)
        {
            if (input.Ids == null || input.Ids.Count == 0)
            {
                throw new AppServiceException("操作失败，原因：参数不能为空");
            }
            var debt = await _db.Debts.FirstAsync(p => p.Id == input.DebtId);
            if (debt == null)
            {
                throw new AppServiceException("操作失败，原因：没有找到对应的应付数据");
            }
            if (!string.IsNullOrEmpty(debt.RelateCode) && debt.RelateCode.Contains("-ADV-"))
            {
                throw new AppServiceException("操作失败，原因：提前垫资产生的自动冲销应付单据无法撤销");
            }

            if (debt.CompanyId.HasValue)
            {
                await CheckInventoryState(debt.CompanyId.Value);
            }
            
            List<DebtDetailPo> debtDetails = new List<DebtDetailPo>();
            List<Guid> detailIds = new List<Guid>();
            if (debt.Value > 0)
            {
                debtDetails = await _db.DebtDetails.Where(p => debt.Id == p.DebtId).ToListAsync();

                if (debtDetails == null || debtDetails.Count == 0)
                {
                    throw new AppServiceException("操作失败，原因：没有找到对应的应付明细数据");
                }

                detailIds = debtDetails.Select(o => o.Id).ToList();
            }


            var abatements = await _db.Abatements.Where(w => input.Ids.Contains(w.Id)).ToListAsync();
            if (abatements == null)
            {
                throw new AppServiceException("操作失败，原因：没有找到对应的冲销记录");
            }
            if (abatements.Any(p => p.CreatedBy == "LossRecognition"))
            {
                throw new AppServiceException("操作失败，原因：不允许撤销确认损失的冲销记录");
            }

            ReverseSettlementInput reverseSettlement = new ReverseSettlementInput()
            {
                data = []
            };

            List<AbatementPo> abatementPos = new List<AbatementPo>();
            List<DebtPo> debtPos = new List<DebtPo>();
            List<CreditPo> creditPos = new List<CreditPo>();
            List<DebtDetailPo> debtDetailPos = new List<DebtDetailPo>();
            List<DebtDetailExcutePo> debtDetailExcutePos = new List<DebtDetailExcutePo>();
            List<PaymentPo> paymentPos = new List<PaymentPo>();
            List<Guid> excuteIds = new List<Guid>();

            foreach (var abatement in abatements)
            {
                var abateType = $"{abatement.DebtType}{abatement.CreditType}".Replace("debt", "");
                switch (abateType)
                {
                    case "credit":
                        var credit = await _db.Credits.FirstOrDefaultAsync(p => p.BillCode == abatement.CreditBillCode || p.BillCode == abatement.DebtBillCode);
                        if (credit == null)
                        {
                            throw new AppServiceException("操作失败，原因：没有找到对应的应收数据");
                        }
                        if (debt.Value > 0)
                        {
                            var debtDetailExcutes = await _db.DebtDetailExcutes.Where(p => detailIds.Contains(p.DebtDetailId) && p.PaymentCode == credit.BillCode && !excuteIds.Contains(p.Id)).ToListAsync();
                            if (debtDetailExcutes != null && debtDetailExcutes.Count > 0)
                            {
                                var numbers = debtDetailExcutes.Select(o => o.Value).OrderDescending().ToList();
                                List<decimal> result = new List<decimal>();
                                var combinaResult = FindCombination(numbers, abatement.Value, 0, result);
                                if (combinaResult)
                                {
                                    foreach (var item in result)
                                    {
                                        var debtDetailExcute = debtDetailExcutes.FirstOrDefault(p => !excuteIds.Contains(p.Id) && p.Value == item);
                                        if (debtDetailExcute != null)
                                        {
                                            var debtDetail = debtDetails?.FirstOrDefault(p => p.Id == debtDetailExcute.DebtDetailId && p.Status == DebtDetailStatusEnum.Completed);
                                            if (debtDetail != null)
                                            {
                                                debtDetail.Status = DebtDetailStatusEnum.WaitExecute;
                                                debtDetailPos.Add(debtDetail);
                                            }
                                            debtDetailExcutePos.Add(debtDetailExcute);
                                            excuteIds.Add(debtDetailExcute.Id);
                                        }
                                    }
                                }
                            }
                        }
                        if (debt.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            debt.AbatedStatus = AbatedStatusEnum.NonAbate;
                            debtPos.Add(debt);
                        }

                        if (credit.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            credit.AbatedStatus = AbatedStatusEnum.NonAbate;
                            creditPos.Add(credit);
                        }

                        abatementPos.Add(abatement);

                        reverseSettlement.data.Add(
                             new ReverseSettlementRequestDto
                             {
                                 mainBillNo = debt.BillCode,
                                 asstBillNo = credit.BillCode,
                                 reverseAmount = abatement.Value
                             });

                        break;
                    case "debt":
                    case "":
                        var debtCode2 = abatement.DebtBillCode == debt.BillCode ? abatement.CreditBillCode : abatement.DebtBillCode;
                        var debt2 = await _db.Debts.FirstOrDefaultAsync(p => p.BillCode == debtCode2);
                        if (debt2 == null)
                        {
                            throw new AppServiceException("操作失败，原因：没有找到对应的应付数据");
                        }

                        if (debt.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE || debt.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE || debt2.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE || debt2.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE)
                        {
                            throw new AppServiceException("操作失败，原因：更换核算部门自动冲销记录不允许撤销");
                        }
                        if (debt.Value > 0)
                        {
                            var debtDetailExcutes = await _db.DebtDetailExcutes.Where(p => detailIds.Contains(p.DebtDetailId) && p.PaymentCode == debt2.BillCode && !excuteIds.Contains(p.Id)).ToListAsync();
                            if (debtDetailExcutes != null && debtDetailExcutes.Count > 0)
                            {
                                var numbers = debtDetailExcutes.Select(o => o.Value).OrderDescending().ToList();
                                List<decimal> result = new List<decimal>();
                                var combinaResult = FindCombination(numbers, abatement.Value, 0, result);
                                if (combinaResult)
                                {
                                    foreach (var item in result)
                                    {
                                        var debtDetailExcute = debtDetailExcutes.FirstOrDefault(p => !excuteIds.Contains(p.Id) && p.Value == item);
                                        if (debtDetailExcute != null)
                                        {
                                            debtDetailExcutePos.Add(debtDetailExcute);
                                            excuteIds.Add(debtDetailExcute.Id);

                                            var debtDetail = debtDetails?.FirstOrDefault(p => p.Id == debtDetailExcute.DebtDetailId && p.Status == DebtDetailStatusEnum.Completed);
                                            if (debtDetail != null)
                                            {
                                                debtDetail.Status = DebtDetailStatusEnum.WaitExecute;
                                                debtDetailPos.Add(debtDetail);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            var _debtDetails = await _db.DebtDetails.Where(p => p.DebtId == debt2.Id).ToListAsync();
                            if (_debtDetails == null || _debtDetails.Count == 0)
                            {
                                throw new AppServiceException("操作失败，原因：没有找到对应的付款计划数据");
                            }

                            debtDetails?.AddRange(_debtDetails);
                            debtDetails?.DistinctBy(d => d.Id);

                            var _detailIds = _debtDetails.Select(o => o.Id).ToList();

                            var debtDetailExcutes = await _db.DebtDetailExcutes.Where(p => _detailIds.Contains(p.DebtDetailId) && p.PaymentCode == debt.BillCode && !excuteIds.Contains(p.Id)).ToListAsync();
                            if (debtDetailExcutes != null)
                            {
                                var numbers = debtDetailExcutes.Select(o => o.Value).OrderDescending().ToList();
                                List<decimal> result = new List<decimal>();
                                var combinaResult = FindCombination(numbers, abatement.Value, 0, result);
                                if (combinaResult)
                                {
                                    foreach (var item in result)
                                    {
                                        var debtDetailExcute = debtDetailExcutes.FirstOrDefault(p => !excuteIds.Contains(p.Id) && p.Value == item);
                                        if (debtDetailExcute != null)
                                        {
                                            debtDetailExcutePos.Add(debtDetailExcute);
                                            excuteIds.Add(debtDetailExcute.Id);

                                            var debtDetail = _debtDetails?.FirstOrDefault(p => p.Id == debtDetailExcute.DebtDetailId && p.Status == DebtDetailStatusEnum.Completed);
                                            if (debtDetail != null)
                                            {
                                                debtDetail.Status = DebtDetailStatusEnum.WaitExecute;
                                                debtDetailPos.Add(debtDetail);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (debt.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            debt.AbatedStatus = AbatedStatusEnum.NonAbate;
                            debtPos.Add(debt);
                        }

                        if (debt2.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            debt2.AbatedStatus = AbatedStatusEnum.NonAbate;
                            debtPos.Add(debt2);
                        }

                        abatementPos.Add(abatement);

                        reverseSettlement.data.Add(
                            new ReverseSettlementRequestDto
                            {
                                mainBillNo = debt!.Value > 0 ? debt.BillCode : debt2.BillCode,
                                asstBillNo = debt!.Value > 0 ? debt2.BillCode : debt.BillCode,
                                reverseAmount = abatement.Value
                            }
                        );

                        break;
                    case "payment":
                        var payment = await _db.Payments.FirstOrDefaultAsync(p => p.Code == abatement.CreditBillCode || p.Code == abatement.DebtBillCode);
                        if (payment == null)
                        {
                            throw new AppServiceException("操作失败，原因：没有找到对应的付款数据");
                        }
                        if (!string.IsNullOrEmpty(payment.PaymentAutoItemCode))
                        {
                            throw new AppServiceException("操作失败，原因：批量付款单的冲销记录不允许撤销");
                        }
                        if (!string.IsNullOrEmpty(payment.PurchaseCode))
                        {
                            throw new AppServiceException("操作失败，原因：采购预付不能撤销冲销");
                        }
                        if (debt.Value > 0)
                        {
                            var debtDetailExcutes = await _db.DebtDetailExcutes.Where(p => p.PaymentCode == payment.Code && detailIds.Contains(p.DebtDetailId) && !excuteIds.Contains(p.Id)).ToListAsync();

                            if (debtDetailExcutes != null && debtDetailExcutes.Count > 0)
                            {
                                var numbers = debtDetailExcutes.Select(o => o.Value).OrderDescending().ToList();
                                List<decimal> result = new List<decimal>();
                                var combinaResult = FindCombination(numbers, abatement.Value, 0, result);
                                if (combinaResult)
                                {
                                    foreach (var item in result)
                                    {
                                        var debtDetailExcute = debtDetailExcutes.FirstOrDefault(p => !excuteIds.Contains(p.Id) && p.Value == item);
                                        if (debtDetailExcute != null)
                                        {
                                            debtDetailExcutePos.Add(debtDetailExcute);
                                            excuteIds.Add(debtDetailExcute.Id);

                                            var debtDetail = debtDetails?.FirstOrDefault(o => o.Status == DebtDetailStatusEnum.Completed && o.Id == debtDetailExcute.DebtDetailId);
                                            if (debtDetail != null)
                                            {
                                                debtDetail.Status = DebtDetailStatusEnum.WaitExecute;
                                                debtDetailPos.Add(debtDetail);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (debt.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            debt.AbatedStatus = AbatedStatusEnum.NonAbate;
                            debtPos.Add(debt);
                        }

                        if (payment.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            payment.AbatedStatus = AbatedStatusEnum.NonAbate;
                            paymentPos.Add(payment);
                        }
                        abatementPos.Add(abatement);

                        var orginPayCode = payment.Code;
                        var nos = payment.Code?.Split("-");
                        if (payment.Code.Contains("PV-") && nos != null && nos.Length >= 3)
                        {
                            payment.Code = nos[0] + "-" + nos[1] + "-" + nos[2];
                        }
                        else if (payment.Code.Contains("PA-") && nos != null && nos.Length >= 4)
                        {
                            payment.Code = nos[0] + "-" + nos[1] + "-" + nos[2] + "-" + nos[3];
                        }

                        reverseSettlement.data.Add(
                            new ReverseSettlementRequestDto
                            {
                                mainBillNo = debt.BillCode,
                                asstBillNo = payment.Code,
                                reverseAmount = abatement.Value
                            }
                        );

                        break;
                    case "receive":
                        if (debt.Value > 0)
                        {
                            string receiveCode = abatement.DebtBillCode == debt.BillCode ? abatement.CreditBillCode : abatement.DebtBillCode;
                            var debtDetailExcutes = await _db.DebtDetailExcutes.Where(p => !excuteIds.Contains(p.Id) && detailIds.Contains(p.DebtDetailId) && p.PaymentCode == receiveCode).ToListAsync();
                            if (debtDetailExcutes != null && debtDetailExcutes.Count > 0)
                            {
                                var numbers = debtDetailExcutes.Select(o => o.Value).OrderDescending().ToList();
                                List<decimal> result = new List<decimal>();
                                var combinaResult = FindCombination(numbers, abatement.Value, 0, result);
                                if (combinaResult)
                                {
                                    foreach (var item in result)
                                    {
                                        var debtDetailExcute = debtDetailExcutes.FirstOrDefault(p => !excuteIds.Contains(p.Id) && p.Value == item);
                                        if (debtDetailExcute != null)
                                        {
                                            debtDetailExcutePos.Add(debtDetailExcute);
                                            excuteIds.Add(debtDetailExcute.Id);
                                            var debtDetail = debtDetails?.FirstOrDefault(o => o.Id == debtDetailExcute.DebtDetailId && o.Status == DebtDetailStatusEnum.Completed);
                                            if (debtDetail != null)
                                            {
                                                debtDetail.Status = DebtDetailStatusEnum.WaitExecute;
                                                debtDetailPos.Add(debtDetail);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (debt.AbatedStatus == AbatedStatusEnum.Abated)
                        {
                            debt.AbatedStatus = AbatedStatusEnum.NonAbate;
                            debtPos.Add(debt);
                        }

                        abatementPos.Add(abatement);

                        reverseSettlement.data.Add(
                            new ReverseSettlementRequestDto
                            {
                                mainBillNo = debt.BillCode,
                                asstBillNo = abatement.DebtBillCode == debt.BillCode ? abatement.CreditBillCode : abatement.DebtBillCode,
                                reverseAmount = abatement.Value
                            }
                        );

                        break;
                    default:
                        break;
                }
            }

            var res = await DebtAbtementReverseAsync(reverseSettlement);
            if (res == 1)
            {
                if (debtPos.Count > 0)
                {
                    _db.Debts.UpdateRange(debtPos);
                }
                if (debtDetailPos.Count > 0)
                {
                    var groups = debtDetailPos.GroupBy(g => new { g.AccountPeriodType, g.DebtId, g.CostDiscount, g.CreditId, g.AccountPeriodDays });
                    List<DebtDetailPo> delDetails = new List<DebtDetailPo>();
                    foreach (var item in groups)
                    {
                        var _item = item.First();
                        var waitDetails = debtDetails.Where(p => p.Status == DebtDetailStatusEnum.WaitExecute && p.AccountPeriodType == _item.AccountPeriodType && p.DebtId == _item.DebtId && p.CostDiscount == _item.CostDiscount && p.CreditId == _item.CreditId && p.Id != _item.Id && p.AccountPeriodDays == _item.AccountPeriodDays).ToList();
                        if (waitDetails != null && waitDetails.Count > 0)
                        {
                            _item.Value += waitDetails.Sum(o => o.Value);
                            _item.OriginValue += waitDetails.Sum(o => o.OriginValue);
                            delDetails.AddRange(waitDetails);
                        }
                    }
                    _db.DebtDetails.UpdateRange(debtDetailPos);

                    if (delDetails.Count > 0)
                    {
                        _db.DebtDetails.RemoveRange(delDetails);
                    }
                }
                if (debtDetailExcutePos.Count > 0)
                {
                    _db.DebtDetailExcutes.RemoveRange(debtDetailExcutePos);
                }
                if (abatementPos.Count > 0)
                {
                    _db.Abatements.RemoveRange(abatementPos);
                }
                if (paymentPos.Count > 0)
                {
                    _db.Payments.UpdateRange(paymentPos);
                }
                if (creditPos.Count > 0)
                {
                    _db.Credits.UpdateRange(creditPos);
                }
                await _unitOfWork.CommitAsync();
            }

            return new BaseResponseData<bool>() { Data = true };
        }
        private async Task MinusDebtAbtPurchasePub(Guid debtId, List<AbatementPo> abatements, List<DebtPo> minusDebts)
        {
            var debt = await _db.Debts.FirstOrDefaultAsync(p => p.Id == debtId);
            if (debt != null && abatements != null)
            {
                var debtAbtPurchasePubs = new List<DebtAbtPurchasePubInput>();
                if (abatements != null && abatements.Any())
                {
                    if (debt.Value < 0)
                    {
                        foreach (var abatement in abatements)
                        {
                            debtAbtPurchasePubs.Add(new DebtAbtPurchasePubInput
                            {
                                MinusDebtCode = debt.BillCode,
                                AbatementCode = abatement.DebtBillCode == debt.BillCode ? abatement.CreditBillCode : abatement.DebtBillCode,
                                AbatementAmount = abatement.Value * -1
                            });
                        }
                    }
                    else
                    {
                        if (minusDebts != null && minusDebts.Any())
                        {
                            foreach (var item in minusDebts)
                            {
                                var AbatementAmount = abatements.Where(p => p.DebtBillCode == item.BillCode || p.CreditBillCode == item.BillCode).ToList().Sum(p => p.Value);
                                if (AbatementAmount != 0)
                                {
                                    debtAbtPurchasePubs.Add(new DebtAbtPurchasePubInput
                                    {
                                        AbatementCode = debt.BillCode,
                                        MinusDebtCode = item.BillCode,
                                        AbatementAmount = AbatementAmount * -1
                                    });
                                }
                            }
                        }
                    }
                    _logger.LogWarning($"拆分的服务费采购：" + debtAbtPurchasePubs.ToJson());
                    await _daprClient.PublishEventAsync<List<DebtAbtPurchasePubInput>>("pubsub-default", "finance-purchase-minusdebtabtpurchase", debtAbtPurchasePubs);
                }
            }
        }
        private async Task<int> DebtAbtementReverseAsync(ReverseSettlementInput input)
        {
            var ret = await _kingdeeApiClient.ReverseSettlement(input);
            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }

            return 1;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="numbers"></param>
        /// <param name="target"></param>
        /// <param name="index"></param>
        /// <param name="currentCombination"></param>
        /// <returns></returns>
        public static bool FindCombination(List<decimal> numbers, decimal target, int index, List<decimal> currentCombination)
        {
            if (target == 0)
            {
                return true;
            }

            if (target < 0 || index >= numbers.Count)
            {
                return false;
            }

            // 包含当前数字
            currentCombination.Add(numbers[index]);
            if (FindCombination(numbers, target - numbers[index], index + 1, currentCombination))
            {
                return true;
            }

            // 不包含当前数字
            currentCombination.RemoveAt(currentCombination.Count - 1);
            if (FindCombination(numbers, target, index + 1, currentCombination))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 更换业务部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> ChangeBusinessDeptAsync(ChangeBusinessDeptInput input)
        {
            if (input.ProjectIds == null || input.ProjectIds.Count == 0)
            {
                throw new AppServiceException("操作失败，原因：项目id参数不能为空");
            }

            try
            {
                var projects = await _projectMgntApiClient.GetProjectListByIds(input.ProjectIds);
                if (projects == null || projects.Count == 0)
                {
                    throw new AppServiceException("操作失败，原因：没有找到对应的项目信息");
                }

                var exceptIds = input.ProjectIds.Except(projects.Where(w => w.Id.HasValue).Select(o => o.Id.Value));
                if (exceptIds != null && exceptIds.Count() > 0)
                {
                    throw new AppServiceException($"操作失败，原因：{string.Join(',', exceptIds)}没有找到对应的项目信息");
                }

                var combinedExp = projects.Select(o => o.Id + o.BusinessDeptFullPath);
                var debts = await _db.Debts.Include(x => x.DebtDetails).Where(p => !combinedExp.ToHashSet().Contains(p.ProjectId + p.BusinessDeptFullPath) && p.ProjectId.HasValue && input.ProjectIds.ToHashSet().Contains(p.ProjectId.Value) && p.AbatedStatus == AbatedStatusEnum.NonAbate && p.AutoType != DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE && p.AutoType != DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE).ToListAsync();
                //var debts = await _db.Debts.Include(x => x.DebtDetails).Where(p => p.BillCode == "IVDBD-DEFJNP-SO-2502-000103-001").ToListAsync();
                if (debts == null || debts.Count == 0)
                {
                    throw new AppServiceException("操作失败，原因：没有待转移的应付单信息");
                }
                await ChangeDeptHandler(projects, debts);
            }
            catch (Exception ex)
            {
                throw new AppServiceException(ex.Message);
            }
            return new BaseResponseData<bool>() { Data = true };
        }

        private async Task ChangeDeptHandler(List<ProjectAndBusinessDeptInfoOutput> projects, List<DebtPo> debts)
        {

            var debtCodes = debts.Select(p => p.BillCode).ToList();
            var abatements = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode)).ToListAsync();
            var migrationRecords = await _db.MigrationRecords.Where(p => p.DataType == MigrationDataTypeEnum.Debt).OrderByDescending(o => o.NewBillCode).ToListAsync();
            var groupDebts = debts.GroupBy(g => g.NameCode);
            DateTimeOffset now = DateTimeOffset.Now;

            foreach (var groupDebt in groupDebts)
            {
                //500一组分多次请求
                int pageSize = 200;
                int pageCount = (groupDebt.Count() / pageSize) + 1;

                for (int i = 0; i < pageCount; i++)
                {
                    var needChangeDebtList = groupDebt.Skip(i * pageSize).Take(pageSize).ToList();
                    List<Guid> debtDetailIds = needChangeDebtList.SelectMany(debt => debt.DebtDetails.Select(detail => detail.Id)).ToList();
                    var allDebtDetailExcutes = debtDetailIds.Count == 0 ? new List<DebtDetailExcutePo>() : await _db.DebtDetailExcutes.Where(p => debtDetailIds.ToHashSet().Contains(p.DebtDetailId)).ToListAsync();

                    List<AbatementPo> newAbatements = new List<AbatementPo>();
                    List<DebtPo> newDebts = new List<DebtPo>();
                    List<DebtDetailExcutePo> newDebtDetailExcutes = new List<DebtDetailExcutePo>();
                    List<MigrationRecordPo> newMigrationRecords = new List<MigrationRecordPo>();
                    List<KingdeeAdjustDebt> kingdeeDebts = new List<KingdeeAdjustDebt>();

                    foreach (var debt in needChangeDebtList)
                    {
                        var project = projects.FirstOrDefault(w => w.Id == debt.ProjectId);
                        if (project == null)
                        {
                            break;
                        }
                        if (debt.BusinessDeptFullPath == project.BusinessDeptFullPath)
                        {
                            break;
                        }
                        var exitMigration = migrationRecords.FirstOrDefault(w => w.OriginBillCode == debt.BillCode);
                        int lastIndex = 0;
                        if (exitMigration != null)
                        {
                            var numStr = exitMigration.NewBillCode.Split('-')?.Last();
                            if (!string.IsNullOrEmpty(numStr))
                            {
                                lastIndex = int.Parse(numStr) + 1;
                            };
                        }

                        debt.AbatedStatus = AbatedStatusEnum.Abated;
                        debt.UpdatedTime = now;
                        var leftValue = debt.Value;

                        MigrationRecordPo migrationRecord = new MigrationRecordPo()
                        {
                            CreatedTime = now,
                            CreatedBy = "none",
                            DataType = MigrationDataTypeEnum.Debt,
                            OriginBillCode = debt.BillCode,
                            MiddleBillCode = "",
                            NewBillCode = "",
                        };

                        if (debt.Value > 0)
                        {
                            leftValue = debt.Value - abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).Sum(p => p.Value);
                        }
                        else
                        {
                            leftValue = debt.Value + abatements.Where(p => p.DebtBillCode == debt.BillCode || p.CreditBillCode == debt.BillCode).Sum(p => p.Value);
                        }

                        if (leftValue > 0 && debt.Value > 0)
                        {
                            var debtDetails = debt.DebtDetails.Where(x => x.Status != DebtDetailStatusEnum.Completed);

                            var newDebt = new DebtPo()
                            {
                                AbatedStatus = AbatedStatusEnum.Abated,
                                BillCode = lastIndex == 0 ? debt.BillCode + "-001" : debt.BillCode + "-" + lastIndex.ToString().PadLeft(3, '0'),
                                Value = -leftValue,
                                DebtType = debt.DebtType,
                                ProjectId = debt.ProjectId,
                                BusinessDeptId = debt.BusinessDeptId,
                                BusinessDeptFullPath = debt.BusinessDeptFullPath,
                                BusinessDeptFullName = debt.BusinessDeptFullName,
                                AccountPeriodScale = debt.AccountPeriodScale,
                                AgentId = debt.AgentId,
                                AgentName = debt.AgentName,
                                Auto = DomainConstants.CHANGE_BUSSINESS_DEPT_AUTO,
                                AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE,
                                AutoTypeName = DomainConstants.CHANGE_BUSSINESS_DEPT_TYPE_NAME,
                                BillDate = debt.BillDate,
                                CoinCode = debt.CoinCode,
                                CoinName = debt.CoinName,
                                CompanyId = debt.CompanyId,
                                CompanyName = debt.CompanyName,
                                CustomerId = debt.CustomerId,
                                CustomerName = debt.CustomerName,
                                CreatedBy = debt.CreatedBy,
                                InvoiceAmount = debt.InvoiceAmount,
                                InvoiceStatus = debt.InvoiceStatus,
                                NameCode = debt.NameCode,
                                Note = debt.Note,
                                OrderNo = debt.OrderNo,
                                PaymentId = debt.PaymentId,
                                PerPaymentCodes = debt.PerPaymentCodes,
                                ProducerOrderNo = debt.ProducerOrderNo,
                                ProjectCode = debt.ProjectCode,
                                ProjectName = debt.ProjectName,
                                PurchaseCode = debt.PurchaseCode,
                                PurchaseContactNo = debt.PurchaseContactNo,
                                RelateCode = debt.RelateCode,
                                RMBAmount = debt.CoinCode == "CNY" ? -leftValue : Math.Round((-leftValue / debt.Value * debt.RMBAmount ?? 0), 2),
                                ServiceId = debt.ServiceId,
                                ServiceName = debt.ServiceName,
                                TaxRate = debt.TaxRate,
                                UpdatedBy = debt.UpdatedBy,
                                CreatedTime = now,
                                UpdatedTime = now,
                                IsInnerAgent = debt.IsInnerAgent,
                            };

                            newDebts.Add(newDebt);

                            kingdeeDebts.Add(new KingdeeAdjustDebt()
                            {
                                asstact_number1 = newDebt.AgentId.Value,
                                billno = newDebt.BillCode,
                                bizdate = debt.DebtType == DebtTypeEnum.origin ? now.UtcDateTime.Date : debt.BillDate.Value,
                                org_number = newDebt.NameCode,
                                payorg_number = newDebt.NameCode,
                                billtypeid_number = KingdeeHelper.TransferDebtType(newDebt.DebtType.Value),
                                jfzx_business_number = newDebt.BusinessDeptId,
                                jfzx_order_number = newDebt.OrderNo,
                                jfzx_creator = newDebt.CreatedBy,
                                currency_number = newDebt.CoinCode ?? "CNY",
                                pricetaxtotal4 = newDebt.Value,
                                amount2 = newDebt.Value,
                                associatedNumber = debt.BillCode,
                                newBusinessNumber = newDebt.BusinessDeptId,
                                adjustmentFlag = "B"
                            });
                            migrationRecord.MiddleBillCode = newDebt.BillCode;

                            var newDeptDebt = new DebtPo()
                            {
                                AbatedStatus = AbatedStatusEnum.NonAbate,
                                BillCode = lastIndex == 0 ? debt.BillCode + "-002" : debt.BillCode + "-" + (lastIndex + 1).ToString().PadLeft(3, '0'),
                                Value = leftValue,
                                DebtType = debt.DebtType,
                                ProjectId = debt.ProjectId,
                                BusinessDeptId = project.BusinessDeptId,
                                BusinessDeptFullPath = project.BusinessDeptFullPath,
                                BusinessDeptFullName = project.BusinessDeptFullName,
                                AccountPeriodScale = debt.AccountPeriodScale,
                                AgentId = debt.AgentId,
                                AgentName = debt.AgentName,
                                Auto = DomainConstants.CHANGE_BUSSINESS_DEPT_AUTO,
                                AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE,
                                AutoTypeName = DomainConstants.CHANGE_BUSSINESS_DEPT_TYPE_NAME,
                                BillDate = debt.BillDate,
                                CoinCode = debt.CoinCode,
                                CoinName = debt.CoinName,
                                CompanyId = debt.CompanyId,
                                CompanyName = debt.CompanyName,
                                CustomerId = debt.CustomerId,
                                CustomerName = debt.CustomerName,
                                CreatedBy = debt.CreatedBy,
                                InvoiceAmount = debt.InvoiceAmount,
                                InvoiceStatus = debt.InvoiceStatus,
                                NameCode = debt.NameCode,
                                Note = debt.Note,
                                OrderNo = debt.OrderNo,
                                PaymentId = debt.PaymentId,
                                PerPaymentCodes = debt.PerPaymentCodes,
                                ProducerOrderNo = debt.ProducerOrderNo,
                                ProjectCode = debt.ProjectCode,
                                ProjectName = debt.ProjectName,
                                PurchaseCode = debt.PurchaseCode,
                                PurchaseContactNo = debt.PurchaseContactNo,
                                RelateCode = debt.RelateCode,
                                RMBAmount = debt.CoinCode == "CNY" ? leftValue : Math.Round((leftValue / debt.Value * debt.RMBAmount ?? 0), 2),
                                ServiceId = debt.ServiceId,
                                ServiceName = debt.ServiceName,
                                TaxRate = debt.TaxRate,
                                UpdatedBy = debt.UpdatedBy,
                                CreatedTime = now,
                                UpdatedTime = now,
                                DebtDetails = new List<DebtDetailPo>(),
                                IsInnerAgent = debt.IsInnerAgent,
                            };
                            migrationRecord.NewBillCode = newDeptDebt.BillCode;

                            newAbatements.Add(new AbatementPo()
                            {
                                Abtdate = now.Date,
                                CreditType = "debt",
                                CreditBillCode = newDebt.BillCode,
                                DebtBillCode = debt.BillCode,
                                DebtType = "debt",
                                Value = leftValue,
                                CreatedTime = now,
                                CreatedBy = "none",
                            });

                            foreach (var debtDetail in debtDetails)
                            {
                                debtDetail.Status = DebtDetailStatusEnum.Completed;
                                debtDetail.UpdatedTime = now;

                                var debtDetailExcute = allDebtDetailExcutes.FirstOrDefault(p => p.DebtDetailId == debtDetail.Id);
                                if (debtDetailExcute == null)
                                {
                                    newDebtDetailExcutes.Add(new DebtDetailExcutePo()
                                    {
                                        DebtDetailId = debtDetail.Id,
                                        ExcuteType = "debt",
                                        CreatedTime = now,
                                        CreatedBy = "none",
                                        PaymentCode = newDebt.BillCode,
                                        PaymentDate = now.Date,
                                        Value = debtDetail.Value,
                                    });
                                }

                                newDeptDebt.DebtDetails.Add(new DebtDetailPo()
                                {
                                    Remark = debtDetail.Remark,
                                    Value = debtDetail.Value,
                                    DebtId = newDeptDebt.Id,
                                    Status = DebtDetailStatusEnum.WaitExecute,
                                    CreatedTime = now,
                                    UpdatedTime = now,
                                    CreatedBy = debtDetail.CreatedBy,
                                    UpdatedBy = debtDetail.UpdatedBy,
                                    AccountingPeriodDate = debtDetail.AccountingPeriodDate,
                                    AccountPeriodType = debtDetail.AccountPeriodType,
                                    AttachFileIds = debtDetail.AttachFileIds,
                                    AuditStatus = debtDetail.AuditStatus,
                                    BackPayTime = debtDetail.BackPayTime,
                                    Code = debtDetail.Code,
                                    CostDiscount = debtDetail.CostDiscount,
                                    CreditId = debtDetail.NewBussinessDeptCreditId ?? debtDetail.CreditId,
                                    NewBussinessDeptCreditId = null,
                                    Discount = debtDetail.Discount,
                                    DistributionDiscount = debtDetail.DistributionDiscount,
                                    DraftBillExpireDate = debtDetail.DraftBillExpireDate,
                                    FinanceDiscount = debtDetail.FinanceDiscount,
                                    IsInvoiceReceipt = debtDetail.IsInvoiceReceipt,
                                    OARequestId = debtDetail.OARequestId,
                                    OrderNo = debtDetail.OrderNo,
                                    OriginValue = debtDetail.OriginValue,
                                    ProbablyPayTime = debtDetail.ProbablyPayTime,
                                    PurchaseCode = debtDetail.PurchaseCode,
                                    ReceiveCode = debtDetail.ReceiveCode,
                                    RecognizeReceiveCode = debtDetail.RecognizeReceiveCode,
                                    Settletype = debtDetail.Settletype,
                                    SpdDiscount = debtDetail.SpdDiscount,
                                    TaxDiscount = debtDetail.TaxDiscount,

                                });
                            }

                            newDebts.Add(newDeptDebt);

                            kingdeeDebts.Add(new KingdeeAdjustDebt()
                            {
                                asstact_number1 = newDeptDebt.AgentId.Value,
                                billno = newDeptDebt.BillCode,
                                bizdate = debt.DebtType == DebtTypeEnum.origin ? now.UtcDateTime.Date : debt.BillDate.Value,
                                org_number = newDeptDebt.NameCode,
                                payorg_number = newDeptDebt.NameCode,
                                billtypeid_number = KingdeeHelper.TransferDebtType(newDeptDebt.DebtType.Value),
                                jfzx_business_number = newDeptDebt.BusinessDeptId,
                                jfzx_order_number = newDeptDebt.OrderNo,
                                jfzx_creator = newDeptDebt.CreatedBy,
                                currency_number = newDeptDebt.CoinCode ?? "CNY",
                                pricetaxtotal4 = newDeptDebt.Value,
                                amount2 = newDeptDebt.Value,
                                associatedNumber = debt.BillCode,
                                newBusinessNumber = newDeptDebt.BusinessDeptId,
                                adjustmentFlag = "C"
                            });
                        }

                        if (leftValue < 0 && debt.Value < 0)
                        {
                            var newDebt = new DebtPo()
                            {
                                AbatedStatus = AbatedStatusEnum.Abated,
                                BillCode = lastIndex == 0 ? debt.BillCode + "-001" : debt.BillCode + "-" + lastIndex.ToString().PadLeft(3, '0'),
                                Value = -leftValue,
                                DebtType = debt.DebtType,
                                ProjectId = debt.ProjectId,
                                BusinessDeptId = debt.BusinessDeptId,
                                BusinessDeptFullPath = debt.BusinessDeptFullPath,
                                BusinessDeptFullName = debt.BusinessDeptFullName,
                                AccountPeriodScale = debt.AccountPeriodScale,
                                AgentId = debt.AgentId,
                                AgentName = debt.AgentName,
                                Auto = DomainConstants.CHANGE_BUSSINESS_DEPT_AUTO,
                                AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE,
                                AutoTypeName = DomainConstants.CHANGE_BUSSINESS_DEPT_TYPE_NAME,
                                BillDate = debt.BillDate,
                                CoinCode = debt.CoinCode,
                                CoinName = debt.CoinName,
                                CompanyId = debt.CompanyId,
                                CompanyName = debt.CompanyName,
                                CustomerId = debt.CustomerId,
                                CustomerName = debt.CustomerName,
                                CreatedBy = debt.CreatedBy,
                                InvoiceAmount = debt.InvoiceAmount,
                                InvoiceStatus = debt.InvoiceStatus,
                                NameCode = debt.NameCode,
                                Note = debt.Note,
                                OrderNo = debt.OrderNo,
                                PaymentId = debt.PaymentId,
                                PerPaymentCodes = debt.PerPaymentCodes,
                                ProducerOrderNo = debt.ProducerOrderNo,
                                ProjectCode = debt.ProjectCode,
                                ProjectName = debt.ProjectName,
                                PurchaseCode = debt.PurchaseCode,
                                PurchaseContactNo = debt.PurchaseContactNo,
                                RelateCode = debt.RelateCode,
                                RMBAmount = debt.CoinCode == "CNY" ? -leftValue : Math.Round((-leftValue / debt.Value * debt.RMBAmount ?? 0), 2),
                                ServiceId = debt.ServiceId,
                                ServiceName = debt.ServiceName,
                                TaxRate = debt.TaxRate,
                                UpdatedBy = debt.UpdatedBy,
                                CreatedTime = now,
                                UpdatedTime = now,
                                IsInnerAgent = debt.IsInnerAgent
                            };
                            //newDebt.BillCode = debt.BillCode + "-001";
                            //newDebt.Value = -leftValue;
                            //newDebt.AbatedStatus = AbatedStatusEnum.abated;
                            //newDebt.CreatedTime = now;
                            newDebts.Add(newDebt);

                            kingdeeDebts.Add(new KingdeeAdjustDebt()
                            {
                                asstact_number1 = newDebt.AgentId.Value,
                                billno = newDebt.BillCode,
                                bizdate = debt.DebtType == DebtTypeEnum.origin ? now.UtcDateTime.Date : debt.BillDate.Value,
                                org_number = newDebt.NameCode,
                                payorg_number = newDebt.NameCode,
                                billtypeid_number = KingdeeHelper.TransferDebtType(newDebt.DebtType.Value),
                                jfzx_business_number = newDebt.BusinessDeptId,
                                jfzx_order_number = newDebt.OrderNo,
                                jfzx_creator = newDebt.CreatedBy,
                                currency_number = newDebt.CoinCode ?? "CNY",
                                pricetaxtotal4 = newDebt.Value,
                                amount2 = newDebt.Value,
                                associatedNumber = debt.BillCode,
                                newBusinessNumber = newDebt.BusinessDeptId,
                                adjustmentFlag = "B"
                            });

                            migrationRecord.MiddleBillCode = newDebt.BillCode;

                            var newDeptDebt = new DebtPo()
                            {
                                AbatedStatus = AbatedStatusEnum.NonAbate,
                                BillCode = lastIndex == 0 ? debt.BillCode + "-002" : debt.BillCode + "-" + (lastIndex + 1).ToString().PadLeft(3, '0'),
                                Value = leftValue,
                                DebtType = debt.DebtType,
                                ProjectId = debt.ProjectId,
                                BusinessDeptId = project.BusinessDeptId,
                                BusinessDeptFullPath = project.BusinessDeptFullPath,
                                BusinessDeptFullName = project.BusinessDeptFullName,
                                AccountPeriodScale = debt.AccountPeriodScale,
                                AgentId = debt.AgentId,
                                AgentName = debt.AgentName,
                                Auto = DomainConstants.CHANGE_BUSSINESS_DEPT_AUTO,
                                AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE,
                                AutoTypeName = DomainConstants.CHANGE_BUSSINESS_DEPT_TYPE_NAME,
                                BillDate = debt.BillDate,
                                CoinCode = debt.CoinCode,
                                CoinName = debt.CoinName,
                                CompanyId = debt.CompanyId,
                                CompanyName = debt.CompanyName,
                                CustomerId = debt.CustomerId,
                                CustomerName = debt.CustomerName,
                                CreatedBy = debt.CreatedBy,
                                InvoiceAmount = debt.InvoiceAmount,
                                InvoiceStatus = debt.InvoiceStatus,
                                NameCode = debt.NameCode,
                                Note = debt.Note,
                                OrderNo = debt.OrderNo,
                                PaymentId = debt.PaymentId,
                                PerPaymentCodes = debt.PerPaymentCodes,
                                ProducerOrderNo = debt.ProducerOrderNo,
                                ProjectCode = debt.ProjectCode,
                                ProjectName = debt.ProjectName,
                                PurchaseCode = debt.PurchaseCode,
                                PurchaseContactNo = debt.PurchaseContactNo,
                                RelateCode = debt.RelateCode,
                                RMBAmount = debt.CoinCode == "CNY" ? leftValue : Math.Round((leftValue / debt.Value * debt.RMBAmount ?? 0), 2),
                                ServiceId = debt.ServiceId,
                                ServiceName = debt.ServiceName,
                                TaxRate = debt.TaxRate,
                                UpdatedBy = debt.UpdatedBy,
                                CreatedTime = now,
                                UpdatedTime = now,
                                IsInnerAgent = debt.IsInnerAgent
                            };

                            newDebts.Add(newDeptDebt);

                            kingdeeDebts.Add(new KingdeeAdjustDebt()
                            {
                                asstact_number1 = newDeptDebt.AgentId.Value,
                                billno = newDeptDebt.BillCode,
                                bizdate = debt.DebtType == DebtTypeEnum.origin ? now.UtcDateTime.Date : debt.BillDate.Value,
                                org_number = newDeptDebt.NameCode,
                                payorg_number = newDeptDebt.NameCode,
                                billtypeid_number = KingdeeHelper.TransferDebtType(newDeptDebt.DebtType.Value),
                                jfzx_business_number = newDeptDebt.BusinessDeptId,
                                jfzx_order_number = newDeptDebt.OrderNo,
                                jfzx_creator = newDeptDebt.CreatedBy,
                                currency_number = newDeptDebt.CoinCode ?? "CNY",
                                pricetaxtotal4 = newDeptDebt.Value,
                                amount2 = newDeptDebt.Value,
                                associatedNumber = debt.BillCode,
                                newBusinessNumber = newDeptDebt.BusinessDeptId,
                                adjustmentFlag = "C"
                            });

                            migrationRecord.NewBillCode = newDeptDebt.BillCode;

                            newAbatements.Add(new AbatementPo()
                            {
                                Abtdate = now.Date,
                                CreditType = "debt",
                                CreditBillCode = newDebt.BillCode,
                                DebtBillCode = debt.BillCode,
                                DebtType = "debt",
                                Value = Math.Abs(leftValue),
                                CreatedTime = now,
                                CreatedBy = "none",
                            });
                        }

                        if (!string.IsNullOrEmpty(migrationRecord.MiddleBillCode) && !string.IsNullOrEmpty(migrationRecord.NewBillCode))
                        {
                            newMigrationRecords.Add(migrationRecord);
                        }
                    }

                    if (needChangeDebtList.Count > 0)
                    {
                        _db.Debts.UpdateRange(needChangeDebtList);
                    }

                    if (newDebts.Count > 0)
                    {
                        _db.Debts.AddRange(newDebts);

                        if (kingdeeDebts.Count > 0)
                        {

                            var kingdeeRes = await _kingdeeApiClient.PushDebtsAdjustToKingdee(kingdeeDebts);
                            if (!(kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800))
                            {
                                throw new AppServiceException("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                            }
                        }

                        if (newAbatements.Count > 0)
                        {
                            _db.Abatements.AddRange(newAbatements);
                        }

                        if (newDebtDetailExcutes.Count > 0)
                        {
                            _db.DebtDetailExcutes.AddRange(newDebtDetailExcutes);
                        }
                        if (newMigrationRecords.Count > 0)
                        {
                            _db.MigrationRecords.AddRange(newMigrationRecords);
                        }
                    }

                    await _unitOfWork.CommitAsync();
                }
            }
        }
        /// <summary>
        /// 修复数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<bool> RepaireDebtDiff(List<Guid> ids)
        {
            return await _debtRepository.RepaireDebtDiff(ids);
        }
        /// <summary>
        /// 根据应收id获取不在批量付款里面的应付
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<DebtQueryListOutput>, int)> GetDebtByCreditId(DebtQueryInput query)
        {
            Expression<Func<DebtPo, bool>> exp = z => 1 == 1;
            var debtIdsAndCredit = new Dictionary<Guid?, Guid?>();
            var debts = new List<DebtPo>();
            if (query.CreditIds != null && query.CreditIds.Count > 0)
            {
                var debtDetails = await _db.DebtDetails.Where(p => query.CreditIds.Contains(p.CreditId.Value)).ToListAsync();
                debts = await _db.Debts.Where(p => debtDetails.Select(p => p.DebtId).Contains(p.Id)).AsNoTracking().ToListAsync();


                var paymentAutoDetails = await _db.PaymentAutoDetails.Where(p => debtDetails.Select(p => p.Id).Contains(p.DebtDetilId)).ToListAsync();
                var paymentAutoItems = await _db.PaymentAutoItems.Where(p => paymentAutoDetails.Select(p => p.PaymentAutoItemId).Contains(p.Id)).AsNoTracking().ToListAsync();
                foreach (var item in debtDetails)
                {
                    var details = paymentAutoDetails.Where(p => p.DebtDetilId == item.Id).ToList();
                    var payNoCompleteds = paymentAutoItems.Where(p => details.Select(p => p.PaymentAutoItemId).Contains(p.Id) && p.Status != PaymentAutoItemStatusEnum.Completed).ToList();
                    if (payNoCompleteds.Count > 0) //在批量付款中没有在途的
                    {
                        debts.Remove(debts.Where(p => p.Id == item.DebtId).FirstOrDefault());
                    }
                    else
                    {
                        debtIdsAndCredit[item.DebtId] = item.CreditId;
                    }
                }
                exp = exp.And(p => debtIdsAndCredit.Keys.Contains(p.Id));
            }
            var creditList = await _db.Credits.Where(p => debtIdsAndCredit.Values.Contains(p.Id)).AsNoTracking().ToListAsync();


            #region 排序
            debts = debts.OrderBy(z => z.CreatedTime).ToList();
            #endregion

            //总条数
            var count = debts.Count;

            //分页
            var list = debts.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<DebtQueryListOutput>()).ToList();
            if (list.Count == 0)
            {
                return (list, count);
            }
            var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
            {
                Names = list.Select(x => x.CreatedBy).ToList(),
                Limit = list.Count()
            });

            //获取已冲销金额
            var debtCodes = list.Select(p => p.BillCode);
            var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode))
                                     .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            foreach (var debt in list)
            {
                debt.AbatmentAmount = abatments.Where(t => t.DebtBillCode == debt.BillCode || t.CreditBillCode == debt.BillCode).Sum(t => t.Value);
                if (userInfos.Data == null || userInfos.Data.List.Count() == 0 || userInfos.Data.List.Where(z => z.Name == debt.CreatedBy).Count() == 0)
                {
                    debt.CreatedByName = debt.CreatedBy;
                }
                else
                {
                    var user = userInfos.Data.List.FirstOrDefault(z => z.Name == debt.CreatedBy);
                    if (user != null)
                    {
                        debt.CreatedByName = user.DisplayName;
                    }
                    else
                    {
                        debt.CreatedByName = debt.CreatedBy;
                    }
                }
                debt.CreditBillCode = creditList.Where(p => debtIdsAndCredit[debt.Id] == p.Id).FirstOrDefault() == null ? "" : creditList.Where(p => debtIdsAndCredit[debt.Id] == p.Id).FirstOrDefault().BillCode;
            }

            return (list, count);
        }
        
        /// <summary>
        /// 盘点状态检查
        /// </summary>
        /// <param name="companyId"></param>
        private async Task CheckInventoryState(Guid companyId)
        {
            string sysMonth = DateTime.Now.ToString("yyyy-MM");
            var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
            if (inventory != null)
            {
                if (inventory.Status == 2)
                {
                    throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                }
                if (inventory.Status == 99)
                {
                    DateTime.TryParse(sysMonth, out DateTime billDate);
                    if (billDate.Year == DateTime.Now.Year && billDate.Month == DateTime.Now.Month)
                    {
                        throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                    }
                }
            }
        }
        
        /// <summary>
        /// 创建付款计划清单导出任务（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<ExportTaskResDto>> AddDetailExportTaskAsync(DebtDetailQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"]!.ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_debtDetailExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("付款计划清单导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var tasksResult = await _coordinateclient.AddTasksAsync(tasks);
                return new BaseResponseData<ExportTaskResDto>()
                {
                    Data = tasksResult.Data?.FirstOrDefault(),
                    Code = tasksResult.Code,
                    Message = tasksResult.Message
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<ExportTaskResDto>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
    }
}

