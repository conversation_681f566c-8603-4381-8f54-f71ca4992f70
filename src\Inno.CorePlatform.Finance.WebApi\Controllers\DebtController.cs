﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 应付
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DebtController : ControllerBase
    {
        private ILogger<DebtController> _logger;
        private IDebtQueryService _debtQueryService;
        private IPaymentQueryService _paymentQueryService;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="debtQueryService"></param>
        public DebtController(
            ILogger<DebtController> logger,
            IPaymentQueryService paymentQueryService,
            IDebtQueryService debtQueryService)
        {
            this._logger = logger;
            this._debtQueryService = debtQueryService;
            this._paymentQueryService = paymentQueryService;
        }

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="relateCode">关联单号</param>
        /// <returns></returns>
        [HttpGet("GetDebtInfoList")]
        public async Task<BaseResponseData<List<DebtQueryOutput>>> GetDebtInfoList(string relateCode)
        {
            var result = new BaseResponseData<List<DebtQueryOutput>>();
            try
            {
                var debtInfoList = await _debtQueryService.GetDebtInfoListAsync(relateCode);
                result.Data = debtInfoList;
                result.Code = CodeStatusEnum.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取应付信息失败");
                result.Code = CodeStatusEnum.Failed;
                result.Message = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 获取应付信息
        /// </summary>
        /// <param name="purchaseCode">采购单号</param>
        /// <returns></returns>
        [HttpGet("GetDebtInfoListByPurchaseCode")]
        public async Task<BaseResponseData<List<DebtQueryOutput>>> GetDebtInfoListByPurchaseCode(string purchaseCode)
        {
            var result = new BaseResponseData<List<DebtQueryOutput>>();
            try
            {
                var debtInfoList = await _debtQueryService.GetDebtInfoListByPurchaseCodeAsync(purchaseCode);
                result.Data = debtInfoList;
                result.Code = CodeStatusEnum.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取应付信息失败");
                result.Code = CodeStatusEnum.Failed;
                result.Message = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 获取公司、供应商在某个项目下的已用额度
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="companyId"></param>
        /// <param name="agentId"></param>
        /// <returns></returns>
        [HttpGet("GetUsedCredit")]
        public async Task<BaseResponseData<decimal>> GetUsedCredit(Guid projectId, Guid companyId, Guid agentId)
        {
            return await _debtQueryService.GetUsedCredit(projectId, companyId, agentId);
        }

        /// <summary>
        /// 获取公司、供应商在某个项目下的已用额度 和 游离的付款单
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="companyId"></param>
        /// <param name="agentId"></param>
        /// <param name="coinCode"></param>
        /// <returns></returns>
        [HttpGet("GetUsedCreditAndPayment")]
        public async Task<BaseResponseData<UsedCreditAndPaymentOutput>> GetUsedCreditAndPayment(Guid projectId, Guid companyId, Guid agentId, string coinCode = "CNY")
        {
            var ret = BaseResponseData<UsedCreditAndPaymentOutput>.Success("操作成功");
            var payments = await _paymentQueryService.GetPaymentOfNoPurchaseCode(new PaymentQueryInputApi { AgentId = agentId, CoinCode = coinCode, companyId = companyId, projectId = projectId });
            var usedCredit = (await _debtQueryService.GetUsedCredit(projectId, companyId, agentId, coinCode)).Data;
            //过滤掉金额为0的付款单
            if (payments != null) { payments = payments.Where(r => Math.Abs(r.Value) > 0).ToList(); }
            ret.Data = new UsedCreditAndPaymentOutput
            {
                PaymentQueryOutputs = payments,
                UsedCredit = usedCredit
            };
            return ret;
        }

        /// <summary>
        /// 根据采购单号得到应付
        /// </summary>
        /// <param name="purchaseCodes"></param>
        /// <returns></returns>
        [HttpPost("GetDebtByPurchaseCodes")]
        public async Task<BaseResponseData<List<DebtOfProjectOutput>>> GetDebtByPurchaseCodes(List<string> purchaseCodes)
        {
            return await _debtQueryService.GetDebtByPurchaseCodes(purchaseCodes);
        }
        /// <summary>
        /// 根据项目id获取应付单
        /// </summary>
        /// <param name="input">入参</param>
        /// <returns></returns>
        [HttpPost("GetDebtByProjectId")]
        public async Task<BaseResponseData<BasePagedData<DebtQueryListOutput>>> GetDebtByProjectId(DebtPurchaseQueryInput input)
        {
            return await _debtQueryService.GetDebtByProjectId(input.ProjectId, DateTimeHelper.LongToDateTime(input.BeginTime), DateTimeHelper.LongToDateTime(input.EndTime), input.PageNum, input.PageSize);
        }
        /// <summary>
        /// 根据项目id获取应付总金额
        /// </summary>
        /// <param name="input">项目id列表</param>
        /// <returns></returns>
        [HttpPost("GetDebtSumAmountByProjectId")]
        public async Task<BaseResponseData<List<DebtSumAmountQueryOutput>>> GetDebtSumAmountByProjectId(List<DebtSumAmountQueryInput> input)
        {
            return await _debtQueryService.GetDebtSumAmountByProjectId(input);
        }

        /// <summary>
        /// 获取应付单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("downloadDebt")]
        public async Task<ResponseData<DebtQueryListOutput>> DownloadDebt(DebtQueryInput query)
        {
            query.IsExport = true;
            var (list, count) = await _debtQueryService.GetListAsync(query);
            return new ResponseData<DebtQueryListOutput>
            {
                Code = 200,
                Data = new Data<DebtQueryListOutput>
                {
                    List = list,
                    Total = count,
                }
            };
        }

        /// <summary>
        /// 修改折扣
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("UpdateDebtDiscount")]
        public async Task<BaseResponseData<string>> UpdateDebtDiscount(UpdateDebtDiscount input)
        {
            var requestBody = JsonConvert.SerializeObject(input);
            _logger.LogInformation("修改折扣,消息体：" + requestBody);
            return await _debtQueryService.UpdateDebtDiscount(input);
        }

        [HttpPost("RepaireDebtDiff")]
        public async Task<bool> RepaireDebtDiff(List<Guid> ids)
        {
            return await _debtQueryService.RepaireDebtDiff(ids);
        }
        
        /// <summary>
        /// 付款计划清单信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("downloadDebtDetail")]
        public async Task<ResponseData<DebtDetailQueryListOutput>> DownloadDebtDetailAsync(DebtDetailQueryInput query)
        {
            var result = await _debtQueryService.GetListDetailQueryAsync(query);
            return new ResponseData<DebtDetailQueryListOutput>
            {
                Code = 200,
                Data = new Data<DebtDetailQueryListOutput>
                {
                    List = result.Item1,
                    Total = result.Item2,
                }
            };
        }
    }
}
