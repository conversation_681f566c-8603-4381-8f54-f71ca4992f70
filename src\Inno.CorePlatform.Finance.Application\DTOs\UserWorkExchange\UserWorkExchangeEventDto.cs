using System;

namespace Inno.CorePlatform.Finance.Application.DTOs.UserWorkExchange
{
    /// <summary>
    /// 用户工作交换事件DTO
    /// </summary>
    public class UserWorkExchangeEventDto
    {
        /// <summary>
        /// 原用户ID
        /// </summary>
        public Guid UserId { get; set; }
        
        /// <summary>
        /// 原用户名
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// 原用户显示名称
        /// </summary>
        public string DisplayName { get; set; }
        
        /// <summary>
        /// 原员工ID
        /// </summary>
        public string StaffId { get; set; }
        
        /// <summary>
        /// 目标用户ID
        /// </summary>
        public Guid TargetUserId { get; set; }
        
        /// <summary>
        /// 目标用户名
        /// </summary>
        public string TargetUserName { get; set; }
        
        /// <summary>
        /// 目标用户显示名称
        /// </summary>
        public string TargetDisplayName { get; set; }
        
        /// <summary>
        /// 目标员工ID
        /// </summary>
        public string TargetStaffId { get; set; }
    }
}
