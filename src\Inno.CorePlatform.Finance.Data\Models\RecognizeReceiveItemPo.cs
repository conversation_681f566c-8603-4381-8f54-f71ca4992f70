﻿using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Data.Models
{
    [Table("RecognizeReceiveItem")]
    [Comment("认款单表")] 
    [Index("Code", IsUnique = true)]
    public class RecognizeReceiveItemPo : BasePo
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        [Comment("认款单号")]
        [MaxLength(200)]
        public string Code { get; set; }
        /// <summary>
        /// 单号日期
        /// </summary>
        [Comment("单号日期")]
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        [Comment("收款单号")]
        [MaxLength(200)]
        public string ReceiveCode { get; set; }
        /// <summary>
        /// 本次认款金额
        /// </summary>
        [Comment("本次认款金额")]
        [Precision(18, 4)]
        public decimal Value { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        [Comment("收款金额")]
        [Precision(18, 4)]
        public decimal ReceiveValue { get; set; }
        /// <summary>
        /// 收款单位Id
        /// </summary>
        [Comment("收款单位Id")]
        [MaxLength(50)]
        public string CompanyId { get; set; }
        /// <summary>
        /// 收款单位名称
        /// </summary>
        [Comment("收款单位名称")]
        [MaxLength(200)]
        public string CompanyName { get; set; }
        /// <summary>
        /// 付款单位Id
        /// </summary>
        [Comment("付款单位Id")]
        [MaxLength(50)]
        public string CustomerId { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary>
        [Comment("付款单位名称")]
        [MaxLength(200)]
        public string CustomerNme { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 收款时间
        /// </summary>
        public DateTime ReceiveDate { get; set; }
        /// <summary>
        /// 批量附件Id
        /// </summary>
        [Comment("批量附件Id")]
        public string? AttachFileIds { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        [Comment("核算部门Id路径")]
        [MaxLength(500)]
        public string? BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        [Comment("核算部门名称路径")]
        [MaxLength(500)]
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int? BusinessDepId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public RecognizeReceiveItemStatusEnum Status { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public RecognizeReceiveClassifyEnum Classify { get; set; }
        /// <summary>
        /// 认款明细（货款）
        /// </summary>
        public virtual List<RecognizeReceiveDetailPo> RecognizeReceiveDetails { get; set; } = new List<RecognizeReceiveDetailPo>();
        /// <summary>
        /// 认款明细(暂收款)
        /// </summary>
        public virtual List<RecognizeReceiveTempDetailPo> RecognizeReceiveTempDetails { get; set; } = new List<RecognizeReceiveTempDetailPo>();

        /// <summary>
        /// 单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        [MaxLength(1000)]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary>  
        [Comment("项目名称")]
        [MaxLength(1000)]
        public string? ProjectName { get; set; }
         
        /// <summary>
        /// 结算方式 
        /// </summary>  
        [Comment("结算方式")]
        [MaxLength(200)]
        public string? Settletype { get; set; }

        /// <summary>
        /// 到期日 
        /// </summary>  
        [Comment("到期日")]
        [MaxLength(200)]
        public DateTime? DraftBillExpireDate { get; set; }

        /// <summary>
        /// 银行账户
        /// </summary>
        public string? BankNum { get; set; }

        /// <summary>
        /// 银行类型(名称)
        /// </summary>
        public string? BankName { get; set; }

        /// <summary>
        /// 贴现日期
        /// </summary>
        public DateTime? DiscountDate { get; set; }

        /// <summary>
        /// 交易时间
        /// </summary>
        [Comment("交易时间")]
        public DateTime? BizTime { get; set; }

        /// <summary>
        /// 剩余认款金额
        /// </summary>
        [Comment("剩余认款金额")]
        [Precision(18, 4)]
        public decimal? RemainingAmount { get; set; }
    }
}
