﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Newtonsoft.Json;
using Npoi.Mapper;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 经销购货修订
    /// </summary>
    public class PurchaseSelfReviseAppService : BaseAppService, IPurchaseSelfReviseAppService
    {
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IPurchasePayPlanRepository _purchasePayPlanRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IExchangeRateService _exchangeRateService;
        public PurchaseSelfReviseAppService(
            IPaymentRepository paymentRepository,
            IPurchasePayPlanRepository purchasePayPlanRepository,
            LogServices.Interfaces.ISubLogService subLogRepository,
            ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            IPurchaseApiClient purchaseApiClient,
            IBDSApiClient bDSApiClient,
            IUnitOfWork _unitOfWork,
            IKingdeeApiClient kingdeeApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IDomainEventDispatcher? deDispatcher,
            ICodeGenClient codeGenClient,
            IExchangeRateService exchangeRateService,
            IAppServiceContextAccessor? contextAccessor) :
            base(creditRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._unitOfWork = _unitOfWork;
            this._purchaseApiClient = purchaseApiClient;
            this._purchasePayPlanRepository = purchasePayPlanRepository;
            this._bDSApiClient = bDSApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._codeGenClient = codeGenClient;
            this._exchangeRateService = exchangeRateService;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var purchaseOrder = await _purchaseApiClient.GetByIdAsync(input.BusinessId.Value);
                if (purchaseOrder == null || purchaseOrder.PurchaseOrderDetails == null || !purchaseOrder.PurchaseOrderDetails.Any())
                {
                    //return BaseResponseData<int>.Failed(500, "未获取到单据或单据明细为空");
                    throw new Exception("未获取到单据或单据明细为空");
                }
                var check = await base.IsCreatedDebtForBill(purchaseOrder.Code);
                if (check)
                {
                    if (input.IsAutoBill.HasValue && input.IsAutoBill.Value)
                    {
                        return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                    }
                    else
                    {
                        throw new Exception("该单据已生成过应收");
                    }
                }
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { purchaseOrder.Consignee.Id.ToString() }
                })).FirstOrDefault();
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                var productIds = purchaseOrder.PurchaseOrderDetails.Select(p => p.Product.NameId.Value).Distinct().ToList();
                var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
                int limit = 500;//一次最多500条

                var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
                productNameInfoOutputAll.AddRange(productInfo);
                int totalPages = (int)Math.Ceiling((double)total / limit);
                if (totalPages > 1)
                {
                    for (int i = 2; i <= totalPages; i++)
                    {
                        (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                        productNameInfoOutputAll.AddRange(productInfo);
                    }
                }

                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(new List<Guid>() { Guid.Parse(purchaseOrder.Project.Id) });
                var agents = purchaseOrder.Agent != null && purchaseOrder.Agent.Id != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { purchaseOrder.Agent.Id.Value }) : null;

                var debt = new DebtDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    BillCode = purchaseOrder.Code,
                    BillDate = billDate, //purchaseOrder.BillDate.Value.Date,
                    CreatedBy = purchaseOrder.CreatedBy ?? "none",
                    CreatedTime = DateTime.Now,
                    Id = Guid.NewGuid(),
                    Value = Math.Round(purchaseOrder.PurchaseOrderDetails.Sum(p => Math.Abs(p.Quantity) * p.Cost), 2),
                    AgentId = purchaseOrder.Agent.Id,
                    AgentName = purchaseOrder.Agent.Name,
                    IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                    CompanyId = purchaseOrder.Consignee.Id,
                    CompanyName = purchaseOrder.Consignee.Name,
                    RelateCode = purchaseOrder.Code,
                    DebtType = DebtTypeEnum.selfrevise,
                    NameCode = purchaseOrder.Consignee.NameCode,
                    ServiceId = purchaseOrder.Service?.Id,
                    ServiceName = purchaseOrder.Service?.Name,
                    BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                    BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                    BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                    ProjectId = Guid.Parse(purchaseOrder.Project.Id),
                    ProjectCode = projectInfo.FirstOrDefault()?.Code,
                    ProjectName = projectInfo.FirstOrDefault()?.Name,
                    PurchaseContactNo = purchaseOrder?.Contract?.Code,
                    ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                    DebtDetails = new List<DebtDetail>(),
                    OrderNo = purchaseOrder.Code,
                    CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                    CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                    Mark = purchaseOrder.PurchaseOrderDetails.First().Mark,
                    IsInternalTransactions = Utility.IsInternalTransactions(purchaseOrder.RelateCodeType.HasValue ? (int)purchaseOrder.RelateCodeType : 0)
                };
                if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                {
                    debt.RebateNo = purchaseOrder.RelateCode;
                }
                string PurchaseCode = "";
                if (!string.IsNullOrEmpty(purchaseOrder?.OriginCode))
                {
                    if (purchaseOrder?.OriginCode.Length > 1000)
                    {
                        PurchaseCode = purchaseOrder?.OriginCode.Substring(0, 999);
                    }
                    else
                    {
                        PurchaseCode = purchaseOrder?.OriginCode;
                    }
                }
                debt.PurchaseCode = PurchaseCode;
                if (debt.Value == 0)
                {
                    //throw new Exception("操作失败：金额为0不生成应付");
                    debt.AbatedStatus = AbatedStatusEnum.Abated;
                }

                if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
                {
                    if (purchaseOrder.TotalRebateAmountModify.HasValue)
                    {
                        debt.Value = purchaseOrder.TotalRebateAmountModify.Value;
                    }  
                    debt.RMBAmount = purchaseOrder.PurchaseOrderDetails.Sum(p => Math.Abs(p.Quantity) * p.Cost);
                }
                else
                {
                    debt.RMBAmount = debt.Value;
                }
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = debt.BillCode.Split('-')[0],
                    BillType = "DPP",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    if (debt.Value > 0)
                    {
                        var debtDetail = new DebtDetail
                        {
                            AccountPeriodType = (int)AccountPeriodTypeEnum.StoreIn,
                            Code = outPut.Codes.First(),
                            CreatedBy = purchaseOrder.CreatedBy ?? "none",
                            DebtId = debt.Id,
                            ProbablyPayTime = DateTime.Now,
                            PurchaseCode = purchaseOrder.Code,
                            Status = DebtDetailStatusEnum.WaitExecute,
                            Value = debt.Value,
                            Id = Guid.NewGuid(),

                        };
                        debt.DebtDetails.Add(debtDetail);
                    }
                    var kingdeeDebts = new List<KingdeeDebt>();
                    await InitKingdeeDebtAsync(purchaseOrder, productNameInfoOutputAll, debt, kingdeeDebts);
                    var requestBody = JsonConvert.SerializeObject(input);
                    var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, input.BusinessSubType, requestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await base.CreateDebt(debt);
                        await _debtRepository.RepaireDebtDiff(new List<Guid> { debt.Id });
                        return BaseResponseData<int>.Success("生成应付成功");
                    }
                    else
                    {
                        throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }
                else
                {
                    throw new Exception(outPut.Msg);
                }
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅经销购货修订生成应付出错");
            }
        }

        private async Task InitKingdeeDebtAsync(PurchaseOutPut purchaseOrder, List<ProductNameInfoOutput> productInfo, DebtDto debt, List<KingdeeDebt> kingdeeDebts)
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = debt.OrderNo,
                jfzx_creator = debt.CreatedBy ?? "none",
                currency_number = debt.CoinCode ?? "CNY",
                jfzx_rebate = string.IsNullOrEmpty(purchaseOrder.RelateCode) ? false : purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"),
                pricetaxtotal4 = debt.Value,
            };
            if (debt.CoinCode != "CNY")
            {
                var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                {
                    Effectdate = DateTime.Now,
                    OrgcurName = debt.CoinName
                });
                if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                {
                    throw new Exception("操作失败：未获取到汇率");
                }
                kingdeeDebt.exchangerate = exchange.Data.Excval;
            }
            var orderDetailOutputs = new List<ReviseDetailToKD>();
            kingdeeDebt.jfzx_rebate = purchaseOrder.RebateType.HasValue;
            var orderAmendments = new List<ApFinApPurchaseOrderAmendmentModel>();

            foreach (var detail in purchaseOrder.PurchaseOrderDetails)
            {
                if (detail.Quantity > 0)
                {

                    if (detail.ReviseProductDetails != null && detail.ReviseProductDetails.Any())
                    {
                        var reviseQuantity = detail.ReviseProductDetails.Sum(x => x.Quantity);

                        foreach (var reviseDetail in detail.ReviseProductDetails)
                        {
                            var revise = new ReviseDetailToKD();
                            if (reviseDetail.Type == LocationTypeEnum.TempSale || reviseDetail.Type == LocationTypeEnum.Sale)
                            {
                                revise.jfzx_revisiontype = "soldout";
                                revise.jfzx_rebatecustomerid = string.IsNullOrEmpty(reviseDetail.CustomerId) ? "" : reviseDetail.CustomerId.ToString().ToUpper();
                            }
                            else if (reviseDetail.Type == LocationTypeEnum.Temp)
                            {
                                revise.jfzx_revisiontype = "temporarystorage";
                                revise.jfzx_rebatecustomerid = string.IsNullOrEmpty(reviseDetail.CustomerId) ? "" : reviseDetail.CustomerId.ToString().ToUpper();
                            }
                            else
                            {
                                revise.jfzx_revisiontype = "stock";
                            }
                            revise.Quantity = reviseDetail.Quantity;
                            revise.Cost = detail.Cost;
                            revise.OriginCost = detail.OriginCost;
                            revise.RelateId = detail.RelateId;
                            revise.TaxRate = detail.TaxRate;
                            revise.ProductId = detail.Product.Id;
                            revise.ProductNameId = detail.Product.NameId;
                            orderDetailOutputs.Add(revise);

                        }
                        if (detail.Quantity - reviseQuantity > 0)//如果明细中的数量比修订的数量多这需要补一条数据
                        {
                            var revise = new ReviseDetailToKD();
                            revise.jfzx_revisiontype = "stock";
                            revise.Quantity = detail.Quantity - reviseQuantity;
                            revise.Cost = detail.Cost;
                            revise.OriginCost = detail.OriginCost;
                            revise.RelateId = detail.RelateId;
                            revise.TaxRate = detail.TaxRate;
                            revise.ProductId = detail.Product.Id;
                            revise.ProductNameId = detail.Product.NameId;
                            orderDetailOutputs.Add(revise);
                        }
                    }
                    else
                    {
                        throw new Exception($"操作失败，原因：{detail.Product.Name}修订明细中没有数据");
                    }

                }
            }
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();

            var noReviseProductDetails = purchaseOrder.PurchaseOrderDetails.Where(p => p.ReviseProductDetails == null || !p.ReviseProductDetails.Any()).ToList();

            var amount = 0m;
            orderDetailOutputs.GroupBy(a => new { a.ProductId, a.ProductNameId, a.Cost, a.TaxRate, a.jfzx_revisiontype, a.RelateId, a.OriginCost, a.jfzx_rebatecustomerid }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                var pricetax = t.Key.Cost;
                var remrmbCost = 0M;
                if (noReviseProductDetails != null && noReviseProductDetails.Any())
                {
                    var noReviseProductDetail = noReviseProductDetails.Where(p => p.Product.Id == t.Key.ProductId && p.RelateId == t.Key.RelateId).FirstOrDefault();
                    if (noReviseProductDetail != null)
                    {
                        if (purchaseOrder.TradeType == TradeTypeEnums.External)
                        {
                            pricetax = t.Key.OriginCost.Value + noReviseProductDetail.OriginCost.Value;
                        }
                        else
                        {
                            pricetax = t.Key.Cost + noReviseProductDetail.Cost;
                        }
                        remrmbCost = t.Key.Cost + noReviseProductDetail.Cost;
                    }

                }

                //d.taxrate = t.Key.TaxRate;
                d.taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? t.Key.TaxRate : 0;
                d.quantity = pricetax < 0 ? Math.Abs(t.Sum(b => b.Quantity)) * -1 : Math.Abs(t.Sum(b => b.Quantity));
                d.pricetax = Math.Abs(pricetax);
                d.e_amountbaseMany = remrmbCost * Math.Abs(d.quantity);
                //if (kingdeeDebt.pricetaxtotal4 < 0)
                //{
                //    d.e_amountbaseMany = - Math.Abs(d.e_amountbaseMany);
                //}
                d.jfzx_rebatecustomerid = string.IsNullOrEmpty(t.Key.jfzx_rebatecustomerid) ? "" : t.Key.jfzx_rebatecustomerid.ToUpper();
                d.jfzx_project_number = debt.ProjectCode;
                var thisProductInfo = productInfo.FirstOrDefault(e => e.productNameId == t.Key.ProductNameId);
                if (thisProductInfo != null)
                {
                    if (thisProductInfo.classificationNewGuid.HasValue)
                    {
                        d.material_number1 = thisProductInfo.classificationNewGuid.Value.ToString();
                    }
                    else
                    {
                        if (thisProductInfo.classificationGuid.HasValue)
                        {
                            d.material_number1 = thisProductInfo.classificationGuid.Value.ToString();
                        }
                    }
                }
                else
                {
                    throw new Exception($"操作失败，原因：{t.Key.ProductNameId}没有找对应的品名");
                }
                d.jfzx_revisiontype = t.Key.jfzx_revisiontype;
                if (purchaseOrder.RebateType.HasValue)
                {
                    d.jfzx_rebateType = (int)purchaseOrder.RebateType;
                }

                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + t.Key.TaxRate / 100.00M)), 20);
                amount += d.price2 * d.quantity;

                var reviseProductDetails = purchaseOrder.PurchaseOrderDetails?.FirstOrDefault(p => p.ReviseProductDetails != null && p.ReviseProductDetails.Any())?.ReviseProductDetails;
                if (reviseProductDetails != null)
                {
                    foreach (var item in reviseProductDetails)
                    {
                        if (item.Type == LocationTypeEnum.Rentaloutbound && !string.IsNullOrEmpty(item.ObjectLocation))
                        {
                            //应付不含税单价
                            var _price = Math.Round((t.Key.Cost / (1 + t.Key.TaxRate / 100.00M)), 20);
                            orderAmendments.Add(new()
                            {
                                equipmentCode = item.ObjectLocation,
                                revisedTotalAmountExclTax = Math.Round(item.Quantity * _price, 2)
                            });
                        }
                    }
                }
            });

            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);

            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebt.purchaseOrderAmendmentModels = orderAmendments;
            kingdeeDebts.Add(kingdeeDebt);
        }
    }
}
