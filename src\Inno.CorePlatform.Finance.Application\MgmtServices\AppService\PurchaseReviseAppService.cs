﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npoi.Mapper;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 寄售购货修订
    /// </summary>
    public class PurchaseReviseAppService : BaseAppService, IPurchaseReviseAppService
    {
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IPurchasePayPlanRepository _purchasePayPlanRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IAbtmentService _abtmentService;
        private readonly ISubLogService _subLogService;
        private readonly FinanceDbContext _db;
        public PurchaseReviseAppService(
            IPaymentRepository paymentRepository,
            IPurchasePayPlanRepository purchasePayPlanRepository,
            ISubLogService subLogRepository,
            ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            IPurchaseApiClient purchaseApiClient,
            IBDSApiClient bDSApiClient,
            IUnitOfWork _unitOfWork,
            IKingdeeApiClient kingdeeApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IDomainEventDispatcher? deDispatcher,
            IExchangeRateService exchangeRateService,
            ICodeGenClient codeGenClient,
            FinanceDbContext db,
            ISubLogService subLogService,
            IAppServiceContextAccessor? contextAccessor,
            IAbtmentService abtmentService) :
            base(creditRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._unitOfWork = _unitOfWork;
            this._purchaseApiClient = purchaseApiClient; 
            this._bDSApiClient = bDSApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._db = db;
            this._exchangeRateService = exchangeRateService;
            this._codeGenClient = codeGenClient;
            this._abtmentService = abtmentService;
            this._appServiceContextAccessor = contextAccessor;
            this._subLogService = subLogService;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            //return await AutoAbatementByAdvance("IVDBD-DECQ-ADV-2506-000042", "IVDBD-DECQ-PUA-2506-000077");
            var purchaseOrder = await _purchaseApiClient.GetByIdAsync(input.BusinessId.Value);
            if (purchaseOrder == null || purchaseOrder.PurchaseOrderDetails == null || !purchaseOrder.PurchaseOrderDetails.Any())
            {
                throw new Exception("未获取到单据或单据明细为空");
            }
            var check = (await _db.Debts.Where(p => p.BillCode == purchaseOrder.Code).CountAsync()) > 0;
            if (check)
            {
                if (input.IsAutoBill.HasValue && input.IsAutoBill.Value)
                {
                    return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                }
                else
                {
                    throw new Exception("该单据已生成过应付");
                }
            }
            var productIds = purchaseOrder.PurchaseOrderDetails.Select(p => p.Product.NameId.Value).Distinct().ToList();
            var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
            int limit = 500;//一次最多500条

            var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
            productNameInfoOutputAll.AddRange(productInfo);
            int totalPages = (int)Math.Ceiling((double)total / limit);
            if (totalPages > 1)
            {
                for (int i = 2; i <= totalPages; i++)
                {
                    (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                    productNameInfoOutputAll.AddRange(productInfo);
                }
            }
            var productDetail = purchaseOrder?.PurchaseOrderDetails.FirstOrDefault(x => x.ReviseProductDetails.Any()).ReviseProductDetails?.First();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(new List<Guid>() { Guid.Parse(purchaseOrder.Project.Id) });
            string BusinessDeptFullPath = string.Empty;
            string BusinessDeptFullName = string.Empty;
            string BusinessDeptId = string.Empty;
            var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(purchaseOrder.Consignee.Id.ToString()));
            var agents = purchaseOrder.Agent != null && purchaseOrder.Agent.Id != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { purchaseOrder.Agent.Id.Value }) : null;
            var debt = new DebtDto
            {
                AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                BillCode = purchaseOrder.Code,
                BillDate = billDate, //purchaseOrder.BillDate.Value.Date,
                CreatedBy = purchaseOrder.CreatedBy ?? "none",
                CreatedTime = DateTime.Now,
                Id = Guid.NewGuid(),
                Value = Math.Round(purchaseOrder.PurchaseOrderDetails.Sum(p => Math.Abs(p.Quantity) * p.Cost), 2),
                AgentId = purchaseOrder.Agent.Id,
                AgentName = purchaseOrder.Agent.Name,
                IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                CompanyId = purchaseOrder.Consignee.Id,
                CompanyName = purchaseOrder.Consignee.Name,
                DebtType = DebtTypeEnum.revise,
                NameCode = purchaseOrder.Consignee.NameCode,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                PurchaseContactNo = purchaseOrder?.Contract?.Code,
                ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                PurchaseCode = purchaseOrder?.OriginCode,
                OrderNo = purchaseOrder.Code,
                RelateCode = purchaseOrder.RelateCode,
                DebtDetails = new List<DebtDetail>(),
                ProjectId = Guid.Parse(purchaseOrder.Project.Id),
                ProjectCode = projectInfo.FirstOrDefault()?.Code,
                ProjectName = projectInfo.FirstOrDefault()?.Name,
                CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                CustomerName = productDetail?.CustomerName,
                CustomerId = productDetail == null || string.IsNullOrEmpty(productDetail.CustomerId) ? null : Guid.Parse(productDetail.CustomerId),
                ReviseRange = input.ReviseRange,
                OriginOrderNo = purchaseOrder?.SaleOrder?.code,
                Mark = purchaseOrder.PurchaseOrderDetails.First().Mark,
                IsInternalTransactions = Utility.IsInternalTransactions(purchaseOrder.RelateCodeType.HasValue ? (int)purchaseOrder.RelateCodeType : 0)
            };
            if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
            {
                debt.RebateNo = purchaseOrder.RelateCode;
            }
            if (debt.Value == 0)
            {
                debt.AbatedStatus = AbatedStatusEnum.Abated;
            }
            //902=服务采购修订
            if (purchaseOrder.ServicePurchaseRevise.HasValue && purchaseOrder.ServicePurchaseRevise.Value == 902)
            {
                debt.DebtType = DebtTypeEnum.servicefeeRevise;
            }
            if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
            {
                if (purchaseOrder.TotalRebateAmountModify.HasValue)
                {
                    debt.Value = purchaseOrder.TotalRebateAmountModify.Value;
                }
                var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                {
                    Effectdate = DateTime.Now,
                    OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                });
                if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                {
                    throw new Exception("操作失败：未获取到汇率");
                }
                debt.RMBAmount = Math.Round(debt.Value, 2) * exchange.Data.Excval;
            }
            else
            {
                debt.RMBAmount = debt.Value;
            }
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { purchaseOrder.Consignee.Id.ToString() }
            })).FirstOrDefault();

            #region 只生成入库账期
            if (input.ReviseRange != ReviseRangeEnum.CostAndPrice && debt.Value > 0)
            {
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = debt.BillCode.Split('-')[0],
                    BillType = "DPP",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    var debtDetail = new DebtDetail
                    {
                        AccountPeriodType = (int)AccountPeriodTypeEnum.StoreIn,
                        Code = outPut.Codes.First(),
                        CreatedBy = purchaseOrder.CreatedBy ?? "none",
                        DebtId = debt.Id,
                        ProbablyPayTime = DateTime.Now,
                        PurchaseCode = purchaseOrder.Code,
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = debt.Value,
                        Id = Guid.NewGuid(),
                    };
                    debt.DebtDetails.Add(debtDetail);
                }
            }
            #endregion

            #region 根据采购付款计划写账期类型 
            if (debt.Value > 0 && (input.ReviseRange == ReviseRangeEnum.CostAndPrice))
            {
                var credits = new List<CreditPo>();
                if (!string.IsNullOrEmpty(purchaseOrder.RelateCode))
                {
                    var projectId = Guid.Parse(purchaseOrder.Project.Id);
                    var serviceId = (purchaseOrder.Service == null ? null : purchaseOrder.Service.Id);
                    credits = await _db.Credits.Where(p =>
                    p.ReviseRange == ReviseRangeEnum.CostAndPrice &&
                    p.OrderNo == purchaseOrder.RelateCode &&
                    p.ServiceId == serviceId &&
                    p.ProjectId == projectId && p.Value > 0).ToListAsync();
                    if (credits == null || !credits.Any())
                    {
                        throw new Exception("修订应收还没有生成，请稍后重试");
                    }
                }
                if (purchaseOrder.PaymentPlans != null && purchaseOrder.PaymentPlans.Any())
                {
                    debt.DebtDetails = new List<DebtDetail>();
                    var debtDetailAll = new List<DebtDetail>();
                    foreach (var plan in purchaseOrder.PaymentPlans)
                    {
                        var debtDetail = new DebtDetail
                        {
                            AccountPeriodType = plan.DPOType,
                            Value = plan.APAmount,
                            CostDiscount = purchaseOrder.PurchaseOrderDetails.First().CostDiscount ?? 0,
                            FinanceDiscount = purchaseOrder.PurchaseOrderDetails.First().FinanceDiscount ?? 0,
                            DistributionDiscount = purchaseOrder.PurchaseOrderDetails.First().DistributionDiscount ?? 0,
                            SpdDiscount = purchaseOrder.PurchaseOrderDetails.First().SpdDiscount ?? 0,
                            TaxDiscount = purchaseOrder.PurchaseOrderDetails.First().TaxDiscount ?? 0,
                            ProbablyPayTime = plan.DPOType == 2 ? DateTime.Now.AddDays(plan.DPO) : null,
                            AccountPeriodDays = plan.DPO
                        };
                        debtDetailAll.Add(debtDetail);
                    }
                    var debtDetailGroups = debtDetailAll.GroupBy(p => new { p.AccountPeriodType, p.CostDiscount, p.FinanceDiscount, p.DistributionDiscount, p.SpdDiscount, p.TaxDiscount, p.AccountPeriodDays }).ToList();
                    foreach (var g in debtDetailGroups)
                    {
                        var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = debt.BillCode.Split('-')[0],
                            BillType = "DPP",
                            SysMonth = companyInfo.sysMonth,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        var debtDetail = new DebtDetail
                        {
                            Id = Guid.NewGuid(),
                            AccountPeriodType = g.Key.AccountPeriodType,
                            Code = outPut.Codes.First(),
                            CreatedBy = purchaseOrder.CreatedBy ?? "none",
                            CreatedTime = DateTime.Now,
                            DebtId = debt.Id,
                            PurchaseCode = purchaseOrder.Code,
                            Status = DebtDetailStatusEnum.WaitExecute,
                            Value = g.Sum(p => p.Value),
                            OrderNo = purchaseOrder.RelateCode,
                            CreditId = credits != null && credits.Any() ? credits.First().Id : null,
                            CostDiscount = g.Key.CostDiscount ?? 0,
                            FinanceDiscount = g.Key.FinanceDiscount ?? 0,
                            DistributionDiscount = g.Key.DistributionDiscount ?? 0,
                            SpdDiscount = g.Key.SpdDiscount ?? 0,
                            TaxDiscount = g.Key.TaxDiscount ?? 0,
                            AccountPeriodDays = g.Key.AccountPeriodDays,
                        };
                        if (debtDetail.CostDiscount.HasValue && debtDetail.CostDiscount != 0)
                        {
                            debtDetail.OriginValue = debtDetail.Value / debtDetail.CostDiscount * 100;
                        }
                        if (debtDetail.AccountPeriodType == 2)
                        {
                            debtDetail.ProbablyPayTime = g.Max(p => p.ProbablyPayTime);
                        }
                        //订单修订的修订应付，如果上游给的有账期的，且账期里面有入库账期的
                        if (debtDetail.AccountPeriodType == 1)
                        {
                            debtDetail.ProbablyPayTime = DateTime.Now;
                        }
                        debt.DebtDetails.Add(debtDetail);
                    };
                }
                else
                {
                    throw new Exception("操作失败，原因：没有付款计划数据");
                }
            }
            #endregion 
            var kingdeeDebts = new List<KingdeeDebt>();
            InitKingdeeDebt(purchaseOrder, productNameInfoOutputAll, debt, kingdeeDebts);
            var requestBody = JsonConvert.SerializeObject(input);
            var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, input.BusinessSubType, requestBody);
            if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
            {
                await base.CreateDebt(debt);
                await _debtRepository.RepaireDebtDiff(new List<Guid> { debt.Id });

                //新增提前垫资逻辑，relateCodeType = 18
                if (purchaseOrder.RelateCodeType == CorePlatform.Common.CompetenceCenter.Enums.RelateCodeTypeEnums.AdvancePaymentRevise)
                {
                    var autoRet = await AutoAbatementByAdvance(purchaseOrder.RelateCode, purchaseOrder.OriginCode, debt);
                    if (autoRet.Code == CodeStatusEnum.Success)
                    {
                        return autoRet;
                    }
                    else
                    {
                        throw new Exception(autoRet.Message);
                    }
                }
                return BaseResponseData<int>.Success("生成应付成功");
            }
            else
            {
                throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
            }
        }

        private static void InitKingdeeDebt(PurchaseOutPut purchaseOrder, List<ProductNameInfoOutput> productInfo, DebtDto debt, List<KingdeeDebt> kingdeeDebts)
        {
            #region 包装金蝶应付参数
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = debt.OrderNo,
                jfzx_creator = debt.CreatedBy ?? "none",
                currency_number = debt.CoinCode ?? "CNY",
                jfzx_rebate = string.IsNullOrEmpty(purchaseOrder.RelateCode) ? false : purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"),
                pricetaxtotal4 = debt.Value,
            };

            kingdeeDebt.jfzx_rebate = purchaseOrder.RebateType.HasValue;
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();

            var amount = 0m;
            purchaseOrder.PurchaseOrderDetails.GroupBy(a => new { a.Product.NameId, a.Cost, a.TaxRate }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                //d.taxrate = t.Key.TaxRate;
                d.taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? t.Key.TaxRate : 0;
                d.quantity = t.Key.Cost < 0 ? Math.Abs(t.Sum(b => b.Quantity)) * -1 : t.Sum(b => b.Quantity);
                d.pricetax = Math.Abs(t.Key.Cost);
                d.jfzx_project_number = debt.ProjectCode;
                d.jfzx_rebatecustomerid = debt.CustomerId?.ToString().ToUpper();
                var thisProductInfo = productInfo.FirstOrDefault(e => e.productNameId == t.Key.NameId);
                if (thisProductInfo != null)
                {
                    if (thisProductInfo.classificationNewGuid.HasValue)
                    {
                        d.material_number1 = thisProductInfo.classificationNewGuid.Value.ToString();
                    }
                    else
                    {
                        if (thisProductInfo.classificationGuid.HasValue)
                        {
                            d.material_number1 = thisProductInfo.classificationGuid.Value.ToString();
                        }
                    }
                }
                else
                {
                    throw new Exception("操作失败：没有找到对应的产品Id:" + t.Key.NameId);
                }


                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + t.Key.TaxRate / 100.00M)), 20);

                amount += d.price2 * d.quantity;
                if (purchaseOrder.RebateType.HasValue)
                {
                    d.jfzx_rebateType = (int)purchaseOrder.RebateType;
                }

                kingdeeDebtDetails.Add(d);


            });


            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);

            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);
            #endregion
        }

        /// <summary>
        /// 提前垫资自动冲销
        /// </summary>
        /// <param name="advancePaymentBillCode">提前垫资单号</param>
        /// <param name="purchaseCode">采购订单号</param>
        /// <param name="negativeDebt">负数应付</param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> AutoAbatementByAdvance(string advancePaymentBillCode, string purchaseCode, DebtDto negativeDebt)
        {
            var p = new { advancePaymentBillCode, negativeDebt };
            var jsonStr = JsonConvert.SerializeObject(p);
            try
            {
                var advancePaymentItem = await _db.AdvancePaymentItem.AsNoTracking().Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.BillCode == advancePaymentBillCode);
                if (advancePaymentItem == null)
                {
                    var ret = BaseResponseData<int>.Failed(500, $"提前垫资负数应付自动冲销失败：根据RelateCode:{advancePaymentBillCode}未获取到提前垫资单");
                    jsonStr = JsonConvert.SerializeObject(ret);
                    return ret;
                }
                var LstInput = new List<GenerateAbtForDebtInput>();
                var debtBillNos = advancePaymentItem.AdvancePaymentDebtDetails.Select(x => x.DebtBillNo).ToList();
                //所有的应付
                var debts = await _db.Debts.Where(x => !string.IsNullOrEmpty(x.BillCode) && debtBillNos.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                //负数应付
                if (negativeDebt == null)
                {
                    var ret = BaseResponseData<int>.Failed(500, $"提前垫资负数应付自动冲销失败：未获取到负数应付单");
                    jsonStr = JsonConvert.SerializeObject(ret);
                    return ret;
                }
                foreach (var debt in debts)
                {
                    var abtValue = advancePaymentItem.AdvancePaymentDebtDetails.Where(x => x.DebtBillNo == debt.BillCode && x.PurchaseCode == purchaseCode).Sum(x => x.AdvanceTaxAmount);
                    if (abtValue.HasValue && abtValue > 0)
                    {
                        LstInput.Add(new GenerateAbtForDebtInput
                        {
                            BillCode = debt.BillCode ??= "",
                            BillValue = debt.Value,
                            DebtId = debt.Id,
                            Value = abtValue.Value
                        });
                    }
                }
                var cnt = await _abtmentService.GenerateAbtForDebtAsync(negativeDebt.Id, LstInput, "AutoAbatementByAdvance", true);
                if (cnt > 0)
                {
                    var ret = BaseResponseData<int>.Success("提前垫资负数应付自动冲销成功");
                    jsonStr = JsonConvert.SerializeObject(ret);
                    return ret;
                }
                else
                {
                    var ret = BaseResponseData<int>.Success("提前垫资负数应付自动冲销成功，但影响行数为0");
                    jsonStr = JsonConvert.SerializeObject(ret);
                    return ret;
                }
            }
            catch (Exception ex)
            {
                var ret = BaseResponseData<int>.Failed(500, "提前垫资负数应付自动冲销失败：" + ex.Message);
                jsonStr = JsonConvert.SerializeObject(ret);
                return ret;
            }
        }
    }
}
