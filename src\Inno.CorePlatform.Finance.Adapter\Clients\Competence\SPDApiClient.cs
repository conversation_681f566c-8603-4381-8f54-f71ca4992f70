﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RestSharp;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class SPDApiClient : ISPDApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SPDApiClient> _logger;
        private readonly ISubLogService _subLogRepository;
        private readonly IUnitOfWork _unitOfWork;
        private DaprClient _daprClient;
        public SPDApiClient(
         IConfiguration configuration,
         HttpClient httpClient,
         ISubLogService subLogRepository,
         IUnitOfWork unitOfWork,
         DaprClient daprClient,
        ILogger<SPDApiClient> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _unitOfWork = unitOfWork;
            _daprClient = daprClient;
            _subLogRepository = subLogRepository;
        }
        public async Task<SPDResponse> ReceiveInvoice(InoviceSpdInput input)
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_configuration["SPDSetting:Host"]}/rest/invoiceAbut/receiveInvoice/{_configuration["SPDSetting:Token"]}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Idempotency-Key", DateTime.Now.Ticks);
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                var jsonRet = string.Empty;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, jsonStr, "none", "认款到发票-推送SPD", true);
                    var ret = await restClient.PostAsync<SPDResponse>(request);
                    if (ret != null && ret.code != 0)
                    {
                        jsonRet = JsonConvert.SerializeObject(ret);
                        //不成功
                        throw new Exception(ret.msg);
                    }
                    return ret;
                }
                catch (Exception ex)
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, "请求参数" + jsonStr + "。返回参数：" + jsonRet, "none", "推送发票-推送SPD-错误记录", true);
                    // 写入重试队列
                    await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    {
                        AppId = "finance-webapi",
                        MsgBody = jsonStr,
                        Topic = "finance-finance-receiveInvoice",
                        FailReason = "【商务平台SPD】" + ex.Message,
                        ExceptionMessage = JsonConvert.SerializeObject(ex),
                        CallBackMethodRoute = "/api/SellSub/ReceiveInvoiceRe"  //重试的回调方法路由 
                    });
                    return new SPDResponse()
                    {
                        msg = ex.Message,
                        code = 9999
                    };
                }
            }
        }

        public async Task<SPDResponse> SynReceive(RecognizeReceiveSpdInput input)
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_configuration["SPDSetting:Host"]}/rest/receive/synReceive/{_configuration["SPDSetting:Token"]}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Idempotency-Key", DateTime.Now.Ticks);
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                var jsonRet = string.Empty;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, jsonStr, "none", "认款到发票-推送SPD", true);
                    var ret = await restClient.PostAsync<SPDResponse>(request);
                    if (ret != null && ret.code != 0)
                    {
                        jsonRet = JsonConvert.SerializeObject(ret);
                        //不成功
                        throw new Exception(ret.msg);
                    }
                    return ret;
                }
                catch (Exception ex)
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, "请求参数" + jsonStr + "。返回参数：" + jsonRet, "none", "认款到发票-推送SPD-错误记录", true);
                    // 写入重试队列
                    await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    {
                        AppId = "finance-webapi",
                        MsgBody = jsonStr,
                        Topic = "finance-finance-synReceive",
                        FailReason = "【商务平台SPD】" + ex.Message,
                        ExceptionMessage = JsonConvert.SerializeObject(ex),
                        CallBackMethodRoute = "/api/SellSub/SynReceiveRe"  //重试的回调方法路由 
                    });
                    return new SPDResponse()
                    {
                        msg = ex.Message,
                        code = 9999
                    };
                }
            }
        }


        public async Task<SPDResponse> synReceiveNoInvoice(RecognizeReceiveSpdInitInput input)
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_configuration["SPDSetting:Host"]}/rest/receive/synReceiveNoInvoice/{_configuration["SPDSetting:Token"]}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Idempotency-Key", DateTime.Now.Ticks);
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                var jsonRet = string.Empty;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, jsonStr, "none", "认款到初始应收-推送SPD", true);
                    var ret = await restClient.PostAsync<SPDResponse>(request);
                    if (ret != null && ret.code != 0)
                    {
                        jsonRet = JsonConvert.SerializeObject(ret);
                        //不成功
                        throw new Exception(ret.msg);
                    }
                    return ret;
                }
                catch (Exception ex)
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, "请求参数" + jsonStr + "。返回参数：" + jsonRet, "none", "认款到初始应收-推送SPD-错误记录", true);
                    // 写入重试队列
                    await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    {
                        AppId = "finance-webapi",
                        MsgBody = jsonStr,
                        Topic = "finance-finance-synReceiveNoInvoice",
                        FailReason = "【商务平台SPD】" + ex.Message,
                        ExceptionMessage = JsonConvert.SerializeObject(ex),
                        CallBackMethodRoute = "/api/SellSub/SynReceiveNoInvoiceRe"  //重试的回调方法路由 
                    });
                    return new SPDResponse()
                    {
                        msg = ex.Message,
                        code = 9999
                    };
                }
            }
        }

        public async Task<SPDResponse> synReceiveAndInvoice(RecognizeReceivePushBusinessInput input)
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_configuration["SPDSetting:Host"]}/rest/receive/synReceiveAndInvoice/{_configuration["SPDSetting:Token"]}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Idempotency-Key", DateTime.Now.Ticks);
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                var jsonRet = string.Empty;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, jsonStr, "none", "认款数据-推送SPD", true);
                    var ret = await restClient.PostAsync<SPDResponse>(request);
                    if (ret != null && ret.code != 0)
                    {
                        jsonRet = JsonConvert.SerializeObject(ret);
                        //不成功
                        throw new Exception(ret.msg);
                    }
                    return ret;
                }
                catch (Exception ex)
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, "请求参数" + jsonStr + "。返回参数：" + jsonRet, "none", "认款数据-推送SPD-错误记录", true);
                    // 写入重试队列
                    await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                    {
                        AppId = "finance-webapi",
                        MsgBody = jsonStr,
                        Topic = "finance-finance-synReceiveAndInvoice",
                        FailReason = "【商务平台SPD】" + ex.Message,
                        ExceptionMessage = JsonConvert.SerializeObject(ex),
                        CallBackMethodRoute = "/api/SellSub/SynReceiveAndInvoiceRe"  //重试的回调方法路由 
                    });
                    return new SPDResponse()
                    {
                        msg = ex.Message,
                        code = -1
                    };
                }
            }
        }

        /// <summary>
        /// 2.2.4.查询发票号是否存在
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SPDResponse> selectExistInvoiceNumList(List<string> invoiceNumList)
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_configuration["SPDSetting:Host"]}/rest/invoiceAbut/selectExistInvoiceNumList/{_configuration["SPDSetting:Token"]}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("Idempotency-Key", DateTime.Now.Ticks);
                var para = invoiceNumList;
                var jsonStr = JsonConvert.SerializeObject(para);
                var jsonRet = string.Empty;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.SPD, jsonStr, "none", "查询发票号是否存在-推送SPD", true);
                    var ret = await restClient.PostAsync<SPDResponse>(request);
                    if (ret != null && ret.code != 0)
                    {
                        jsonRet = JsonConvert.SerializeObject(ret);
                        //不成功
                        throw new Exception(ret.msg);
                    }
                    return ret;
                }
                catch (Exception ex)
                { 
                    return new SPDResponse()
                    {
                        msg = ex.Message,
                        code = -1
                    };
                }
            }
        }

        public async Task CreateSubLog(SubLogSourceEnum source, string content, string userName, string operate, bool isCommit = true)
        {
            await _subLogRepository.LogAsync(source.ToString(), content, operate);
        }
    }
}
