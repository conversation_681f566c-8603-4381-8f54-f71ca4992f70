﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RecognizeReceiveQueryController : BaseController
    {
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IRecognizeReceiveQueryService _recognizeReceiveQueryService;
        private readonly IPCApiClient _pcApiClient;
        private readonly ISellApiClient _sellApiClient;
        private readonly ILogger<RecognizeReceiveQueryController> _logger;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly ISPDApiClient _SPDApiClient;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IRecognizeReceiveAppService _recognizeReceiveService;
        private readonly ICreditQueryService _creditQueryService;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        public RecognizeReceiveQueryController(ILogger<RecognizeReceiveQueryController> logger,
            IRecognizeReceiveQueryService recognizeReceiveQueryService,
            IPCApiClient pcApiClient,
            IBDSApiClient bDSApiClient,
            ISellApiClient sellApiClient,
            ISPDApiClient SPDApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IFileGatewayClient fileGatewayClient,
            IInvoiceQueryService invoiceQueryService,
            IRecognizeReceiveAppService recognizeReceiveService,
            ICreditQueryService creditQueryService,
            IAppServiceContextAccessor appServiceContextAccessor, ISubLogService subLog) : base(subLog)
        {
            _recognizeReceiveQueryService = recognizeReceiveQueryService;
            _logger = logger;
            _sellApiClient = sellApiClient;
            _pcApiClient = pcApiClient;
            _fileGatewayClient = fileGatewayClient;
            _bDSApiClient = bDSApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _SPDApiClient = SPDApiClient;
            _invoiceQueryService = invoiceQueryService;
            _recognizeReceiveService = recognizeReceiveService;
            _creditQueryService = creditQueryService;
            _appServiceContextAccessor = appServiceContextAccessor;
        }

        /// <summary>
        /// 获取认款明细
        /// </summary>
        /// <param name="query">认款单Id</param>
        /// <returns></returns>
        [HttpPost("getdetail")]
        public async Task<BaseResponseData<List<RecognizeReceiveDetailOutput>>> GetDetails([FromBody] QueryById query)
        {
            var result = await _recognizeReceiveQueryService.GetDetails(query.Id, query.Classify, query.Status);
            var res = new BaseResponseData<List<RecognizeReceiveDetailOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;

        }
        /// <summary>
        /// 获取认款明细
        /// </summary>
        /// <param name="id">认款单Id</param>
        /// <returns></returns>
        [HttpPost("getdetailInfo")]
        public async Task<BaseResponseData<RecognizeReceiveDetailOutput>> GetDetail(Guid id)
        {
            var result = await _recognizeReceiveQueryService.GetDetail(id);
            var res = new BaseResponseData<RecognizeReceiveDetailOutput>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;

        }
        /// <summary>
        /// 获取收款单
        /// </summary>
        /// <param name="input">认款单Id</param>
        /// <returns></returns>
        [HttpPost("getKdReceiveBills")]
        public async Task<BaseResponseData<List<RecognizeReceiveOutput>>> GetKdReceiveBills([FromBody] RecognizeReceiveOneInput input)
        {
            var res = new BaseResponseData<List<RecognizeReceiveOutput>>
            {
                Code = CodeStatusEnum.Success
            };
            if (!string.IsNullOrEmpty(input.company))
            {
                if (input.opt == 1) //可以认款的类型
                {
                    input.type = new List<string> {
                         ((int)RecognizeReceiveTypeEnum.SaleReturn).ToString(),
                         ((int)RecognizeReceiveTypeEnum.Prepayment).ToString(),
                         ((int)RecognizeReceiveTypeEnum.RefundPurchasePayment).ToString(),
                     };
                }
                var result = await _recognizeReceiveQueryService.GetKdReceiveBills(new RecognizeReceiveInput
                {
                    casRecbillCustomer = input.casRecbillCustomer,
                    company = new List<string> { input.company },
                    customer = input.customer,
                    opt = input.opt,
                    type = input.type
                });
                res.Data = result;
            }
            return res;
        }
        /// <summary>
        /// 获取收款单
        /// </summary>
        /// <param name="input">认款单Id</param>
        /// <returns></returns>
        [HttpPost("getReceiveBills")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveOutput>>> GetReceiveBills([FromBody] RecognizeReceiveInput input)
        {
            var strategryInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
            var strategys = await _pcApiClient.GetStrategyAsync(strategryInput);

            var companyNameCodes = new List<string>();
            if (strategys != null)
            {
                var rowStrategies = strategys.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    return new BaseResponseData<PageResponse<RecognizeReceiveOutput>>
                    {
                        Code = CodeStatusEnum.Success,
                        Data = new PageResponse<RecognizeReceiveOutput>
                        {
                            List = new List<RecognizeReceiveOutput>(),
                            Total = 0
                        },
                    };
                }
                foreach (var key in strategys.RowStrategies.Keys)
                {
                    if (key.ToLower() == "company")
                    {
                        if (!strategys.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var companyIds = strategys.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                            var daprCompanies = await _bDSApiClient.GetCompanyInfoAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                            {
                                ids = companyIds
                            });
                            companyNameCodes = daprCompanies.Select(p => p.nameCode).ToList();
                        }
                    }
                    if (key.ToLower() == "customer")
                    {
                        if (!strategys.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategys.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                            if (strategList.Count() > 0 && strategList.Any())
                            {
                                foreach (var customerId in strategList)
                                {
                                    input.casRecbillCustomer.Add(new QueryCasRecbillCustomerModel
                                    {
                                        customer = customerId
                                    });
                                }
                            }
                        }
                    }
                }
            }

            if (input.customers != null && input.customers.Any())
            {
                foreach (var customerId in input.customers)
                {
                    input.casRecbillCustomer = new List<QueryCasRecbillCustomerModel> {
                        new QueryCasRecbillCustomerModel
                        {
                            customer = customerId
                        }
                    };
                }
            }

            if (input.company == null || !input.company.Any())
            {
                input.company = companyNameCodes;
            }
            input.type = new List<string> {
                 "100",//销售回款
                 "101",//预收款
                 "102",//退采购付款
                 "108",//赔款
                 "109",//收回之前支付的押金
                 "110",//收回之前支付的投标保证金
                 "111",//收回之前支付的履约保证金
                 "998",//未知
                 "999",//其他
                 "127",//收回之前支付的海关保证金
                 "129",//收回之前支付的医院保证金
                 "124",//收到招标押金
                 "123",//收到保证金
                 "104",//代收款
                };
            if (input.classify == "-1") //选择全部
            {
                input.type.Add("129");//收回之前支付的医院保证金
                input.type.Add("127");//收回之前支付的海关保证金
                input.type.Add("124");//收到招标押金
                input.type.Add("123");//收到保证金
                input.type.Add("122");//保险理赔收入
                input.type.Add("121");//所得税退税
                input.type.Add("120");//出口退税
                input.type.Add("119");//税费返还
                input.type.Add("118");//个税返还
                input.type.Add("117");//即征即退
                input.type.Add("116");//政府补贴
                input.type.Add("115");//工会经费返还
                input.type.Add("104");//代收款
                input.type.Add("142");//销售现金折扣
                input.type.Add("145");//收货款(保证金转)
                //input.type.Add("203");//负数应收
                //input.type.Add("240");//退返利款
                //input.type.Add("204");//退预收款
                //input.type.Add("228");//退回收到的押金
                //input.type.Add("229");//退回收到的保证金
            }
            else if (!string.IsNullOrEmpty(input.classify))
            {
                input.type = new List<string> { input.classify };
            }
            var result = await _recognizeReceiveQueryService.GetKdReceiveBills(input);
            if (!string.IsNullOrEmpty(input.code))
            {
                result = result.Where(p => p.billno.Contains(input.code)).ToList();
            }
            var resultPage = result.OrderByDescending(p => p.payeedate).Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
            var res = new BaseResponseData<PageResponse<RecognizeReceiveOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<RecognizeReceiveOutput>
                {
                    List = resultPage,
                    Total = result.Count
                },
            };
            return res;
        }
        /// <summary>
        /// 获取认款清单
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("getlist")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveItemOutPut>>> GetListPages([FromBody] RecognizeReceiveItemInput input)
        {
            input.UserId = CurrentUser.Id;
            var result = await _recognizeReceiveQueryService.GetListPages(input);
            var res = new BaseResponseData<PageResponse<RecognizeReceiveItemOutPut>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<RecognizeReceiveItemOutPut>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }

        /// <summary>
        /// 获取可认款金额
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        [HttpPost("getRemainingRecognizableAmountByCodes")]
        public async Task<BaseResponseData<List<RemainingRecognizableAmountOutput>>> GetRemainingRecognizableAmountByCodes(List<string> codes)
        {
            return await _recognizeReceiveQueryService.GetRemainingRecognizableAmountByCodes(codes);
        }

        /// <summary>
        /// 获取认款页签数量
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("getTabCount")]
        public async Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCount([FromBody] RecognizeReceiveItemInput input)
        {
            input.UserId = CurrentUser.Id;
            return await _recognizeReceiveQueryService.GetTabCount(input);
        }
        /// <summary>
        /// 导出认款单
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("Export")]
        public async Task<IActionResult> Export([FromBody] RecognizeReceiveItemInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id;
                input.page = 1;
                input.limit = 9999999;
                if (input.sort == null || input.sort.Count == 0) 
                {
                    input.sort=new List<string>();
                    input.sort.Add("createdTime,desc");
                }
                var result = await _recognizeReceiveQueryService.GetListPages(input);
                var list = result.List;
                if (list == null || !list.Any())
                {
                    return StatusCode(500, $"未查到符合条件的数据");
                }
                var detailList = new List<RecognizeReceiveDetailOutput>();
                foreach (var item in list.Select(p => p.Classify).Distinct())
                {
                    detailList.AddRange(await _recognizeReceiveQueryService.GetDetailsByIdAndClassify(list.Where(p => p.Classify == item).Select(p => p.Id).ToList(), (int)item));
                }
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    // 4,5,24
                    var worksheet = package.Workbook.Worksheets.Add("认款单数据");
                    worksheet.Cells[1, 1].Value = "认款单号";
                    worksheet.Cells[1, 2].Value = "收款单号";
                    worksheet.Cells[1, 3].Value = "暂收款单号";
                    worksheet.Cells[1, 4].Value = "本次认款金额";
                    worksheet.Cells[1, 5].Value = "剩余可认款金额";
                    worksheet.Cells[1, 6].Value = "收款金额";
                    worksheet.Cells[1, 7].Value = "收款类型";
                    worksheet.Cells[1, 8].Value = "核算部门";
                    worksheet.Cells[1, 9].Value = "公司";
                    worksheet.Cells[1, 10].Value = "客户";
                    worksheet.Cells[1, 11].Value = "收款日期";
                    worksheet.Cells[1, 12].Value = "银行类型";
                    worksheet.Cells[1, 13].Value = "银行账户";
                    worksheet.Cells[1, 14].Value = "结算方式";
                    worksheet.Cells[1, 15].Value = "到期日";
                    worksheet.Cells[1, 16].Value = "项目名称";
                    worksheet.Cells[1, 17].Value = "项目编码";
                    worksheet.Cells[1, 18].Value = "状态";
                    worksheet.Cells[1, 19].Value = "类型";
                    worksheet.Cells[1, 20].Value = "转货款状态";
                    worksheet.Cells[1, 21].Value = "创建时间";
                    worksheet.Cells[1, 22].Value = "创建人";

                    worksheet.Cells[1, 23].Value = "发票/订单号/应收单号";
                    worksheet.Cells[1, 24].Value = "认款人";
                    worksheet.Cells[1, 25].Value = "实际客户";
                    worksheet.Cells[1, 26].Value = "终端客户";
                    worksheet.Cells[1, 27].Value = "认款类型";
                    worksheet.Cells[1, 28].Value = "认款金额";
                    worksheet.Cells[1, 29].Value = "认款日期";
                    worksheet.Cells[1, 30].Value = "业务单元";
                    worksheet.Cells[1, 31].Value = "是否跳号";
                    worksheet.Cells[1, 32].Value = "细分类型";
                    worksheet.Cells[1, 33].Value = "备注";
                    worksheet.Cells[1, 34].Value = "发票/订单号/应收单号日期";
                    worksheet.Cells[1, 35].Value = "交易时间";

                    int row = 2;
                    foreach (var item in list)
                    {
                        // 设置数值单元格格式
                        worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 28].Style.Numberformat.Format = "#,##0.00";
                        // 填装单元格数据
                        worksheet.Cells[row, 1].Value = item.Code;
                        worksheet.Cells[row, 2].Value = item.ReceiveCode;
                        worksheet.Cells[row, 3].Value = item.RelateCode;
                        worksheet.Cells[row, 4].Value = item.Value;
                        worksheet.Cells[row, 5].Value = item.RemainingRecognizableAmount;
                        worksheet.Cells[row, 6].Value = item.ReceiveValue;
                        worksheet.Cells[row, 7].Value = item.Type;
                        worksheet.Cells[row, 8].Value = item.BusinessDeptFullName;
                        worksheet.Cells[row, 9].Value = item.CompanyName;
                        worksheet.Cells[row, 10].Value = item.CustomerNme;
                        worksheet.Cells[row, 11].Value = Convert.ToDateTime(item.ReceiveDate).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 12].Value = item.BankName;
                        worksheet.Cells[row, 13].Value = item.BankNum;
                        worksheet.Cells[row, 14].Value = item.Settletype;
                        worksheet.Cells[row, 15].Value = item.DraftBillExpireDate == null ? "" : Convert.ToDateTime(item.DraftBillExpireDate).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 16].Value = item.ProjectName;
                        worksheet.Cells[row, 17].Value = item.ProjectCode;
                        worksheet.Cells[row, 18].Value = item.StatusDescription;
                        worksheet.Cells[row, 19].Value = item.ClassifyDescription;
                        worksheet.Cells[row, 20].Value = item.TransferStatusDescription;
                        worksheet.Cells[row, 21].Value = item.CreatedTime.DateTime.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 22].Value = item.CreatedBy;
                        // 详情
                        var details = detailList.Where(p => p.RecognizeReceiveItemId == item.Id).ToList();
                        // 详情
                        if (details != null && details.Any())
                        {
                            foreach (var detail in details)
                            {
                                worksheet.Cells[row, 23].Value = detail.Code;
                                worksheet.Cells[row, 24].Value = detail.CreatedBy;
                                worksheet.Cells[row, 25].Value = detail.CustomerName;
                                worksheet.Cells[row, 26].Value = detail.HospitalName;
                                worksheet.Cells[row, 27].Value = detail.TypeDescription;
                                worksheet.Cells[row, 28].Value = detail.Value;
                                worksheet.Cells[row, 29].Value = Convert.ToDateTime(detail.RecognizeDate).ToString("yyyy-MM-dd");
                                worksheet.Cells[row, 30].Value = detail.ServiceName;
                                worksheet.Cells[row, 31].Value = detail.IsSkip.HasValue && detail.IsSkip.Value ? "是" : "否";
                                worksheet.Cells[row, 32].Value = detail.ClassifyDescription;
                                worksheet.Cells[row, 33].Value = detail.Note;
                                worksheet.Cells[row, 34].Value = detail.CodeTime;
                                worksheet.Cells[row, 35].Value = item.BizTime == null ? "" : Convert.ToDateTime(item.BizTime).ToString("yyyy-MM-dd");

                                row++;
                            }
                        }
                        else
                        {
                            row++;
                        }
                    }
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"出现错误: {ex.Message}");
            }
        }
        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(RecognizeItemAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            var item = await _recognizeReceiveQueryService.GetItemById(input.RecognizeReceiveItemId);
            if (!string.IsNullOrEmpty(item.AttachFileIds))
            {
                var fileIds = item.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var bizFiles = new List<BizFileUploadOutput>();
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            bizFiles.Add(file);
                        }
                    }
                }
#if DEBUG
                bizFiles.Add(new BizFileUploadOutput { Id = Guid.Parse(fileIds[0]), Length = 1000, Name = "测试" });
#endif
                ret.Data = bizFiles;

            }
            return ret;
        }
        /// <summary>
        /// 查询销售-发票数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("PageQuerySell")]
        public async Task<List<PageQueryForFinancesOutput>> PageQuerySellAsync(PageQueryForFinancesInput input)
        {
            if (input.Type != null && input.Type == (int)RecognizeTypeEnums.Orderno)
            {
                var recognize = await _recognizeReceiveQueryService.GetItemById(input.ItemId);
                input.UserId = _appServiceContextAccessor.Get().UserId;
                input.BillCode = input.name;
                input.StartTime = DateTime.UtcNow.AddYears(-1);
                input.EndTime = DateTime.UtcNow.AddDays(1);
                input.PageSize = int.MaxValue;
                if (recognize != null)
                {
                    input.CompanyId = Guid.Parse(recognize.CompanyId);
                    //input.CustomerId = Guid.Parse(recognize.CustomerId);
                    input.BusinessDeptId = recognize.BusinessDepId;
                }
                // 第三方回款客户不限制查询
                if (input.IsReturnCustomer)
                {
                    input.CustomerId = null;
                }
                var ret = await _sellApiClient.PageQueryForFinancesAsync(input);
                return ret.List;
            }
            if (input.Type == (int)RecognizeTypeEnums.Invoice)
            {
                var datalist = new List<PageQueryForFinancesOutput>();
                var recognize = await _recognizeReceiveQueryService.GetItemById(input.ItemId);
                var query = new InvoicesQueryInput();
                query.UserId = CurrentUser.Id.Value;
                query.IsgreaterThanZero = true;
                query.Status = -1; //排除已作废
                query.UserName = CurrentUser.UserName;
                query.page = 1;
                query.limit = int.MaxValue; //获取全部筛选可认款金额为0的
                query.InvoiceNoLike = input.name;//模糊查询
                if (Guid.TryParse(recognize.CompanyId, out Guid companyId))
                {
                    query.CompanyId = companyId;
                }
                if (!string.IsNullOrEmpty(recognize.CustomerId))
                {
                    query.CustomerId = recognize.CustomerId;
                }
                // 第三方回款客户不限制查询
                if (input.IsReturnCustomer)
                {
                    query.CustomerId = null;
                }

                var (list, count) = await _invoiceQueryService.GetInvoiceListAsync(query);
                if (list.Any())
                {
                    // optimize 批量获取可认款金额
                    var recognizeReceiveAmounts = await _recognizeReceiveService.GetRecognizeReceiveAmount(list.Select(p => p.InvoiceNo).ToList(), 1);
                    foreach (var item in list)
                    {
                        var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.InvoiceNo);
                        var recognizeReceiveAmount = rra != null ? rra.Amount : 0;
                        // 初始化发票则加入计算
                        if (item.IsInit.HasValue && item.IsInit.Value)
                        {
                            recognizeReceiveAmount += Math.Abs(item.ReceiveAmount ?? 0);
                        }
                        var model = new PageQueryForFinancesOutput();
                        //可认款金额
                        model.TotalAmount = (item.InvoiceAmount - Math.Abs(item.RedAmount ??= 0) - recognizeReceiveAmount).Value;
                        model.BillCode = item.InvoiceNo;
                        if (model.TotalAmount < 0)
                        {
                            model.TotalAmount = 0;
                        }
                        if (model.TotalAmount != 0)
                        {
                            datalist.Add(model);
                        }
                    }
                }
                return datalist;
            }
            else if (input.Type == (int)RecognizeTypeEnums.Credit) 
            {
                var datalist = new List<PageQueryForFinancesOutput>();
                var recognize = await _recognizeReceiveQueryService.GetItemById(input.ItemId);
                var query = new CreditQueryInput();
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                query.IsgreaterThanZero = true;
                query.searchKey = input.name;
                query.CreditType = "2";
                query.page = 1;
                query.limit = int.MaxValue;
                query.CompanyId = input.CompanyId;
                if (Guid.TryParse(recognize.CompanyId, out Guid companyId))
                {
                    query.CompanyId = companyId;
                }
                if (Guid.TryParse(recognize.CustomerId, out Guid customerId))
                {
                    query.CustomerId = customerId;
                }
                // 第三方回款客户不限制查询
                if (input.IsReturnCustomer)
                {
                    query.CustomerId = null;
                }
                var (list, count) = await _creditQueryService.GetListAsync(query);
                if (list.Any())
                {
                    foreach (var item in list)
                    {
                        

                        var model = new PageQueryForFinancesOutput();
                        //可认款金额
                        model.TotalAmount = item.LeftAmount;
                        model.BillCode = item.BillCode;
                        if (model.TotalAmount < 0)
                        {
                            model.TotalAmount = 0;
                        }
                        if (model.TotalAmount != 0)
                        {
                            datalist.Add(model);
                        }
                        datalist.Add(model);
                    }
                }
                return datalist;
            }
            else
            {
                return new List<PageQueryForFinancesOutput> { };
            }
        }
        /// <summary>
        /// 获取金蝶附件
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet("GetKDFilePath")]
        public async Task<BaseResponseData<List<ReceiptNumberModelOutput>>> GetKDFilePath(string code)
        {
            var ret = await _kingdeeApiClient.SelectTheReceiptNumber(new List<ReceiptNumberModelInput> {
                new ReceiptNumberModelInput {
                     paymentNum = code,
                }
            }, type: "recBill");
            return ret;
        }
        /// <summary>
        /// 根据业务单元获取认款清单
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("GetListByServiceId")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveItemOutPut>>> GetListByServiceIdPages([FromBody] RecognizeReceiveItemInput input)
        {
            input.UserId = CurrentUser.Id;
            var result = await _recognizeReceiveQueryService.GetListByServiceIdPages(input);
            var res = new BaseResponseData<PageResponse<RecognizeReceiveItemOutPut>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<RecognizeReceiveItemOutPut>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        /// <summary>
        /// 根据业务单元获取认款清单页签数量
        /// </summary>
        /// <param name="input">认款单查询条件</param>
        /// <returns></returns>
        [HttpPost("getTabCountByService")]
        public async Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCountByService([FromBody] RecognizeReceiveItemInput input)
        {
            input.UserId = CurrentUser.Id;
            return await _recognizeReceiveQueryService.GetTabCountByService(input);
        }
        /// <summary>
        /// 导出收款单
        /// </summary>
        /// <param name="input">收款单查询条件</param>
        /// <returns></returns>
        [HttpPost("ExportService")]
        public async Task<IActionResult> ExportService([FromBody] RecognizeReceiveItemInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id;
                input.page = 1;
                input.limit = int.MaxValue;
                var result = await _recognizeReceiveQueryService.GetListByServiceIdPages(input);
                var list = result.List;
                if (list == null || !list.Any())
                {
                    return StatusCode(500, $"未查到符合条件的数据");
                }
                var detailList = new List<RecognizeReceiveDetailOutput>();
                foreach (var item in list.Select(p => p.Classify).Distinct())
                {
                    detailList.AddRange(await _recognizeReceiveQueryService.GetDetailsByIdAndClassify(list.Where(p => p.Classify == item).Select(p => p.Id).ToList(), (int)item));
                }
                if (detailList == null || !detailList.Any())
                {
                    return StatusCode(500, $"未查到符合条件的明细数据");
                }
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    // 4,5,24
                    var worksheet = package.Workbook.Worksheets.Add("收款单清单");
                    worksheet.Cells[1, 1].Value = "认款单号";
                    worksheet.Cells[1, 2].Value = "收款单号";
                    worksheet.Cells[1, 3].Value = "暂收款单号";
                    worksheet.Cells[1, 4].Value = "本次认款金额";
                    worksheet.Cells[1, 5].Value = "收款金额";
                    worksheet.Cells[1, 6].Value = "收款类型";
                    worksheet.Cells[1, 7].Value = "核算部门";
                    worksheet.Cells[1, 8].Value = "公司";
                    worksheet.Cells[1, 9].Value = "客户";
                    worksheet.Cells[1, 10].Value = "收款日期";
                    worksheet.Cells[1, 11].Value = "银行类型";
                    worksheet.Cells[1, 12].Value = "银行账户";
                    worksheet.Cells[1, 13].Value = "结算方式";
                    worksheet.Cells[1, 14].Value = "到期日";
                    worksheet.Cells[1, 15].Value = "项目名称";
                    worksheet.Cells[1, 16].Value = "项目编码";
                    worksheet.Cells[1, 17].Value = "状态";
                    worksheet.Cells[1, 18].Value = "类型";
                    worksheet.Cells[1, 19].Value = "创建时间";
                    worksheet.Cells[1, 20].Value = "创建人";

                    worksheet.Cells[1, 21].Value = "发票/订单号/应收单号";
                    worksheet.Cells[1, 22].Value = "认款人";
                    worksheet.Cells[1, 23].Value = "实际客户";
                    worksheet.Cells[1, 24].Value = "终端客户";
                    worksheet.Cells[1, 25].Value = "认款类型";
                    worksheet.Cells[1, 26].Value = "认款金额";
                    worksheet.Cells[1, 27].Value = "认款日期";
                    //worksheet.Cells[1, 28].Value = "业务单元";
                    worksheet.Cells[1, 28].Value = "是否跳号";
                    worksheet.Cells[1, 29].Value = "细分类型";
                    worksheet.Cells[1, 30].Value = "备注";
                    worksheet.Cells[1, 31].Value = "发票/订单号/应收单号日期";

                    int row = 2;
                    foreach (var item in list)
                    {
                        // 设置数值单元格格式
                        worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 24].Style.Numberformat.Format = "#,##0.00";
                        // 填装单元格数据
                        worksheet.Cells[row, 1].Value = item.Code;
                        worksheet.Cells[row, 2].Value = item.ReceiveCode;
                        worksheet.Cells[row, 3].Value = item.RelateCode;
                        worksheet.Cells[row, 4].Value = item.Value;
                        worksheet.Cells[row, 5].Value = item.ReceiveValue;
                        worksheet.Cells[row, 6].Value = item.Type;
                        worksheet.Cells[row, 7].Value = item.BusinessDeptFullName;
                        worksheet.Cells[row, 8].Value = item.CompanyName;
                        worksheet.Cells[row, 9].Value = item.CustomerNme;
                        worksheet.Cells[row, 10].Value = Convert.ToDateTime(item.ReceiveDate).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 11].Value = item.BankName;
                        worksheet.Cells[row, 12].Value = item.BankNum;
                        worksheet.Cells[row, 13].Value = item.Settletype;
                        worksheet.Cells[row, 14].Value = item.DraftBillExpireDate == null ? "" : Convert.ToDateTime(item.DraftBillExpireDate).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 15].Value = item.ProjectName;
                        worksheet.Cells[row, 16].Value = item.ProjectCode;
                        worksheet.Cells[row, 17].Value = item.StatusDescription;
                        worksheet.Cells[row, 18].Value = item.ClassifyDescription;
                        worksheet.Cells[row, 19].Value = item.CreatedTime.DateTime.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 20].Value = item.CreatedBy;

                        var details = detailList.Where(p => p.RecognizeReceiveItemId == item.Id).ToList();
                        // 详情
                        if (details != null && details.Any())
                        {
                            foreach (var detail in details)
                            {
                                worksheet.Cells[row, 21].Value = detail.Code;
                                worksheet.Cells[row, 22].Value = detail.CreatedBy;
                                worksheet.Cells[row, 23].Value = detail.CustomerName;
                                worksheet.Cells[row, 24].Value = detail.HospitalName;
                                worksheet.Cells[row, 25].Value = detail.TypeDescription;
                                worksheet.Cells[row, 26].Value = detail.Value;
                                worksheet.Cells[row, 27].Value = Convert.ToDateTime(detail.RecognizeDate).ToString("yyyy-MM-dd");
                                //worksheet.Cells[row, 28].Value = detail.ServiceName;
                                worksheet.Cells[row, 28].Value = detail.IsSkip.HasValue && detail.IsSkip.Value ? "是" : "否";
                                worksheet.Cells[row, 29].Value = detail.ClassifyDescription;
                                worksheet.Cells[row, 30].Value = detail.Note;
                                worksheet.Cells[row, 31].Value = detail.CodeTime;

                                row++;
                            }
                        }
                        else
                        {
                            row++;
                        }
                    }
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"出现错误: {ex.Message}");
            }
        }

        [HttpPost("GetAgentRefundAbatements")]
        public async Task<BaseResponseData<PageResponse<AgentRefundAbatementOutput>>> GetAgentRefundAbatements([FromBody] AgentRefundAbatementInput input)
        { 
            var result = await _recognizeReceiveQueryService.GetAgentRefundAbatements(input);
            var res = new BaseResponseData<PageResponse<AgentRefundAbatementOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<AgentRefundAbatementOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        /// <summary>
        ///  获取供应商退款冲销列表下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AgentRefundAbatementsDownLoad")]
        public async Task<IActionResult> AgentRefundAbatementsDownLoad([FromBody] AgentRefundAbatementInput input)
        {
            try
            {
                input.limit=int.MaxValue;
                var ret = await _recognizeReceiveQueryService.GetAgentRefundAbatements(input);
           
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "应付单号";
                    worksheet.Cells[1, 2].Value = "收款单号";
                    worksheet.Cells[1, 3].Value = "冲销金额";
                    worksheet.Cells[1, 4].Value = "核算部门";
                    worksheet.Cells[1, 5].Value = "公司名称";
                    worksheet.Cells[1, 6].Value = "客供应商名称";
                    worksheet.Cells[1, 7].Value = "项目名称";
                    worksheet.Cells[1, 8].Value = "冲销人";
                    worksheet.Cells[1, 9].Value = "冲销日期";
                  
                    #endregion

                    #region 数据
                    int row = 2;
                    if (ret != null && ret.List != null && ret.List.Any())
                    {
                        foreach (var item in ret.List)
                        {
                            worksheet.Cells[row, 1].Value = item.DebtCode;
                            worksheet.Cells[row, 2].Value = item.ReceiveCode;
                            worksheet.Cells[row, 3].Value = item.AbatementValue;
                            worksheet.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 4].Value = item.BusinessDeptFullName;
                            worksheet.Cells[row, 5].Value = item.CompanyName;
                            worksheet.Cells[row, 6].Value = item.AgentName;
                            worksheet.Cells[row, 7].Value = item.ProjectName; 
                            worksheet.Cells[row, 8].Value = item.AbatementCreatedBy; 
                            worksheet.Cells[row, 9].Value = item.AbatementDate;
                            row++;
                        }
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
