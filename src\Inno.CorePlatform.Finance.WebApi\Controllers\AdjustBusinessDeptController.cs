using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 项目更换核算部门
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AdjustBusinessDeptController : ControllerBase
    {
        private readonly IAdjustBusinessDeptAppService _adjustBusinessDeptAppService;
        private readonly ISubLogService _subLogService;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public AdjustBusinessDeptController(IAdjustBusinessDeptAppService adjustBusinessDeptAppService
        ,ISubLogService subLogService)
        {
            _adjustBusinessDeptAppService = adjustBusinessDeptAppService;
            _subLogService = subLogService;
        }

        /// <summary>
        /// 获取项目相关的在途订单信息
        /// </summary>
        /// <param name="input">包含项目信息的输入参数</param>
        /// <returns>包含在途订单数据的响应</returns>
        [HttpPost("getInTransitOrders")]
        public async Task<BaseResponseData<List<InTransitOrderOutput>>> GetInTransitOrders([FromBody] List<AdjustProjectInfoInput> input)
        {
            if (input == null || !input.Any())
            {
                return BaseResponseData<List<InTransitOrderOutput>>.Success();
            }

            try
            {
                var projectIds = input.Select(x => x.Id).Distinct().ToList();
                
                // 获取识别应收和应付批量付款的在途项
                var recognizeReceiveItems = await _adjustBusinessDeptAppService.GetRecognizeReceiveItemsByProjectIdsAsync(projectIds);
                var paymentAutoItems = await _adjustBusinessDeptAppService.GetPaymentAutoItemsByProjectIdsAsync(projectIds);
                
                // 合并在途项并创建响应
                var list = recognizeReceiveItems.Concat(paymentAutoItems).ToList();
                
                return new BaseResponseData<List<InTransitOrderOutput>>
                {
                    Data = list
                };
            }
            catch (Exception ex)
            {
                _subLogService.LogAzure("getInTransitOrders",$"{input.ToJson()}-{ex.StackTrace}","获取项目相关的在途订单信息");
                return BaseResponseData<List<InTransitOrderOutput>>.Failed(500, "内部错误");
            }
        }
    }
}