﻿using Inno.CorePlatform.Common.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    /// <summary>
    ///  销售订单查询仅供财务使用 入参
    /// </summary>
    public class PageQueryForFinancesInput : BasePagedRequestData
    {
        /// <summary>
        /// 公司 ID
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 核算部门ID
        /// </summary>
        public int? BusinessDeptId { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary> 
        public DateTimeOffset? StartTime { get; set; }


        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTimeOffset? EndTime { get; set; }

        /// <summary>
        /// 状态列表
        /// </summary>
        public List<SaleStatusEnum>? StatusList { get; set; }

        public int? Type { get; set; }
        public string? name { get; set; }

        public Guid ItemId { get; set; }

        /// <summary>
        /// 是否第三方回款客户
        /// </summary>
        public bool IsReturnCustomer { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        public List<Guid>? ProjectIds { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public List<string>? Sort { get; set; }
    }

    /// <summary>
    /// 销售订单查询仅供财务使用 出参
    /// </summary>
    public class PageQueryForFinancesOutput
    {
        /// <summary>
        /// ID 
        /// </summary>
        public Guid Id { get; set; }



        /// <summary>
        /// 订单时间
        /// </summary>
        public DateTimeOffset BillDate { get; set; }


        /// <summary>
        /// 订单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public SaleStatusEnum Status { get; set; }

        /// <summary>
        /// 可认款金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        public string? CustomerId { get; set; }

        public string? CustomerName { get; set; }
        /// <summary>
        /// 终端医院id
        /// </summary>
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端医院名
        /// </summary>
        public string? HospitalName { get; set; }


        public SaleTypeOutputEnum? SaleType { get; set; }

        /// <summary>
        /// 项目信息列表
        /// </summary>
        public List<PageQueryForFinancesProjectOutput>? ProjectInfos { get; set; }
        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }


        /// <summary>
        /// 总订单金额
        /// </summary>
        public decimal Amount { get; set; }
    }
    /// <summary>
    /// 项目信息
    /// </summary>
    public class PageQueryForFinancesProjectOutput
    {

        /// <summary>
        /// 项目ID
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
    }
}
