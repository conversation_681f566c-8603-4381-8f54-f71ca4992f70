<template>
  <div class="app-page-container">
    <!-- <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>收款认领</el-breadcrumb-item>
      </el-breadcrumb>
    </div>-->
    <div class="app-page-body" style="padding-top: 0">
      <el-tabs v-model="activeName" class="app-page-tabs" @tab-change="tabhandleClick">
        <el-tab-pane label="待认款记录" name="receive">
          <inno-query-operation v-model:query-list="queryListReceive" :crud="crudReceive" />
          <inno-crud-operation :crud="crudReceive" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 45px">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`待认款记录`" />
              </el-tabs>
            </template>
            <!-- v-auth="functionUris.add" -->
            <el-button
              slot="reference"
              v-if="crudReceive.rowData.receivingtype!=='退采购付款' && hasPermission(functionUris.add)"
              :loading="crudReceive.delAllLoading"
              type="primary"
              @click="createRecognizeReceive(1)"
            >创建认款单</el-button>
            <!-- v-auth="functionUris.add" -->
            <el-button
              slot="reference"
              v-if="crudReceive.rowData.receivingtype!=='退采购付款'&& hasPermission(functionUris.add)"
              :loading="crudReceive.delAllLoading"
              type="warning "
              @click="createRecognizeReceive(2)"
            >创建暂收款</el-button>
            <!-- v-auth="functionUris.add" -->
            <el-button slot="reference" v-if="hasPermission(functionUris.add)" :loading="crudReceive.delAllLoading" type="primary" @click="saveSureType">保存收款类型</el-button>
          </inno-crud-operation>
          <el-table
            ref="tableReceive"
            v-inno-loading="crudReceive.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crudReceive.data"
            stripe
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crudReceive.tableRowClassName"
            @sort-change="crudReceive.sortChange"
            @selection-change="crudReceive.selectionChangeHandler"
            @row-click="crudReceive.singleSelection"
          >
            <el-table-column type="selection" width="30" fixed="left" />
            <el-table-column label="序号" fixed="left" type="index"></el-table-column>
            <el-table-column label="收款单号" property="billno" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billno }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="orgName" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orgName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="收款金额" property="actrecamt" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.actrecamt" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="客户" property="payerName" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.payerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目" property="itemNumber" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.itemNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="可认款金额" property="namountclaimed" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.namountclaimed" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="收款类型" property="receivingtype" width="100" show-overflow-tooltip sortable></el-table-column>
            <el-table-column label="业务单元" property="serviceId" show-overflow-tooltip></el-table-column>
            <el-table-column label="收款日期" property="payeedateStr" width="100" show-overflow-tooltip sortable></el-table-column>
            <el-table-column label="交易时间" property="bizTime" width="100" show-overflow-tooltip sortable></el-table-column>
            <el-table-column label="汇款人银行类型" property="bankName" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="汇款人银行账户" property="bankNum" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="收款人银行类型" property="payeeBankName" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="收款人银行账户" property="accountBank" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="结算方式" property="settletype" width="100" show-overflow-tooltip sortable></el-table-column>
            <el-table-column label="到期日" property="draftbillexpiredateStr" show-overflow-tooltip sortable></el-table-column>
            <el-table-column label="指定项目" property="projectno3">
              <template #default="scope">
                <inno-remote-select
                  v-model="scope.row.code"
                  valueK="code"
                  :queryData="{ status: 2 }"
                  :is-guid="2"
                  default-first-option
                  placeholder="请选择项目"
                  :url="gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta'"
                />
              </template>
            </el-table-column>
            <el-table-column label=" 指定核算部门" property="orgno" width="120">
              <template #default="scope">
                <inno-department-select
                  v-model="scope.row.deptId"
                  v-model:name="scope.row.name"
                  v-model:path="scope.row.path"
                  v-model:fullName="scope.row.fullName"
                  functionUri="metadata://fam"
                  placeholder="请选择核算部门"
                ></inno-department-select>
              </template>
            </el-table-column>
            <el-table-column label=" 指定收款类型" property="kdReceivingtype" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.kdReceivingtype" placeholder="请选择收款类型" :disabled="!['负数应收'].indexOf(scope.row.receivingtype)">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="备注" property="remake">
              <template #default="scope">
                <el-input v-model="scope.row.remake" placeholder="请输入备注"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link v-if="scope.row.billno" style="font-size: 12px" type="primary" @click="downloadFile(scope.row.billno)">回执单</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crudReceive.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudReceive" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="客户认款单" name="recognize">
          <inno-query-operation v-model:query-list="queryList" :crud="crud" />
          <inno-split-pane split="horizontal" :min-percent="40" :default-percent="60" style="padding: 0px">
            <template #paneL="{ full, onFull }">
              <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0; height: 45px">
                <template #opts-left>
                  <el-tabs v-model="crud.query.abatedStatus" class="demo-tabs" @tab-change="tabhandleChildClick">
                    <el-tab-pane :label="`待提交(${tabCount.waitSubmitCount})`" name="0" lazy />
                    <el-tab-pane :label="`审批中(${tabCount.waitExecuteCount})`" name="1" lazy />
                    <el-tab-pane :label="`已完成(${tabCount.completedCount})`" name="99" lazy />
                    <el-tab-pane :label="`部分撤销(${tabCount.partCancel})`" name="-2" lazy />
                    <el-tab-pane :label="`已撤销(${tabCount.auditingCount})`" name="-1" lazy />
                    <el-tab-pane :label="`全部(${tabCount.allCount})`" name lazy />
                  </el-tabs>
                </template>
                <template #default>
                  <!-- v-auth="functionUris.submit" -->
                  <inno-button-tooltip
                    v-if="classifyType === 2 && (crud.rowData.status === 99 || crud.rowData.status === -2) && hasPermission(functionUris.del)"
                    content="请至少选择一条数据"
                    class="filter-item"
                    type="warning"
                    :loading="crud.delAllLoading || submitLoading"
                    :disabled="crud.selections.length === 0"
                    @click="cancelTempDeatail()"
                  >暂收款撤销</inno-button-tooltip>
                  <inno-button-tooltip
                    v-if="classifyType === 2 && (crud.rowData.status === 99 || crud.rowData.status === -2) && hasPermission(functionUris.submit)"
                    content="请至少选择一条数据"
                    class="filter-item"
                    type="warning"
                    :loading="crud.delAllLoading || submitLoading"
                    :disabled="crud.selections.length === 0"
                    @click="transfer(crud.rowData)"
                  >转货款</inno-button-tooltip>
                  <!-- v-auth="functionUris.submit" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && hasPermission(functionUris.submit)"
                    content="请至少选择一条数据"
                    class="filter-item"
                    type="primary"
                    :loading="crud.delAllLoading || submitLoading"
                    :disabled="crud.selections.length === 0"
                    @click="submit(crud.rowData)"
                  >提交</inno-button-tooltip>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && hasPermission(functionUris.add)"
                    content="请至少选择一条数据"
                    class="filter-item"
                    type="primary"
                    :loading="crud.delAllLoading"
                    :disabled="crud.selections.length === 0"
                    @click="openAttachment(crud.rowData)"
                  >附件</inno-button-tooltip>
                  <!-- v-auth="functionUris.del" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0  && hasPermission(functionUris.del)"
                    content="请至少选择一条数据"
                    type="warning"
                    :loading="crud.delAllLoading"
                    :disabled="crud.selections.length === 0"
                    @click="deleteItem(crud.rowData)"
                  >删除</inno-button-tooltip>
                  <!-- v-auth="functionUris.del" -->
                  <inno-button-tooltip
                    v-if="
                      crud.rowData.status === 99 &&
                      crud.rowData.relateCode === null && hasPermission(functionUris.del) &&
                      crud.rowData.classify === 1
                    "
                    content="请至少选择一条数据"
                    type="warning"
                    :loading="crud.delAllLoading"
                    :disabled="crud.selections.length === 0"
                    @click="cancelItem(crud.rowData)"
                  >撤销</inno-button-tooltip>
                  <!-- v-auth="functionUris.del" -->
                  <inno-button-tooltip v-if="hasPermission(functionUris.del)" type="warning" :loading="exportBillLoading" @click="exportBill()">导出数据</inno-button-tooltip>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableItem"
                v-inno-loading="crud.loading"
                class="auto-layout-table"
                highlight-current-row
                :data="crud.data"
                stripe
                fit
                border
                show-summary
                :summary-method="getSummaries"
                :row-class-name="crud.tableRowClassName"
                @sort-change="crud.sortChange"
                @selection-change="crud.selectionChangeHandler"
                @row-click="
                        (e)=>
                {
                crud.singleSelection(e);
                getDetailData(e);
                getReturnCustomer();
                }
                "
              >
                <el-table-column fixed="left" width="45">
                  <template #default="scope">
                    <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
                  </template>
                </el-table-column>
                <el-table-column label="认款单号" property="code" min-width="200" show-overflow-tooltip sortable>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.code" :crud="crud" :column="column" isInput />
                  </template>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="收款单号" property="receiveCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="暂收款单号" property="relateCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column class-name="isSum" property="value" label="本次认款金额" min-width="90" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column property="remainingRecognizableAmount" label="剩余可认款金额" min-width="110" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.remainingRecognizableAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column property="receiveValue" label="收款金额" min-width="90" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.receiveValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="收款类型" property="type" show-overflow-tooltip></el-table-column>
                <el-table-column label="核算部门" property="businessDeptFullName" width="120" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="公司" property="companyName" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="客户" property="customerNme" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.customerNme }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="收款日期" min-width="120" property="receiveDate" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.receiveDate, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column label="银行类型" property="bankName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.bankName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="银行账户" property="bankNum" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.bankNum }}</inno-button-copy>
                  </template>
                </el-table-column>

                <el-table-column label="结算方式" property="settletype" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.settletype }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="到期日" min-width="120" property="draftBillExpireDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    dateFormat(scope.row.draftBillExpireDate, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="贴现日期" min-width="120" property="discountDate" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    dateFormat(scope.row.discountDate, 'YYYY-MM-DD')
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="项目名称" property="projectName" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="项目编码" property="projectCode" width="150" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column property="statusDescription" label="状态" width="80" />
                <el-table-column property="classifyDescription" label="类型" width="80" />
                <el-table-column property="transferStatusDescription" label="转货款状态" width="100" />
                <el-table-column label="创建日期" width="120" property="createdTime" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column label="创建人" property="createdByName" width="90" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
                  </template>
                  <template #default="scope">{{ scope.row.createdByName }}</template>
                </el-table-column>
                <el-table-column label="附件" property="attachFileIds" width="130" show-overflow-tooltip>
                  <template #default="scope">
                    <el-link
                      style="font-size: 13px"
                      @click="
                        showAttachFile(scope.row.attachFileIds, scope.row.id)
                      "
                    >{{ scope.row.attachFileIds ? '查看附件' : '' }}</el-link>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70" fixed="right">
                  <template #default="scope">
                    <el-link v-if="scope.row.receiveCode" style="font-size: 12px" type="primary" @click="downloadFile(scope.row.receiveCode)">回执单</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                已选择 {{ crud.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :page-sizes="[10, 20, 50, 100, 200, 500]" :crud="crud" />
              </div>
            </template>
            <template #paneR="{ full, onFull }">
              <inno-crud-operation :crud="crudDetail" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs v-model="setDetailTab" class="demo-tabs">
                    <el-tab-pane :label="`明细信息`" name="-1"></el-tab-pane>
                  </el-tabs>
                </template>
                <template v-if="!crud.query?.id" #default>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && classifyType === 1 && hasPermission(functionUris.add)"
                    icon="Check"
                    :loading="saveDateLoading"
                    type="success"
                    @click="saveDate"
                  >保存</inno-button-tooltip>
                  <inno-button-tooltip
                    v-if="(crud.rowData.status === 99 || crud.rowData.status === -2) && classifyType === 1 && (crud.rowData.relateCode === '' || crud.rowData.relateCode === null) && hasPermission(functionUris.del)"
                    icon="Refresh"
                    :loading="crudDetail.delAllLoading"
                    type="primary"
                    @click="partCancelDialog"
                  >部分撤销</inno-button-tooltip>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && classifyType === 1 && hasPermission(functionUris.add)"
                    icon="Plus"
                    :loading="crudDetail.delAllLoading"
                    type="primary"
                    @click="batchCreatedDialog"
                  >批量创建</inno-button-tooltip>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && crud.rowData.classify === 2 && hasPermission(functionUris.add)"
                    icon="Plus"
                    :loading="crudDetail.delAllLoading"
                    type="primary"
                    content="请先选择批量付款单"
                    :disabled="crud.selections.length === 0"
                    @click="createdDialog(1)"
                  >创建</inno-button-tooltip>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && crud.rowData.classify === 2 && hasPermission(functionUris.add)"
                    content="请先选择批量付款单"
                    type="primary"
                    :loading="crudDetail.delAllLoading"
                    :disabled="crudDetail.selections.length !== 1"
                    @click="createdDialog(2)"
                  >编辑</inno-button-tooltip>
                  <!-- v-auth="functionUris.del" -->
                  <inno-button-tooltip
                    v-if="crud.rowData.status === 0 && hasPermission(functionUris.del)"
                    content="请至少选择一条数据"
                    class="filter-item"
                    type="warning"
                    :loading="crudDetail.delAllLoading"
                    :disabled="crudDetail.selections.length === 0"
                    @click="deleteDetail(crud.rowData)"
                  >删除</inno-button-tooltip>
                  <!-- v-auth="functionUris.add" -->
                  <inno-button-tooltip
                    v-if="false"
                    type="warning"
                    icon="Download"
                    content="请先选择批量付款单"
                    :loading="exportLoading"
                    :disabled="crud.selections.length === 0"
                    @click="importExcel"
                  >导入数据</inno-button-tooltip>
                  <inno-button-tooltip v-if="crud.rowData.status === 0 && classifyType === 1 && hasPermission(functionUris.add)" type="primary" @click="openImportModel">导入数据</inno-button-tooltip>
                  <inno-button-tooltip v-if="false" type @click="toggleAllExpansion">{{ isAllExpanded ? '全部折叠' : '全部展开' }}</inno-button-tooltip>
                  <inno-button-tooltip
                    v-if="crud.selections.length === 1 && classifyType === 1 && crudDetail.data.length > 0"
                    type="primary"
                    @click="downloadAsync(
                      'api/RecognizeReceive/exportDetailsTask',
                      '导出认款明细'
                    )"
                  >导出认款明细</inno-button-tooltip>
                  <inno-button-tooltip
                    v-if="crud.selections.length === 1 && classifyType === 1 && crudDetail.data.length > 0"
                    type="primary"
                    @click="downloadAsync(
                      'api/RecognizeReceive/exportDetailCreditsTask',
                      '导出明细包含应收'
                    )"
                  >导出明细包含应收</inno-button-tooltip>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table
                id="tableDetail"
                ref="tableDetail"
                v-inno-loading="crudDetail.loading"
                class="auto-layout-table"
                highlight-current-row
                :data="crudDetail.data"
                stripe
                fit
                border
                :row-class-name="crudDetail.tableRowClassName"
                @sort-change="crudDetail.sortChange"
                @selection-change="crudDetail.selectionChangeHandler"
                @row-click="(e) => clickdetailRow(e)"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <!-- 认款明细应收详情 -->
                <!-- 新增用于显示带展开折叠功能的子表格的列 -->
                <el-table-column type="expand" fixed="left">
                  <template #default="{ row }">
                    <div style="padding-left: 54px" v-if="row.typeDescription !== '初始应收'">
                      <el-table :data="row.creditInfo" class="auto-layout-table" stripe border fit>
                        <el-table-column label="应收单号" property="billCode" width="240" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="应收类型" property="creditTypeStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="单据日期" property="billDateStr" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.billDateStr }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="项目" property="projectName" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.projectName }}</template>
                        </el-table-column>
                        <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip>
                          <template #default="scope">{{ scope.row.serviceName }}</template>
                        </el-table-column>
                        <el-table-column label="金额" property="value" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.value }}</inno-button-copy>
                          </template>
                        </el-table-column>
                        <el-table-column label="本次认款金额" property="currentValue" show-overflow-tooltip>
                          <template #default="scope">
                            <inno-button-copy :link="false">{{ scope.row.currentValue }}</inno-button-copy>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="50" property="statusDescription" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.statusDescription }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="发票号/订单号/应收单号" width="240" property="code" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  label="认款人"
                  property="createdByName"
                  width="100"
                  show-overflow-tooltip
                ></el-table-column>-->
                <el-table-column label="认款人" property="createdByName" width="100" show-overflow-tooltip>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryObject.createdBy" :crud="crudDetail" :column="column" />
                  </template>
                  <template #default="scope">{{ scope.row.createdByName }}</template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="实际客户" property="customerName" width="140" show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">{{ scope.row.customerName }}</div>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="终端客户" property="hospitalName" width="140" show-overflow-tooltip>
                  <template #default="scope">
                    <div style="color: rgb(255, 77, 0)">{{ scope.row.hospitalName }}</div>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="认款类型" property="typeDescription" width="80" show-overflow-tooltip></el-table-column>

                <el-table-column v-if="classifyType === 2" label="项目名称" property="projectName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="项目单号" property="projectCode" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="客户名称" property="customerName" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column label="认款金额" property="value" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.value" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="撤销金额" property="cancelValue" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.cancelValue" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column label="认款日期" min-width="120" property="recognizeDate" width="120" show-overflow-tooltip>
                  <template #default="scope">{{ dateFormat(scope.row.recognizeDate, 'YYYY-MM-DD') }}</template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="业务单元" property="serviceName" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.serviceName }}</template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="是否跳号" property="isSkip" width="70" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.isSkip ? '是' : '否' }}</template>
                </el-table-column>
                <el-table-column label="细分类型" property="classifyDescription" width="100" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.classifyDescription }}</template>
                </el-table-column>
                <el-table-column v-if="classifyType === 2" label="收款类型" property="collectionType" show-overflow-tooltip>
                  <template #default="scope">
                    {{
                    options.filter(
                    (p) => p.value === scope.row.collectionType + ''
                    ) === null
                    ? ''
                    : options.filter(
                    (p) => p.value === scope.row.collectionType + ''
                    )[0].label
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="备注" property="note" show-overflow-tooltip>
                  <template #default="scope">{{ scope.row.note }}</template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="发票号/订单号/应收单号日期" property="codeTime" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.codeTime }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="票面金额" property="invoiceAmount" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-numeral :value="scope.row.invoiceAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column v-if="classifyType === 1" label="回款显示日期" property="backDateTime" show-overflow-tooltip width="150">
                  <template #default="scope">
                    <el-date-picker v-model="scope.row.backDateTime" v-if="crud.rowData.status === 0" type="date" placeholder="回款显示日期" size="small" style="width: 100%;" />
                    <inno-button-copy :link="false" v-if="crud.rowData.status !== 0">{{ dateFormat(scope.row.backDateTime, 'YYYY-MM-DD') }}</inno-button-copy>
                  </template>
                </el-table-column>
              </el-table>
              <div class="app-page-footer background">
                认款金额合计：
                <inno-numeral :value="totaldetail(crudDetail.data)" format="0,0.00" />
                <div class="flex-1" />
              </div>
            </template>
          </inno-split-pane>
        </el-tab-pane>
        <el-tab-pane label="供应商退款冲销" name="agentRefundAbatements">
          <inno-query-operation v-model:query-list="queryAgentRefundAbatementsList" :crud="crudAgentRefundAbatements" />
          <inno-crud-operation :crud="crudAgentRefundAbatements" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`供应商退款冲销`" />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip type="warning" @click="downloadAgentRefundAbatementsDownLoad">导出数据</inno-button-tooltip>
            </template>
          </inno-crud-operation>
          <el-table
            v-inno-loading="crudAgentRefundAbatements.loading"
            class="auto-layout-table"
            highlight-current-row
            :data="crudAgentRefundAbatements.data"
            stripe
            fit
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crudAgentRefundAbatements.tableRowClassName"
            @sort-change="crudAgentRefundAbatements.sortChange"
            @selection-change="crudAgentRefundAbatements.selectionChangeHandler"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column label="应付单号" property="debtCode" min-width="200" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.debtCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="收款单号" property="receiveCode" min-width="200" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="冲销金额" property="abatementValue" min-width="90" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatementValue" format="0,0.00" />
              </template>
            </el-table-column>
            <!-- <el-table-column label="收款金额" property="receiveValue" min-width="90" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.receiveValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="收款日期" property="receiveDate" width="200" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.receiveDate }}</inno-button-copy>
              </template>
            </el-table-column>-->
            <el-table-column label="核算部门" property="businessDeptFullName" min-width="200">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司名称" property="companyName" min-width="200">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="供应商名称" property="agentName" min-width="200">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" min-width="200">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="冲销人" property="abatementCreatedBy" width="150">
              <template #default="scope">
                <inno-user-name :userName="scope.row.abatementCreatedBy"></inno-user-name>
              </template>
            </el-table-column>
            <el-table-column label="冲销日期" property="abatementDate" width="200">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.abatementDate }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crudAgentRefundAbatements.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudAgentRefundAbatements" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog v-model="DeptDialogVisible" title="请选择核算部门">
      <el-form>
        <el-form-item label="核算部门">
          <inno-department-select
            ref="innoDepartmentSelectRef"
            v-model="newDepart.id"
            v-model:name="newDepart.name"
            v-model:path="newDepart.path"
            v-model:fullName="newDepart.fullName"
            v-model:item="newDepart.item"
            functionUri="metadata://fam"
          ></inno-department-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="DeptDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createOpt">创建</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="comfile_upload" title="上传附件" destroy-on-close :close-on-click-modal="false" draggable>
      <inno-file-uploader
        v-model="flieList"
        list-type="text"
        drag
        multiple
        bizType="finance"
        fileMode="large"
        appId="fam"
        :limitType="[
          'doc',
          'docx',
          'pdf',
          'xls',
          'xlsx',
          'png',
          'jpg',
          'jpeg',
          'gif'
        ]"
        :folders="folders"
        :beforeUpload="fileBeforeUpload"
        :on-success="fileOnSuccess"
        style="display: flex; justify-content: center"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点此上传文件</em>
        </div>
        <!-- <template #tip>
          <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
        </template>-->
      </inno-file-uploader>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosefile">取消</el-button>
          <el-button type="primary" @click="savefile">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 文件查看 -->
    <el-dialog v-model="comfile_show" title="附件查看" destroy-on-close :close-on-click-modal="false" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小(b)" />
        <el-table-column label="操作">
          <template #default="scope">
            <span @click="showFileInfo(scope.row.id)">查看</span>
            |
            <span style="cursor: pointer" @click="deleteFile(scope.row.id)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <RecognizeSelectComponent
      ref="RecognizeSelectRef"
      :showDialog="receiveSelDialogShow"
      :createdType="createdType"
      :paymentAutoItemId="selectId"
      @closeDialog="closeDialogCallBack"
      @refreshIndex="refreshIndexCallBack"
    ></RecognizeSelectComponent>
    <RecognizeSureTypeComponent
      ref="RecognizeSureTypeRef"
      :showDialog="receivesureTypeDialogShow"
      :createdType="createdType"
      :paymentAutoItemId="selectId"
      @closeDialog="closeDialogCallBack"
      @refreshIndex="refreshIndexCallBack"
    ></RecognizeSureTypeComponent>
    <RecognizeCreatedComponent
      ref="RecognizeCreatedRef"
      :showDialog="createdDialogShow"
      :itemId="selectId"
      :customerId="crud.rowData.customerId"
      :customerName="crud.rowData.customerName"
      :isReturnCustomer="isReturnCustomer"
      :detailId="crudDetail.rowData.id"
      :isedit="createdType"
      :classifyType="classifyType"
      @closeDialog="closeCreatedDialogCallBack"
      @refreshIndex="refreshDetailCallBack"
    ></RecognizeCreatedComponent>
    <RecognizeImportComponent
      :showDialog="importDialogShow"
      :itemId="selectId"
      :customerId="crud.rowData.customerId"
      :isReturnCustomer="isReturnCustomer"
      @closeDialog="closeImportDialogCallBack"
      @refreshIndex="refreshIndexCallBack"
    ></RecognizeImportComponent>

    <RecognizeBatchCreatedComponent
      ref="RecognizeBatchCreatedRef"
      :itemId="selectId"
      :cId="companyId"
      :customerId="crud.rowData.customerId"
      :isReturnCustomer="isReturnCustomer"
      :showDialog="batchCreatedDialogShow"
      :editdata="crudDetail.data"
      :detailId="crudDetail.rowData.id"
      :isedit="createdType"
      :classifyType="1"
      :canAmount="crud.rowData.remainingRecognizableAmount"
      @closeDialog="closeBatchCreatedDialogCallBack"
      @refreshIndex="refreshDetailCallBack"
    ></RecognizeBatchCreatedComponent>

    <RecognizePartCancelComponent
      ref="RecognizePartCancelRef"
      :itemId="selectId"
      :editdata="crudDetail.data"
      :showDialog="partCancelDialogShow"
      @closeDialog="closePartCancelDialogCallBack"
      @refreshIndex="refreshDetailCallBack"
    ></RecognizePartCancelComponent>

    <RecognizePartCancelTempComponent
      ref="RecognizePartCancelTempRef"
      :itemId="selectId"
      :editdata="crudDetail.data"
      :showDialog="partCancelTempDialogShow"
      @closeDialog="closePartCancelTempDialogCallBack"
      @refreshIndex="refreshDetailCallBack"
    ></RecognizePartCancelTempComponent>
    <!-- 导入数据组件 -->
    <ImportRecognize ref="ImportDetail" @onImportSuccess="onImportSuccess"></ImportRecognize>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  nextTick
} from 'vue';
import comfile from '@/component/com-files.vue';
import { backendUrl, gatewayUrl } from '@/public-path';
import { ElTable, ElLoading } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request';
import { getTreeList, getDepartTree } from '@/api/bdsData';
import RecognizeSelectComponent from '@/views/recognizeReceive/selected.vue';
import RecognizeSureTypeComponent from '@/views/recognizeReceive/sureType.vue';
import RecognizeCreatedComponent from '@/views/recognizeReceive/created.vue';
import RecognizeImportComponent from '@/views/recognizeReceive/import.vue';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import RecognizeBatchCreatedComponent from '@/views/recognizeReceive/batchCreated.vue';
import RecognizePartCancelComponent from '@/views/recognizeReceive/partCancel.vue';
import RecognizePartCancelTempComponent from '@/views/recognizeReceive/partCancelTemp.vue';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import ImportRecognize from './components/importRecognize.vue';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
const setDetailTab = '-1';
let files = [];
const projectItem = ref({});
const RecognizeCreatedRef = ref();
const RecognizeBatchCreatedRef = ref();
const RecognizePartCancelRef = ref();
const RecognizePartCancelTempRef = ref();
let dataListType = reactive([]);
let selectId = ref('');
let comfileShow = ref(false);
let businessDeptsIdstr = ref('');
let companyId = ref('');
let companyName = ref('');
// let tabCount = ref({
//   //已撤销
//   revokedCount: 0,
//   //待提交
//   waitSubmitCount: 0,
//   //待执行
//   waitexcutedCount: 0,
//   //已完成
//   FineshedCount: 0,
//   //已完成
//   allcount: 0
// });
const statusTypeEnum = [
  {
    id: '-1',
    name: '已撤销'
  },
  {
    id: '0',
    name: '待提交'
  },
  {
    id: '1',
    name: '审批中'
  },
  {
    id: '99',
    name: '已完成'
  }
  // ,
  // {
  //   id: '',
  //   name: '全部'
  // }
];
const options = [
  {
    value: '-1',
    label: '全部'
  },
  {
    value: '100',
    label: '销售回款'
  },
  {
    value: '101',
    label: '预收款'
  },
  {
    value: '102',
    label: '退采购付款'
  },
  {
    value: '104',
    label: '代收款'
  },
  {
    value: '108',
    label: '赔款'
  },
  {
    value: '109',
    label: '收回之前支付的押金'
  },
  {
    value: '110',
    label: '收回之前支付的投标保证金'
  },
  {
    value: '111',
    label: '收回之前支付的履约保证金'
  },
  {
    value: '115',
    label: '工会经费返还'
  },
  {
    value: '116',
    label: '政府补贴'
  },
  {
    value: '117',
    label: '即征即退'
  },
  {
    value: '118',
    label: '个税返还'
  },
  {
    value: '119',
    label: '税费返还'
  },
  {
    value: '120',
    label: '出口退税'
  },
  {
    value: '121',
    label: '所得税退税'
  },
  {
    value: '122',
    label: '保险理赔收入'
  },
  {
    value: '123',
    label: '收到保证金'
  },
  {
    value: '124',
    label: '收到招标押金'
  },
  {
    value: '127',
    label: '收回之前支付的海关保证金'
  },
  {
    value: '129',
    label: '收回之前支付的医院保证金'
  },
  {
    value: '142',
    label: '销售现金折扣'
  },
  {
    value: '145',
    label: '收货款（保证金转）'
  },
  {
    value: '203',
    label: '负数应收'
  },
  {
    value: '240',
    label: '退返利款'
  },
  {
    value: '204',
    label: '退预收款'
  },
  {
    value: '228',
    label: '退回收到的押金'
  },
  {
    value: '229',
    label: '退回收到的保证金'
  },
  {
    value: '998',
    label: '未知'
  },
  {
    value: '999',
    label: '其他'
  }
];

const displayNearOptions = [
  {
    value: 0,
    label: '否'
  },
  {
    value: 1,
    label: '是'
  }
];
const itemClassifys = [
  {
    value: 1,
    label: '货款'
  },
  {
    value: 2,
    label: '暂收款'
  }
];
const saveSureType = () => {
  if (crudReceive.selections.length <= 0) {
    ElMessage({
      showClose: true,
      message: '请至少选择一行收款单数据',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  createdItem();
};
const loadTableData = () => {
  request({
    url: '/api/RecognizeReceive/GetTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
//创建
const createdItem = () => {
  request({
    url: '/api/RecognizeReceive/sureType',
    method: 'POST',
    data: crudReceive.selections
  })
    .then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'success',
          duration: 3 * 1000
        });
        crudReceive.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {
      ElMessage({
        showClose: true,
        message: '操作失败，请联系管理员',
        type: 'error',
        duration: 3 * 1000
      });
    });
};
let createdType = ref(0);
let dataListBusinessDept = reactive([]);
let submitLoading = ref(false);
//获取路由
const router = useRouter();
const route = useRoute();
const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
const functionUris = {
  add: 'metadata://fam/RecognizeReceive-index/functions/add-recognize',
  sureType:
    'metadata://fam/RecognizeReceive-index/functions/sureType-recognize',
  submit: 'metadata://fam/RecognizeReceive-index/functions/submit-recognize',
  del: 'metadata://fam/RecognizeReceive-index/functions/del-recognize',
  revocation: 'metadata://fam/finance-bulkpayment/functions/revocation-payment',
  excute: 'metadata://fam/finance-bulkpayment/functions/excute-payment'
};
let receiveSelDialogShow = ref(false);
let receivesureTypeDialogShow = ref(false);
let createdDialogShow = ref(false);
let batchCreatedDialogShow = ref(false);
let partCancelDialogShow = ref(false);
let partCancelTempDialogShow = ref(false);
let importDialogShow = ref(false);
let currUserName = ref('');
currUserName.value = window.userName;
const crud = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/getlist',
    idField: 'id',
    method: 'post',
    sort: ['createdTime,desc'],
    query: {
      status: '',
      id: '',
      abatedStatus: '',
      username: currUserName.value
    },
    props: {
      // 默认隐藏搜索
      searchToggle: true
    },
    userNames: ['createdBy'],
    crudMethod: {},
    tablekey: 'tablekeyItem', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        loadTabCount();
        //默认选中第一行
        if (crud.data.length) {
          nextTick(() => {
            crud.singleSelection(crud.data[0]);
            selectId.value = crud.data[0].id;
            // crudDetail.toQuery();
            getDetailData(crud.data[0]);
            getReturnCustomer();
            loadRemainingRecognizableAmount();
          });
        } else {
          crudDetail.data = [];
        }
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        if (column.property === 'leftAmount') {
          if (item.value < 0) {
            return -Number(item[column.property]);
          }
        }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
let DeptDialogVisible = ref(false);
const crudDetail = CRUD(
  {
    title: '应用',
    url: '/api/RecognizeReceiveQuery/getdetail',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    tablekey: 'tablekeyDetail',
    query: {},
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const activeName = ref('receive');
const tableReceive = ref<InstanceType<typeof ElTable>>();
const crudReceive = CRUD(
  {
    title: '待认款单',
    url: '/api/RecognizeReceiveQuery/GetReceiveBills',
    method: 'post',
    tablekey: 'crudReceiveStorage',
    query: { opt: 1 },
    props: {
      // 默认隐藏搜索
      searchToggle: true
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crudReceive.data.length) {
          crudReceive.singleSelection(crudReceive.data[0]);
        }
      }
    },
    idField: 'id',
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableReceive
  }
);
//高级检索
const queryListReceive = computed(() => {
  return [
    {
      key: 'code',
      label: '收款单号',
      show: true
    },
    {
      key: 'classify',
      label: '收款类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: options,
      show: false
    },
    {
      key: 'displayNearInt',
      label: '是否显示负数应收',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: displayNearOptions,
      show: false
    },
    {
      key: 'company',
      label: '公司',
      method: 'post',
      type: 'remoteSelect',
      url: `${window.bdsHost}/api/companies/queryItem`,
      multiple: true,
      valueK: 'nameCode',
      labelK: 'companyName',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { functionUri: 'metadata://fam' }
      },

      show: true
    },
    {
      key: 'customerName',
      label: '客户',
      show: true
    },
    //{
    //  key: 'customers',
    //  label: '客户',
    //  type: 'remoteSelect',
    //  multiple: true,
    //  method: 'post',
    //  url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    //  labelK: 'name',
    //  valueK: 'id',
    //  props: {
    //    KeyWord: 'name',
    //    resultKey: 'data.data',
    //    queryData: { functionUri: 'metadata://fam' }
    //  },
    //  show: true
    //},
    {
      key: 'projectNo',
      label: '项目',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/Authmeta`,
      labelK: 'name',
      valueK: 'code',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { status: 2, functionUri: 'metadata://fam' }
      },
      show: true
    },
    {
      key: 'staratDateStamp',
      endDate: 'endDateStamp',
      label: '收款日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    },
    {
      key: 'payeeBankName',
      label: '收款人银行类型',
      show: true
    },
    {
      key: 'accountBank',
      label: '收款人银行账户',
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      // type: 'remoteSelect',
      // method: 'post',
      // url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`,
      // valueK: 'businessUnitID',
      // labelK: 'businessUnitName',
      // props: { KeyWord: 'nameLike', resultKey: 'data.data' },
      show: true
    }
  ];
});
const clickdetailRow = (e) => {
  crudDetail.singleSelection(e);
};
const classify = ref(1);
const createRecognizeReceive = (classifyOpt) => {
  if (crudReceive.selections.length <= 0) {
    ElMessage({
      showClose: true,
      message: '请至少选择一行收款单数据',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  DeptDialogVisible.value = true;
  classify.value = classifyOpt;
};
let newDepart = reactive({
  id: '',
  name: '',
  path: '',
  fullName: '',
  item: {}
});
const innoDepartmentSelectRef = ref();
// 递归方法检查树结构中是否存在指定的parentId
const checkIfParentExists = (tree, targetParentId) => {
  let resultData = {
    result: false,
    data: {}
  };
  // 遍历当前层级的每个节点
  for (const node of tree) {
    // 如果找到了匹配的parentId，返回true
    if (node.id === targetParentId) {
      return (resultData = {
        result: true,
        data: node
      });
    }
    // 如果当前节点有子节点，继续递归检查子节点
    if (node.children && node.children.length > 0) {
      resultData = checkIfParentExists(node.children, targetParentId);
      // 只要有一个分支返回true，整个结果就为true
      if (resultData.result) break;
    }
  }
  return resultData;
};
const createOpt = () => {
  if (newDepart.id == undefined || newDepart.id == '') {
    ElMessage({
      showClose: true,
      message: '请选择核算部门',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let parentData = {
    data: {
      deptShortName: null
      // extraInfo: {
      //   deptShortName: null
      // }
    }
  };
  if (
    // newDepart.item.extraInfo.deptShortName === null &&
    // newDepart.fullName.indexOf('/') != -1
    newDepart.item.deptShortName === null &&
    newDepart.fullName.indexOf('/') != -1
  ) {
    parentData = checkIfParentExists(
      innoDepartmentSelectRef.value.list,
      newDepart.item.parentId
    );
    // 限制20次递归
    let i = 20;
    // parentData.data.extraInfo.deptShortName == null && i > 0
    while (parentData.data.deptShortName == null && i > 0) {
      parentData = checkIfParentExists(
        innoDepartmentSelectRef.value.list,
        parentData.data.parentId
      );
      i--;
    }
  }
  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  let param = {
    businessDeptFullName: newDepart.fullName,
    businessDeptFullPath: newDepart.path,
    businessDeptId: newDepart.id,
    recognizeReceiveInfos: crudReceive.selections,
    classify: classify.value,
    // businessArea:
    //   parentData?.data?.extraInfo.deptShortName ||
    //   newDepart?.item?.extraInfo.deptShortName ||
    //   ''
    businessArea:
      parentData?.data?.deptShortName ||
      newDepart?.item?.deptShortName ||
      ''
  };
  request({
    url: '/api/RecognizeReceive/created',
    method: 'POST',
    data: param
  })
    .then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '保存成功',
          type: 'success',
          duration: 3 * 1000
        });
        activeName.value = 'recognize';
        crud.toQuery();
        DeptDialogVisible.value = false;
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      loading.close();
    })
    .catch((t) => {
      loading.close();
    });
};
const classifyType = ref();
const getDetailData = (e) => {
  // console.log('点击行事件赋值并调用详细信息=========', JSON.stringify(e));
  crudDetail.query.id = e.id;
  selectId.value = e.id;
  businessDeptsIdstr.value = e.businessDept_IdPaths;
  companyId.value = e.companyId;
  companyName.value = e.companyName;
  //货款,暂收款
  crudDetail.query.classify = e.classify;
  classifyType.value = e.classify;
  crudDetail.toQuery();
  // console.log('crudDetail.data:', JSON.stringify(crudDetail.data));
  //盘点校验
  //InventoryCheck();
};
const exportLoading = ref(false);
const importExcel = (e) => {
  exportLoading.value = true;
  InventoryCheck()
    .then((result) => {
      if (result) {
        // 返回值为true
        importDialogShow.value = true;
      } else {
        exportLoading.value = false;
        // 返回值为false
        ElMessage({
          showClose: true,
          message:
            '操作失败，原因：' +
            companyName.value +
            '存在未完成的盘点数据，不能认款',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
    })
    .catch((error) => {
      exportLoading.value = false;
      console.error(error);
    });
};
let tabCount = ref({
  waitSubmitCount: 0,
  auditingCount: 0,
  waitExecuteCount: 0,
  completedCount: 0,
  partCancel: 0,
  allCount: 0
});
const closeImportDialogCallBack = () => {
  importDialogShow.value = false;
  exportLoading.value = false;
};
onMounted(() => {
  //获取项目类型
  getTreeList('', 'ProjectType').then((res) => {
    dataListType.push(...res.data.data[0].children);
  });
  // 获取事业部
  getDepartTree('metadata://fam').then((res) => {
    const getid = (list) => {
      return list.map((i) => {
        return {
          // ...i,
          // disabled: i.extraInfo.disabled ?? false,
          // _id: i.id + '_' + i.name + '_' + i.extraInfo.deptShortName,
          // children: i.extraInfo.children
          //   ? getid(i.extraInfo.children)
          //   : undefined
          ...i,
          disabled: i.disabled ?? false,
          _id: i.id + '_' + i.name + '_' + i.deptShortName,
          children: i.children
            ? getid(i.children)
            : undefined
        };
      });
    };
    const tree1 = res.data.data;
    dataListBusinessDept.push(...getid(tree1));
    // 表头拖拽必须在这里执行
    tableDrag(tableItem, crud.tablekey);
    tableDrag(tableDetail, crudDetail.tablekey);
  });
  if (route.query && route.query.id) {
    crud.query.id = route.query.id;
  }
  crudReceive.toQuery();
  //crud.toQuery();
});
const props = defineProps({
  __refresh: Boolean
});
onActivated(() => {
  if (props.__refresh) {
    receiveSelDialogShow.value = false;
    crud.toQuery();
  }
});
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
//高级检索
const queryList = computed(() => {
   let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId'
    }
  );
  return [
    {
      key: 'code',
      label: '认款单号',
      show: true
    },
    {
      key: 'receivedetailcode',
      label: '明细单号',
      show: true
    },
    {
      key: 'receivecode',
      label: '收款单号',
      show: true
    },
    {
      key: 'projectName',
      label: '项目名称',
      show: true
    },
    ...items,
    // {
    //   key: 'companyId',
    //   label: '公司',
    //   type: 'remoteSelect',
    //   method: 'post',
    //   url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    //   labelK: 'name',
    //   valueK: 'id',
    //   props: { KeyWord: 'name', resultKey: 'data.data' }
    // },
    // {
    //   key: 'department',
    //   label: '核算部门',
    //   type: 'departmentSelect',
    //   //options: dataListBusinessDept,
    //   props: {
    //     queryData: { functionUri: 'metadata://fam' },
    //     allSelectable: true
    //   },
    //   show: true
    // },
    {
      key: 'createDateS',
      endDate: 'createDateE',
      label: '创建日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    },
    // {
    //   key: 'status',
    //   label: '状态',
    //   type: 'select',
    //   labelK: 'name',
    //   valueK: 'id',
    //   dataList: statusTypeEnum,
    //   show: false
    // },
    {
      key: 'customers',
      label: '客户',
      type: 'remoteSelect',
      multiple: true,
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
      labelK: 'name',
      valueK: 'id',
      props: {
        KeyWord: 'name',
        resultKey: 'data.data',
        queryData: { functionUri: 'metadata://fam' }
      },
      show: true
    },
    {
      key: 'itemClassify',
      label: '类型',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: itemClassifys,
      show: false
    },
    {
      key: 'receiveDateS',
      endDate: 'receiveDateE',
      label: '收款日期',
      type: 'daterange',
      formart: 'YYYY-MM-DD',
      defaultTime: [
        new Date(2000, 1, 1, 0, 0, 0),
        new Date(2000, 2, 1, 23, 59, 59)
      ],
      show: true
    },
    {
      key: 'createdBy',
      label: '创建人',
      method: 'post',
      multiple: true,
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
      placeholder: '用户名称搜索',
      valueK: 'name',
      labelK: 'displayName',
      props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
      slots: {
        option: ({ item }) => (
          <>
            <span>{item.displayName}</span>
            <span style="float:right">{item.name}</span>
          </>
        )
      }
    },
    {
      key: 'customerName',
      label: '实际客户',
      show: true
    },
    {
      key: 'hospitalName',
      label: '终端客户',
      show: true
    },
    {
      key: 'classify',
      label: '收款类型',
      type: 'select',
      labelK: 'label',
      valueK: 'label',
      dataList: options,
      show: false
    },
    {
      key: 'settletype',
      label: '结算方式',
      show: true
    },
    {
      key: 'transferStatus',
      label: '转货款状态',
      type: 'select',
      labelK: 'label',
      valueK: 'value',
      dataList: [
        { label: '已转货款', value: 1 },
        { label: '未转货款', value: 0 }
      ],
      show: false
    },
  ];
});
//合计
const total = (arr) => {
  var result = 0;
  arr.forEach((element) => {
    result += element.sum;
  });
  return result;
};
//合计2
const totaldetail = (arr) => {
  var result = 0;
  arr.forEach((element) => {
    result += element.value;
  });
  return result;
};
//提交
const submit = (row) => {
  if (crudDetail.data.length == 0) {
    ElMessage({
      showClose: true,
      message: '操作失败：原因：没有明细数据！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  InventoryCheck()
    .then((result) => {
      if (result) {
        // 返回值为true
        if (row.classify != 2) {
          var filterLenth = crudDetail.data.filter(
            (p) => p.customerId.toLowerCase() == row.customerId.toLowerCase()
          ).length;
          if (filterLenth != crudDetail.data.length) {
            if (!isReturnCustomer.value) {
              ElMessage({
                showClose: true,
                message: '非第三方回款客户与实际客户部不一致，不能提交',
                type: 'error',
                duration: 3 * 1000
              });
              return;
            }
            ElMessageBox.confirm(
              '该认款单的客户与实际客户部不一致，请确认后操作！',
              '操作提醒',
              {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(() => {
                submitOpt(row);
              })
              .catch(() => {});
          } else {
            submitOpt(row);
          }
        } else {
          submitOpt(row);
        }
      } else {
        // 返回值为false
        ElMessage({
          showClose: true,
          message:
            '操作失败，原因：' +
            companyName.value +
            '存在未完成的盘点数据，不能认款',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
    })
    .catch((error) => {
      console.error(error);
    });
};
const submitOpt = (row) => {
  var id = row.id;
  selectId.value = id;
  submitLoading.value = true;
  request({
    url: '/api/RecognizeReceive/submit',
    method: 'POST',
    params: { id: id }
  })
    .then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '提交成功',
          type: 'success',
          duration: 3 * 1000
        });
        submitLoading.value = false;
        crud.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        submitLoading.value = false;
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};
//撤回
const revocation = (row) => {
  var id = row.id;
  selectId.value = id;
  submitLoading.value = true;
  request({
    url: '/api/BatchPayment/CancelPaymentAutoItem',
    method: 'PUT',
    params: { id: id }
  }).then((res) => {
    if (res.data.code == '200') {
      ElMessage({
        showClose: true,
        message: '撤回成功',
        type: 'success',
        duration: 3 * 1000
      });
      submitLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      submitLoading.value = false;
    }
  });
};
//执行
const excute = (row) => {
  var id = row.id;
  selectId.value = id;
  submitLoading.value = true;
  request({
    url: '/api/BatchPayment/ExcutePaymentAutoItem',
    method: 'POST',
    params: { id: id }
  }).then((res) => {
    if (res.data.code == '200') {
      ElMessage({
        showClose: true,
        message: '执行成功',
        type: 'success',
        duration: 3 * 1000
      });
      submitLoading.value = false;
      crud.toQuery();
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      submitLoading.value = false;
    }
  });
};
const RecognizeSelectRef = ref();
const RecognizeSureTypeRef = ref();
//认款
const receiveSelDialog = (val) => {
  receiveSelDialogShow.value = true;
};
const receivesureTypeDialog = (val) => {
  receivesureTypeDialogShow.value = true;
};
//是否显示弹窗
const createdDialog = (val) => {
  console.log('============='+JSON.stringify(crudDetail.data))
  createdType.value = val;
  if (val === 2) {
    RecognizeCreatedRef.value.getDetail(crudDetail.rowData.id);
    createdDialogShow.value = true;
  } else {
    InventoryCheck()
      .then((result) => {
        if (result) {
          // 返回值为true
          createdDialogShow.value = true;
        } else {
          // 返回值为false
          ElMessage({
            showClose: true,
            message:
              '操作失败，原因：' +
              companyName.value +
              '存在未完成的盘点数据，不能认款',
            type: 'warning',
            duration: 3 * 1000
          });
          return;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  }
};
//批量添加
  const batchCreatedDialog = () => {
  InventoryCheck()
    .then((result) => {
      if (result) {
        // 返回值为true
        console.log(
          'batchCreatedDialogShow.value:' + batchCreatedDialogShow.value
        );
        batchCreatedDialogShow.value = true;
      } else {
        // 返回值为false
        ElMessage({
          showClose: true,
          message:
            '操作失败，原因：' +
            companyName.value +
            '存在未完成的盘点数据，不能认款',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
    })
    .catch((error) => {
      console.error(error);
    });
};
const closeDialogCallBack = () => {
  receiveSelDialogShow.value = false;
  receivesureTypeDialogShow.value = false;
};
const closeCreatedDialogCallBack = () => {
  createdDialogShow.value = false;
};
const closeBatchCreatedDialogCallBack = () => {
  batchCreatedDialogShow.value = false;
};
const partCancelDialog = () => {
  partCancelDialogShow.value = true;
};
const closePartCancelDialogCallBack = () => {
  partCancelDialogShow.value = false;
};
const cancelTempDeatail = () => {
  partCancelTempDialogShow.value = true;
}
const closePartCancelTempDialogCallBack = () => {
  partCancelTempDialogShow.value = false;
};
const refreshIndexCallBack = () => {
  crudDetail.toQuery();
  exportLoading.value = false;
};
const refreshDetailCallBack = () => {
 /* crud.toQuery();*/
  crudDetail.toQuery();
};
//删除单据
const deleteItem = (row) => {
  var id = row.id;
  ElMessageBox.confirm('此操作将删除当前单据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/RecognizeReceive/item/delete',
      method: 'DELETE',
      params: { id: id }
    }).then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '单据删除成功',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      crud.toQuery(); //刷新页面
    });
  });
};
const tabhandleClick = (tabName) => {
  if (tabName == 'recognize') {
    crud.toQuery();
  } else if (tabName == 'receive') {
    crudReceive.toQuery();
  }else if (tabName == 'agentRefundAbatements') {
    crudAgentRefundAbatements.toQuery();
  }
};
const tabhandleChildClick = (tabName) => {
  crud.query.status = tabName;
  crud.toQuery();
};
const cancelItem = (row) => {
  var id = row.id;
  ElMessageBox.confirm('此操作将撤销当前认款, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    request({
      url: '/api/RecognizeReceive/cancelreceive',
      method: 'post',
      params: { itemId: id }
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
        crud.toQuery(); //刷新页面
      })
      .finally(() => {
        loading.close();
      });
  });
};
//导出数据
const exportBillLoading = ref(false);
const exportBill = () => {
  ElMessageBox.confirm('是否导出符合条件的所有数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    exportBillLoading.value = true;
    return request({
      url: '/api/RecognizeReceiveQuery/Export',
      data: crud.query,
      method: 'POST',
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
      responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
    })
      .then((res) => {
        const xlsx =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
        const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
        a.download = '导出认款单文件' + new Date().getTime() + '.xlsx';
        a.href = window.URL.createObjectURL(blob);
        a.click();
        a.remove();
      })
      .catch((err) => {
        throw '请求错误';
      })
      .finally(() => {
        exportBillLoading.value = false;
      });
  });
}
//删除明细
const deleteDetail = (row) => {
  var ids = crudDetail.selections.map((r) => {
    return r.id;
  });

  ElMessageBox.confirm('此操作将删除当前明细, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/RecognizeReceive/delete',
      method: 'DELETE',
      data: { ids: ids, recognizeReceiveItemId: row.id }
    }).then((res) => {
      if (res.data.code == '200') {
        ElMessage({
          showClose: true,
          message: '明细删除成功',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      crudDetail.toQuery(); //刷新页面
    });
  });
};
//附件
let flieList = ref('');
let comfile_upload = ref(false);
const openAttachment = (row) => {
  flieList = ref('');
  comfile_upload.value = true;
};
const handleClosefile = () => {
  comfile_upload.value = false;
};
let recognizeReceiveItemId = ref('');
const comfile_show = ref(false);
const showfiles = ref([]);
const showAttachFile = (showAttachFileids, id) => {
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    recognizeReceiveItemId.value = id;
    request({
      url: `/api/RecognizeReceiveQuery/GetAttachFile`,
      method: 'POST',
      data: {
        recognizeReceiveItemId: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show.value = true;
          showfiles.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'success',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};
const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
};
const deleteFile = (fileid) => {
  ElMessageBox.confirm('是否确定?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: `/api/RecognizeReceive/DeleteAttachFileIds`,
      method: 'POST',
      data: {
        recognizeReceiveItemId: recognizeReceiveItemId.value,
        attachFileId: fileid
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          if (res.data.data == '' || res.data.data.length == 0) {
            crud.toQuery();
            comfile_show.value = false;
          } else {
            showAttachFile(res.data.data, recognizeReceiveItemId.value);
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  });
};
const savefile = () => {
  if (flieList.value == '' || flieList.value.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：请上传文件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  request({
    url: `/api/RecognizeReceive/AttachFileIds`,
    method: 'POST',
    data: {
      recognizeReceiveItemId: crud.selections[0].id,
      AttachFileIds: flieList.value
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        comfile_upload.value = false;
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: '保存成功!',
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    });
};
const downloadFile = (code) => {
  request({
    url: '/api/RecognizeReceiveQuery/GetKDFilePath?code=' + code,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          window.open(res.data.data[0].previewAddress);
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶回执单，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
//转货款
const transfer = (row) => {
  ElMessageBox.confirm('此操作将转货款, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    request({
      url: '/api/RecognizeReceive/Transfer',
      method: 'POST',
      data: { id: crud.selections[0]?.id }
    }).then((res) => {
      if (res.data.code === 200) {
        crud.toQuery();
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'success',
          duration: 3 * 1000
        });
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    });
  });
};
//盘点期间校验
const InventoryCheck = () => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/Inventory/InventoryCheck?companyId=' + companyId.value,
      method: 'POST'
    })
      .then((res) => {
        if (res.data.code !== 200) {
          resolve(false);
        } else {
          resolve(true);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};
//查询客户是否为第三方回款客户
let isReturnCustomer = ref(false);
const getReturnCustomer = () => {
  if (crud.rowData.customerId !== undefined && crud.rowData.customerId !== '') {
    request({
      url:
        '/api/RecognizeReceive/isReturnCustomer?customerId=' +
        crud.rowData.customerId,
      method: 'POST'
    }).then((res) => {
      if (res.data.code !== 200) {
        isReturnCustomer.value = false;
      } else {
        console.log(JSON.stringify(res.data));
        if (res.data.data.isPaymentInstitution === 1) {
          isReturnCustomer.value = true;
        } else {
          isReturnCustomer.value = false;
        }
      }
    });
  } else {
    isReturnCustomer.value = false;
  }
};
// 保存（付款时间）
const saveDateLoading = ref(false);
const saveDate = () => {
   saveDateLoading.value = true;
   request({
      url: '/api/RecognizeReceive/SaveBackDateTime',
      method: 'POST',
      data: { details: crudDetail.data }
   })
   .then((res) => {
    if (res.data.code === 200) {
      crudDetail.toQuery();
      ElMessage({
        showClose: true,
        message: '操作成功!',
        type: 'success',
        duration: 3 * 1000
      });
      saveDateLoading.value = false;
    } else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      saveDateLoading.value = false;
    }
   })
     .finish(() => {
      saveDateLoading.value = false;
     });
}

const ImportDetail = ref();
//导入
const openImportModel = () => {
  ImportDetail.value.detailImportModel.importDetailInput.recognizeReceiveItemId = crud.rowData.id
  ImportDetail.value.detailImportModel.importDetailInput.isReturnCustomer = isReturnCustomer.value;
  ImportDetail.value.detailImportModel.showDialog = true;
}
//导入成功回调
const onImportSuccess = async (datas: any) => {
  crud.toQuery();
};
//获取页签数量
const loadTabCount = () => {
  request({
    url: '/api/RecognizeReceiveQuery/getTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
}
//获取可认款金额
const loadRemainingRecognizableAmount = () => {
  request({
    url: '/api/RecognizeReceiveQuery/getRemainingRecognizableAmountByCodes',
    data: crud.data.map(x=>x.code),
    method: 'post'
  }).then((res) => {
    if(res.data.code === 200) {
      console.log(JSON.stringify(res.data.data));
      // 创建一个以 res.data.data 中的 code 为键，aba 为值的映射对象
      const resMap = {};
      res.data.data.forEach(item => {
        resMap[item.code] = item.remainingRecognizableAmount;
      });
      // 遍历 crud.data，根据 code 找到对应的 aba 值并赋值给 value
      crud.data.forEach(item => {
        if (resMap[item.code]) {
          item.remainingRecognizableAmount = resMap[item.code];
        }
      });
    }
  });
}

const isAllExpanded = ref(false);
// 切换全部展开/折叠
const toggleAllExpansion = async () => {
  await nextTick() // 确保DOM更新
  
  if (!tableDetail.value || !crudDetail.data?.length) {
    console.warn('表格未初始化或数据为空')
    return
  }

  crudDetail.data.forEach(row => {
    if (row.typeDescription !== '初始应收') {
      tableDetail.value.toggleRowExpansion(row, !isAllExpanded.value)
    }
  })
  
  isAllExpanded.value = !isAllExpanded.value
}

let downLoading = ref(false);
//协调服务导出
const downloadAsync = (
  url: String,
  fileName: String,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: {
      id: crud.rowData.id
    },
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
  .then((res) => {
    if (res.data.code === 200) {
      ElMessage({
        showClose: true,
        message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
        type: 'success',
        duration: 3 * 1000
      });
      downLoading.value = false;
      return true;
    }
    else {
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      downLoading.value = false;
      return false;
    }
  })
  .catch((t) => {
    downLoading.value = false;
  });
};

const queryAgentRefundAbatementsList = computed(() => {
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId'
    }
  );
  return [
    {
      key: 'debtCode',
      label: '应付单号',
      show: true
    }, 
    {
      key: 'receivecode',
      label: '收款单号',
      show: true
    }, 
    ...items,
    // {
    //   key: 'department',
    //   label: '核算部门',
    //   type: 'departmentSelect', 
    //   props: {
    //     queryData: { functionUri: 'metadata://fam' },
    //     allSelectable: true
    //   },
    //   show: true
    // }, 
    // {
    //   key: 'companyId',
    //   label: '公司',
    //   type: 'remoteSelect',
    //   method: 'post',
    //   url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    //   labelK: 'name',
    //   valueK: 'id',
    //   props: { KeyWord: 'name', resultKey: 'data.data' }
    // }, 
    {
      key: 'agentId',
      label: '供应商',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data' }
    },
    {
    key: 'projectNo',
    label: '项目',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/Authmeta`,
    labelK: 'name',
    valueK: 'code',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { status: 2, functionUri: 'metadata://fam' }
    },
    show: true
  },
  ];
});
const tableAgentRefundAbatements = ref<InstanceType<typeof ElTable>>();
const crudAgentRefundAbatements = CRUD(
  {
    title: '供应商退款冲销表',
    url: '/api/RecognizeReceiveQuery/GetAgentRefundAbatements',
    method: 'post',
    tablekey: 'crudAgentRefundAbatements',
    query: { opt: 1 },
    props: {
      // 默认隐藏搜索
      searchToggle: true
    },
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crudAgentRefundAbatements.data.length) {
          crudAgentRefundAbatements.singleSelection(crudAgentRefundAbatements.data[0]);
        }
      }
    },
    idField: 'id',
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableAgentRefundAbatements
  }
);
//下载
const downloadAgentRefundAbatementsDownLoad = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportAgentRefundAbatementsDownLoad(crudAgentRefundAbatements.query, '供应商退款冲销');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportAgentRefundAbatementsDownLoad = async (data, filename) => {
  await request({
    url: '/api/RecognizeReceiveQuery/AgentRefundAbatementsDownLoad',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};
</script>
<style scoped lang="scss">
.app-page-tabs {
  flex: 1;
  :deep(.el-tabs__content) {
    display: flex;
    .el-tab-pane {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
}
</style>
