﻿
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 运营制作开票
    /// </summary>
    public interface ICustomizeInvoiceQueryService
    {
        /// <summary>
        /// 获取原始开票明细查询
        /// </summary>
        Task<(List<OriginDetailOutput>, int)> GetOriginDetailAsync(OriginDetailQueryInput input);

        /// <summary>
        /// 获取开票单列表
        /// </summary>
        Task<(List<CustomizeInvoiceItemOutput>, int)> GetCustomizeInvoiceItem(CustomizeInvoiceItemQueryInput input);
        /// <summary>
        /// 获取开票单列表
        /// </summary>
        Task<(List<CustomizeInvoiceDetailOutput>, int)> GetCustomizeInvoiceDetail(CustomizeInvoiceDetailQueryInput input);
        /// <summary>
        /// 根据订单号获取开票单列表
        /// </summary>
        Task<(List<CustomizeInvoiceDetailOutput>, int)> GetCustomizeInvoiceDetailByOrderNo(string orderNo);

        /// <summary>
        /// 根据Id查询
        /// </summary>
        Task<CustomizeInvoiceItem> GetById(Guid id);

        Task<CustomizeInvoiceItemTabOutput> GetTabCountAsync(CustomizeInvoiceItemQueryInput input);

        /// <summary>
        /// 获取客户邮箱
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> GetCustomerEmail([FromBody] CustomizeInvoiceItemQueryInput input);

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<CustomizeInvoiceItemDownLoadOutput>>> DownLoadAsync(CustomizeInvoiceClassifyQueryInput input);
        /// <summary>
        /// 开票分类单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<CustomizeInvoiceClassifyOutput>, int)> GetCustomizeInvoiceClassify(CustomizeInvoiceClassifyQueryInput input);
        Task<CustomizeInvoiceItemTabOutput> GetClassifyTabCountAsync(CustomizeInvoiceClassifyQueryInput input);

        /// <summary>
        /// 获取附件id集合
        /// </summary>
        /// <param name="input"></param>
        /// <param name="createdBy"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<string>>> GetAttachFileIds(CustomizeInvoiceClassifyQueryInput input);

        /// <summary>
        /// 获取预开票列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        Task<(List<PreCustomizeInvoiceListOutput>, int)> GetPreCustomizeInvoiceList(PreCustomizeInvoiceListInput input);

        /// <summary>
        /// 获取预开票列表页签数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<PreCustomizeInvoiceListTabOutput>> GetPreCustomizeInvoiceTabCount(PreCustomizeInvoiceListInput query);

        /// <summary>
        /// 获取预开票明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        Task<(List<PreCustomizeInvoiceDetailsOutput>, int)> GetPreCustomizeInvoiceDetails(PreCustomizeInvoiceDetailsInput input);

        /// <summary>
        /// 提交预开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> CreatePreCustomizeInvoice(SubmitPreCustomizeInvoiceInput input);

        /// <summary>
        /// 删除预开票申请
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeletePreCustomizeInvoice(Guid id);

        /// <summary>
        /// 导入预开票明细
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> ExportPreCustomizeInvoiceDetails(IFormFile file, Guid? id);

        /// <summary>
        /// 导入预开票明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SubmitPreCustomizeInvoice(Guid? id);

        /// <summary>
        /// 导入初始应收开票
        /// </summary>
        /// <param name="file"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> ExportInitCredit(IFormFile file,string? userName);
        /// <summary>
        /// 导出主单加明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<MemoryStream> ExportCustomizeInvoiceItemAndDetail(OriginDetailQueryInput query);

        /// <summary>
        /// 获取页面选中的主单导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<MemoryStream> ExportCustomizeInvoiceItemByIds(OriginDetailQueryInput query);
        /// <summary>
        /// 运营制作开票协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportCustomizeInvoiceTask(CreditQueryInput query);


    }
}
