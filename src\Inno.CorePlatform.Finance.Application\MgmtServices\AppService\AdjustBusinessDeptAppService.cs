using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 实现 IAdjustBusinessDeptAppService 接口，提供与业务部门调整相关的查询服务。
    /// </summary>
    public class AdjustBusinessDeptAppService : IAdjustBusinessDeptAppService
    {
        private readonly FinanceDbContext _db;

        /// <summary>
        /// 构造函数，用于初始化数据库上下文。
        /// </summary>
        /// <param name="db">FinanceDbContext 实例，用于访问数据库。</param>
        public AdjustBusinessDeptAppService(FinanceDbContext db)
        {
            _db = db;
        }

        /// <summary>
        /// 根据项目ID列表获取对应的识别收款项。
        /// </summary>
        /// <param name="projectIds">项目ID的列表。</param>
        /// <returns>与给定项目ID关联的识别收款项的列表。</returns>
        public async Task<List<InTransitOrderOutput>> GetRecognizeReceiveItemsByProjectIdsAsync(List<Guid> projectIds)
        {
            return await (from rri in _db.RecognizeReceiveItems
                          join rrd in _db.RecognizeReceiveDetails on rri.Id equals rrd.RecognizeReceiveItemId
                          join rrdc in _db.RecognizeReceiveDetailCredits on rrd.Id equals rrdc.RecognizeReceiveDetailId
                          join c in _db.Credits on rrdc.CreditCode equals c.BillCode
                          where (rri.Status == RecognizeReceiveItemStatusEnum.WaitSubmit || 
                                 rri.Status == RecognizeReceiveItemStatusEnum.Auditing) &&
                                c.ProjectId.HasValue && 
                                projectIds.Contains(c.ProjectId.Value)
                          select new InTransitOrderOutput
                          {
                              OrderNo = rri.Code,
                              Status = (int)rri.Status,
                              CreatedBy = rri.CreatedBy,
                              Type = 2,
                              ProjectId = c.ProjectId.Value
                          }).Distinct().ToListAsync();
        }

        /// <summary>
        /// 根据项目ID列表获取对应的自动付款项。
        /// </summary>
        /// <param name="projectIds">项目ID的列表。</param>
        /// <returns>与给定项目ID关联的自动付款项的列表。</returns>
        public async Task<List<InTransitOrderOutput>> GetPaymentAutoItemsByProjectIdsAsync(List<Guid> projectIds)
        {
            return await (from p in _db.PaymentAutoItems
                          join pd in _db.PaymentAutoDetails on p.Id equals pd.PaymentAutoItemId
                          join dd in _db.DebtDetails on pd.DebtDetilId equals dd.Id
                          join d in _db.Debts on dd.DebtId equals d.Id
                          where (p.Status == PaymentAutoItemStatusEnum.Auditing || p.Status == PaymentAutoItemStatusEnum.WaitExecute)&&d.ProjectId.HasValue && projectIds.Contains(d.ProjectId.Value)
                          select new InTransitOrderOutput
                          {
                              OrderNo = p.Code,
                              Status =(int) p.Status,
                              CreatedBy = p.CreatedBy,
                              Type = 1,
                              ProjectId = d.ProjectId.Value
                          }).Distinct().ToListAsync();
        }
    }
}