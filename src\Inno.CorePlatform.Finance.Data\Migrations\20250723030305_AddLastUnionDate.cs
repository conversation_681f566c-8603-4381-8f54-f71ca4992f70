﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Inno.CorePlatform.Finance.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddLastUnionDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "BillDate",
                table: "PreCustomizeInvoiceItem",
                type: "date",
                nullable: false,
                comment: "日期",
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldComment: "日期");

            migrationBuilder.AddColumn<DateTime>(
                name: "LastUnionDate",
                table: "PreCustomizeInvoiceItem",
                type: "date",
                nullable: true,
                comment: "最后关联日期");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastUnionDate",
                table: "PreCustomizeInvoiceItem");

            migrationBuilder.AlterColumn<DateTime>(
                name: "BillDate",
                table: "PreCustomizeInvoiceItem",
                type: "datetime2",
                nullable: false,
                comment: "日期",
                oldClrType: typeof(DateTime),
                oldType: "date",
                oldComment: "日期");
        }
    }
}
