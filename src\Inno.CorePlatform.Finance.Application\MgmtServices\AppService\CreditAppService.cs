﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Sell.Application.Extensions;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Pipelines.Sockets.Unofficial.Arenas;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{

    public class CreditAppService : BaseAppService, ICreditAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICreditRepository _creditRepository;
        private readonly IAbatementQueryService _abatementQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IBaseAllQueryService<CreditPo> _creditQueryService;
        private readonly IBaseAllQueryService<InvoiceCreditPo> _invoiceCreditQueryService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IConfiguration _configuration;
        private readonly CorePlatform.Common.Clients.Interfaces.ICoordinateClient _coordinateclient;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService; 
        private readonly IAppServiceContextAccessor _appServiceContextAccessor; 
        public CreditAppService(
            ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            IAbatementQueryService abatementQueryService,
            IKingdeeApiClient kingdeeApiClient,
            ISubLogService subLogService,
            IUnitOfWork unitOfWork,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IConfiguration configuration,
            CorePlatform.Common.Clients.Interfaces.ICoordinateClient coordinateclient,
            IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IBaseAllQueryService<CreditPo> creditQueryService,
            FinanceDbContext db, 
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
            IBaseAllQueryService<InvoiceCreditPo> invoiceCreditQueryService, 
            IDebtDetailRepository debtDetailRepository)
            : base(creditRepository, debtRepository, subLogService, unitOfWork, deDispatcher, contextAccessor)
        { 
            _bDSApiClient = bDSApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _unitOfWork = unitOfWork;
            _creditRepository = creditRepository;
            _abatementQueryService = abatementQueryService;
            _kingdeeApiClient = kingdeeApiClient;
            _debtDetailRepository = debtDetailRepository;
            _creditQueryService = creditQueryService;
            _invoiceCreditQueryService = invoiceCreditQueryService;
            _inventoryQueryService = inventoryQueryService;
            _appServiceContextAccessor = contextAccessor;
            _configuration = configuration;
            _coordinateclient = coordinateclient; 
            _db = db;
        }
        /// <summary>
        /// 确认收入
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(int, string)> ConfirmReceipt(ConfirmReceiptInput query)
        {
            if (query.CreditId.HasValue)
            {
                try
                {
                    var credit = await _creditRepository.GetWithNoTrackAsync(query.CreditId.Value);
                    if (credit != null)
                    {
                        DateTime now = DateTime.Now;
                        DateTime newDateTime = new DateTime(now.Year, now.Month, now.Day, now.Hour + 8, now.Minute, now.Second);
                        var data = new List<IncomeIsConfirmData>();
                        data.Add(new IncomeIsConfirmData()
                        {
                            date = newDateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            billno = credit.BillCode,
                        });
                        var incomeIsConfirm = new KingdeeIncomeIsConfirm() { data = data, jfzx_iscofirm = true };
                        var king = await _kingdeeApiClient.PushIncomeIsCofirm(incomeIsConfirm);
                        credit.IsSureIncome = 1;
                        credit.IsSureIncomeDate = newDateTime;
                        credit.UpdatedBy = query.UpdatedBy;
                        credit.UpdatedTime = DateTime.Now;
                        var count = await _creditRepository.UpdateAsync(credit);
                        return (count, "确认成功");
                    }
                    else
                    {
                        throw new Exception("未找到应收单");
                    }
                }
                catch (Exception)
                {
                    throw;
                }
            }
            else
            {
                throw new Exception("参数错误，未传输应收单id");
            }
        }

        /// <summary>
        /// 反向确认
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(int, string)> ReverseConfirm(ConfirmReceiptInput query)
        {
            if (query.CreditId.HasValue)
            {
                try
                {
                    var credit = await _creditRepository.GetWithNoTrackAsync(query.CreditId.Value);
                    var (list, count_a) = await _abatementQueryService.GetListAsync(new AbatementQueryInput() { CreditBillCode = credit.BillCode });
                    if (count_a > 0)
                    {
                        return (0, "有冲销信息不能反向确认");
                    }

                    if (credit != null)
                    {
                        DateTime now = DateTime.Now;
                        DateTime newDateTime = new DateTime(now.Year, now.Month, now.Day, now.Hour + 8, now.Minute, now.Second);
                        var data = new List<IncomeIsConfirmData>();
                        data.Add(new IncomeIsConfirmData()
                        {
                            date = newDateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            billno = credit.BillCode,
                        });
                        var incomeIsConfirm = new KingdeeIncomeIsConfirm() { data = data, jfzx_iscofirm = false };
                        var king = await _kingdeeApiClient.PushIncomeIsCofirm(incomeIsConfirm);
                        if (king != null && king.Code == CodeStatusEnum.Success)
                        {
                            credit.IsSureIncome = 0;
                            credit.IsSureIncomeDate = newDateTime;
                            credit.UpdatedBy = query.UpdatedBy;
                            credit.UpdatedTime = DateTime.Now;
                            var count = await _creditRepository.UpdateAsync(credit);
                            return (count, "反向确认成功");
                        }
                        else if (king != null)
                        {
                            return (0, king.Message);
                        }
                        else { return (0, "请求金蝶系统失败"); }
                    }
                    else { return (0, "没找到应收单"); }
                }
                catch (Exception)
                {
                    throw;
                }
            }
            else { return (0, "没找到应收单"); }
        }

        public override Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            throw new NotImplementedException();
        }

        public async Task<BaseResponseData<int>> ConfirmCreditByStoreOutCodes(List<StoreOutSignDTO> storeOutData)
        {
            if (storeOutData.Count == 0)
            {
                throw new Exception("请传入正确的单号");
            }
            var storeoutCodes = storeOutData.Select(p => p.storeOutCode);
            //var credits = await _creditRepository.GetCreditsByStoreOutCodes(storeOutData.Select(p => p.storeOutCode).ToList());
            var credits = await _db.Credits.Where(p => storeoutCodes.ToHashSet().Contains(p.RelateCode)).ToListAsync();
            var count = 0;
            try
            {
                if (credits != null && credits.Count > 0)
                {
                    var data = new List<IncomeIsConfirmData>();
                    foreach (var credit in credits)
                    {
                        if (credit.IsSureIncome != null && credit.IsSureIncome == 1)//已经签收
                            continue;
                        if (credit.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE || credit.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE)
                            continue;
                        data.Add(new IncomeIsConfirmData()
                        {
                            date = storeOutData.Where(p => p.storeOutCode == credit.RelateCode).FirstOrDefault() == null ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : DateTimeHelper.LongToDateTime(storeOutData.Where(p => p.storeOutCode == credit.RelateCode).FirstOrDefault().sureIncomeDate).ToString("yyyy-MM-dd HH:mm:ss"),
                            billno = credit.BillCode,
                        });
                    }
                    if (data.Count == 0)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    var incomeIsConfirm = new KingdeeIncomeIsConfirm() { data = data, jfzx_iscofirm = true };
                    var king = await _kingdeeApiClient.PushIncomeIsCofirm(incomeIsConfirm);
                    if (king != null && king.Code == CodeStatusEnum.Success)
                    {
                        foreach (var credit in credits)
                        {
                            if (credit.AutoType == DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE)
                                continue;
                            credit.IsSureIncome = 1;
                            credit.IsSureIncomeDate = DateTime.Parse(data.Where(p => credit.BillCode.Contains(p.billno)).FirstOrDefault().date);
                            credit.UpdatedBy = "admin";
                            credit.UpdatedTime = DateTime.Now;
                            //_db.Credits.Update(credit.Adapt<CreditPo>());
                        }


                        count = await _db.SaveChangesAsync();
                    }
                }
                else
                {
                    throw new Exception("未找到应收单,关联单号：" + string.Join(',', storeOutData.Select(p => p.storeOutCode).ToArray()));
                }
            }
            catch (Exception)
            {
                throw;
            }
            if (count == 0)
            {
                throw new Exception("自动确认收入没有全部成功，对应单号：" + string.Join(",", storeOutData.Select(p => p.storeOutCode).ToArray()));
            }
            return BaseResponseData<int>.Success("操作成功");
        }

        public async Task<BaseResponseData<int>> SplitDebtDetail(Guid detailId, decimal amount)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user.Data.List.First().InstitutionType == 4)
            {
                return BaseResponseData<int>.Failed(500, "业务单元端不支持拆分");
            }
            var detail = await _debtDetailRepository.GetDebtDetail(detailId);
            if (detail == null || detail.AccountPeriodType == 3 || detail.Status != 0) //回款支持
            {
                //服务费应付放开限制
                var debt = await _debtRepository.GetWithNoTrackAsync(detail.DebtId.Value);
                if (debt.DebtType != DebtTypeEnum.servicefee)
                {
                    return BaseResponseData<int>.Failed(500, "明细存在或者该明细不支持拆分");
                }
            }
            if (amount >= detail.Value)
            {
                return BaseResponseData<int>.Failed(500, "拆分金额只能小于原金额");
            }
            var paymentAutoDetail = await _db.PaymentAutoDetails.Include(p => p.PaymentAutoItem).Where(p => p.DebtDetilId == detailId).FirstOrDefaultAsync();
            if (paymentAutoDetail != null)
            {
                return BaseResponseData<int>.Failed(500, $"操作失败，该付款计划已经在{paymentAutoDetail.PaymentAutoItem.Code}批量付款单中存在,不支持拆分");
            }
            if (detail.AccountPeriodType == 2)
            {
                // 如果对应应收的sch存在寄售垫资单，不允许拆分
                var credit = await _db.Credits.FirstOrDefaultAsync(x => x.Id == detail.CreditId);
                if (credit == null)
                {
                    return BaseResponseData<int>.Failed(500, "销售账期需判断应收对应垫资，未找到对应应收单");
                }
                var abas = await _db.AdvanceBusinessApply.Where(x => x.ServiceId == credit.ServiceId && x.HospitalId == credit.CustomerId && x.CompanyId == credit.CompanyId && x.Status != 3).ToListAsync();
                if (abas.Any())
                {
                    return BaseResponseData<int>.Failed(500, "销售账期对应应收存在寄售垫资单，不允许拆分");
                }

            }
            if (detail.AccountPeriodType == 2 || detail.AccountPeriodType == 0)
            {
                if (!detail.ProbablyPayTime.HasValue)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：回款/销售账期没有预计付款日期，不允许拆分");
                }
            }
            var oldValue = detail.Value;
            detail.Value = detail.Value - amount;
            var newDetail = new DebtDetail()
            {
                DebtId = detail.DebtId,
                AccountPeriodType = detail.AccountPeriodType,
                AccountPeriodDays = detail.AccountPeriodDays,
                ProbablyPayTime = detail.ProbablyPayTime,
                Code = detail.Code,
                Status = detail.Status,
                Value = amount,
                CreatedTime = DateTime.Now,
                CreatedBy = detail.CreatedBy,
                Id = Guid.NewGuid(),
                Settletype = detail.Settletype,
                TaxDiscount = detail.TaxDiscount,
                CostDiscount = detail.CostDiscount,
                FinanceDiscount = detail.FinanceDiscount,
                DistributionDiscount = detail.DistributionDiscount,
                SpdDiscount = detail.SpdDiscount,
                ReceiveCode = detail.ReceiveCode,
                RecognizeReceiveCode = detail.RecognizeReceiveCode,
                DraftBillExpireDate = detail.DraftBillExpireDate,
                PurchaseCode = detail.PurchaseCode,
                Discount = detail.Discount,
            };
            if (oldValue != 0)
            {
                newDetail.OriginValue = amount / oldValue * detail.OriginValue;
            }
            else
            {
                newDetail.OriginValue = 0;
            }
            detail.OriginValue -= newDetail.OriginValue;
            if (detail.AccountPeriodType == 2 || detail.AccountPeriodType == 0)
            {
                // 销售账期需复制预计付款日期、订单号、应收单号
                newDetail.OrderNo = detail.OrderNo;
                newDetail.CreditId = detail.CreditId;
                newDetail.BackPayTime = detail.BackPayTime;
                newDetail.ProbablyPayTime = detail.ProbablyPayTime;
            }
            await _debtDetailRepository.UpdateAsync(detail);
            await _debtDetailRepository.AddAsync(newDetail);
            await _unitOfWork.CommitAsync();
            return BaseResponseData<int>.Success("操作成功");
        }

        /// <summary>
        ///无需审批
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> NoNeedInvoice(NoNeedInvoiceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            var credits = await _creditQueryService.GetAllListAsync(p => input.CreditBillCodes.ToHashSet().Contains(p.BillCode));
            if (credits != null && credits.Any())
            {
                var creditIds = credits.Select(p => p.Id).ToHashSet();
                var companyId = credits.First().CompanyId;
                var customerId = credits.First().CustomerId;
                var credits_same = credits.Where(p => p.CustomerId == customerId && p.CompanyId == companyId).ToList();
                if (credits.Count() == credits_same.Count())
                {
                    var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId != null && creditIds.Contains((Guid)p.CreditId) && p.IsCancel != true)
                                             .Select(p => new { p.CreditId, p.InvoiceAmount, p.CreditAmount, p.Type }).ToListAsync();
                    var noInvoiceAmountTotal = 0m;
                    var groupId = Guid.NewGuid();
                    var creditsTotalValue = 0m;
                    var invoceAmountTotal = 0m;
                    credits.ForEach(p =>
                    {
                        var invoiceAmount = invoiceCredits.Where(t => t.CreditId == p.Id).Sum(p => p.CreditAmount.Value);
                        p.IsNoNeedInvoice = IsNoNeedInvoiceEnum.NoNeed;
                        p.GroupId = groupId;
                        p.UpdatedBy = input.UserName;
                        creditsTotalValue += p.Value;
                        invoceAmountTotal += invoiceAmount;
                    });
                    noInvoiceAmountTotal = creditsTotalValue - invoceAmountTotal;
                    if (noInvoiceAmountTotal == 0)
                    {
                        await _creditRepository.UpdateManyAsync(credits.Adapt<List<Credit>>());
                        var updateRet = await _unitOfWork.CommitAsync();
                        if (updateRet <= 0)
                        {
                            ret = BaseResponseData<int>.Failed(500, "操作失败，原因：修改数据失败！");
                        }

                    }
                    else
                    {
                        ret = BaseResponseData<int>.Failed(500, "操作失败，原因：所选应收单的未开票金额合计必须为0！");
                    }

                }
                else
                {
                    ret = BaseResponseData<int>.Failed(500, "操作失败，原因：所选应收单必须为同公司、同客户！");
                }
            }
            return ret;
        }

        /// <summary>
        /// 取消应收（仅支持服务费应收）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        public async Task<BaseResponseData<string>> Cancel(CancelCreditInput input)
        {
            var ret = BaseResponseData<string>.Failed(500, "操作失败");
            try
            {
                var credits = await _creditQueryService.GetAllListAsync(p =>
                    p.CreditType == CreditTypeEnum.servicefee &&
                    p.InvoiceStatus != InvoiceStatusEnum.invoiced &&
                    p.AbatedStatus != AbatedStatusEnum.Abated &&
                    p.Mark != 89 &&
                    input.SaleBillCodes.Contains(p.RelateCode)
                );
                if (credits != null && credits.Any())
                {
                    var sysMonth = await _bDSApiClient.GetSystemMonth(credits.First().CompanyId.Value.ToString());
                    var query = _db.InventoryItem.Where(p => p.CompanyId == credits.First().CompanyId.Value && p.Status == 2);
                    var itemPo = await query.FirstOrDefaultAsync();
                    if (itemPo != null)
                    {
                        return BaseResponseData<string>.Failed(500, "操作失败，原因：盘点期间不能提交撤销");
                    }

                    await CheckSysMonth(credits.First().CompanyId.Value, sysMonth);
                    var sysMonthDate = DateTime.Parse(sysMonth);
                    var creditIds = credits.Select(c => c.Id).ToList();
                    var tempCredits = credits.Where(p => p.BillDate.Value.Month != sysMonthDate.Month || p.BillDate.Value.Year != sysMonthDate.Year).Select(p => p.BillCode);


                    if (tempCredits.Count() > 0)
                    {
                        ret.Message = $"操作失败，原因：应收单：【{string.Join(",", tempCredits)}】存在跨月的应收！";
                        return ret;
                    }
                    tempCredits = credits.Where(p => p.AbatedStatus == AbatedStatusEnum.Abated).Select(p => p.BillCode);
                    if (tempCredits.Count() > 0)
                    {
                        ret.Message = $"操作失败，原因：应收单：【{string.Join(",", tempCredits)}】存在冲销的应收！";
                        return ret;
                    }
                    var creditBillCodes = credits.Select(c => c.BillCode).ToList();
                    //存在申开单表里面  
                    var customizeInvoiceSubs = await (from subDetail in _db.CustomizeInvoiceSubDetails
                                                      join item in _db.CustomizeInvoiceItem on subDetail.CustomizeInvoiceItemId equals item.Id
                                                      where (item.Status == CustomizeInvoiceStatusEnum.WaitSubmit || item.Status == CustomizeInvoiceStatusEnum.Auditing) &&
                                                       creditBillCodes.Contains(subDetail.CreditBillCode)
                                                      select subDetail).AsNoTracking().ToListAsync();
                    if (customizeInvoiceSubs != null && customizeInvoiceSubs.Any())
                    {
                        creditBillCodes = customizeInvoiceSubs.Select(p => p.CreditBillCode).ToList();
                        ret.Message = $"操作失败，原因：应收单：【{string.Join(",", creditBillCodes)}】正在开票制作中！";
                        return ret;
                    }

                    var receiveDetails_credits = await _db.RecognizeReceiveDetails.Where(p => p.Type == 3 && creditBillCodes.Contains(p.Code) && p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled).AsNoTracking().ToListAsync();
                    if (receiveDetails_credits != null && receiveDetails_credits.Any())
                    {
                        creditBillCodes = receiveDetails_credits.Select(p => p.Code).ToList();
                        ret.Message = $"操作失败，原因：应收单：【{string.Join(",", creditBillCodes)}】正在认款或已认款！";
                        return ret;
                    }
                    var receiveDetails_orders = await _db.RecognizeReceiveDetails.Where(p => p.Type == 2 && input.SaleBillCodes.Contains(p.Code) && p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled).AsNoTracking().ToListAsync();
                    if (receiveDetails_orders != null && receiveDetails_orders.Any())
                    {
                        creditBillCodes = receiveDetails_orders.Select(p => p.Code).ToList();
                        ret.Message = $"操作失败，原因：订单号：【{string.Join(",", creditBillCodes)}】正在认款或已认款！";
                        return ret;
                    }
                    var invoiceCredits = await _invoiceCreditQueryService.GetAllListAsync(p => creditIds.Contains(p.CreditId.Value));
                    var invoiceNos = invoiceCredits.Where(p => p.IsCancel != true).Select(p => p.InvoiceNo).Distinct().ToHashSet();
                    var receiveDetails_invoices = await _db.RecognizeReceiveDetails.Where(p => p.Type == 1 && invoiceNos.Contains(p.Code) && p.RecognizeReceiveItem.Status != RecognizeReceiveItemStatusEnum.Canceled).AsNoTracking().ToListAsync();
                    if (receiveDetails_invoices != null && receiveDetails_invoices.Any())
                    {
                        creditBillCodes = receiveDetails_invoices.Select(p => p.Code).ToList();
                        ret.Message = $"操作失败，原因：发票号：【{string.Join(",", creditBillCodes)}】正在认款或已认款！";
                        return ret;
                    }

                    var data = new RollBackBillDto()
                    {
                        billType = "B",
                        billnos = creditBillCodes.Select(p => new RollBackBillNo { billno = p }).ToList()
                    };
                    var kingdeeRoll = await _kingdeeApiClient.RollBackBill(data);
                    if (kingdeeRoll.Code == CodeStatusEnum.Success)
                    {
                        _db.InvoiceCredits.RemoveRange(invoiceCredits);
                        _db.Credits.RemoveRange(credits);
                        await _db.SaveChangesAsync();
                        ret.Message = "操作成功！";
                        ret.Code = CodeStatusEnum.Success;
                    }
                    else
                    {
                        ret.Message = $"操作失败，原因：【金蝶】{kingdeeRoll.Message}";
                    }
                    return ret;

                }
                else
                {
                    ret.Message = "操作失败，原因：没有找到对应的应收数据！";
                    return ret;
                }

            }
            catch (Exception ex)
            {
                ret.Message = ex.Message;
                return ret;
            }

        }

        private async Task<string> CheckSysMonth(Guid companyId, string sysMonth)
        {
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
            {
                var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
                if (inventory != null)
                {
                    if (inventory.Status == 2)
                    {
                        throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                    }
                    if (inventory.Status == 99)
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }
                }
            }
            else
            {
                DateTime.TryParse(sysMonth, out DateTime billDate);
                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                {
                    throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                }
            }
            return sysMonth;
        }

        public async Task<BaseResponseData<List<CreditDto>>> GetInitCredit(GetInitCreditInput input)
        {
            var ret = BaseResponseData<List<CreditDto>>.Success("操作成功");
            var list = await _db.Credits.Where(p => p.CreditType == CreditTypeEnum.origin && input.BillCodes.Contains(p.BillCode)).Select(p => p.Adapt<CreditDto>()).ToListAsync();
            ret.Data = list;
            return ret;
        }

        /// <summary>
        /// 分批确认收入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<GetPartialIncomeOutput>>> RevcfmbillBatchisConfirm(List<GetPartialIncomeInput> input)
        {
            try
            {
                var ret = BaseResponseData<List<GetPartialIncomeOutput>>.Success("操作成功");
                List<GetPartialIncomeOutput> outputs = new List<GetPartialIncomeOutput>();
                // 保存数据序列
                var creditSureIncomePos = new List<CreditSureIncomePo>();
                // 调用金蝶入参
                var kingdeeInputs = new List<RevcfmbillBatchiscofirmInput>();
                // 订单号集合
                var orderNos = input.Select(x => x.OrderNo).ToHashSet();
                var creditsAll = await _db.Credits.Where(x => orderNos.Contains(x.OrderNo)).AsNoTracking().ToListAsync();
                var credits = creditsAll.Where(p => p.Auto != 1 || !p.Auto.HasValue).ToList();
                var creditsTmp = creditsAll.Where(p => p.Auto == 1).ToList();
                // 查询应收分批次已确认金额
                var creditIds = credits.Select(x => x.Id).ToHashSet();
                var creditSureIncomes = await _db.CreditSureIncome.Where(x => creditIds.Contains(x.CreditId)).ToListAsync();
                // 确认金额等于应收金额需修改应收单确认收入状态
                var updateCredits = new List<CreditPo>();

                foreach (var item in input)
                {
                    if (string.IsNullOrEmpty(item.OrderNo))
                    {
                        return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, "列表中订单号不能为空");
                    }
                    if (!item.ConfirmDate.HasValue)
                    {
                        return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, "列表中收入确认日期不能为空");
                    }
                    if (!item.ConfirmAmount.HasValue)
                    {
                        return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, "列表中收入确认金额不能为空");
                    }
                    var credit = credits.FirstOrDefault(x => x.OrderNo == item.OrderNo);
                    if (credit == null)
                    {
                        outputs.Add(new GetPartialIncomeOutput { OrderNo = item.OrderNo });
                        continue;
                        //return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, $"未找到订单号为{item.OrderNo}对应的应收单");
                    }
                    // 装填数据
                    var model = new CreditSureIncomePo();
                    model.CreditId = credit.Id;
                    model.Value = item.ConfirmAmount.Value;
                    model.NoTaxValue = item.NoTaxValue;
                    model.Cost = item.Cost;//含税成本
                    model.NoTaxCost = item.NoTaxCost;//不含税成本
                    model.SureIncomeDate = item.ConfirmDate.Value;
                    model.Remark = item.Remark;
                    model.CreatedBy = item.UserName ??= "none";
                    model.CreatedTime = DateTimeOffset.UtcNow;
                    creditSureIncomePos.Add(model);
                    // 装填入参
                    var kingdee = new RevcfmbillBatchiscofirmInput();
                    kingdee.confirmamt = item.NoTaxValue ??= 0;
                    kingdee.totalCost = item.NoTaxCost ??= 0;//不含税成本
                    kingdee.jfzx_iscofirm = true;
                    kingdee.billno = credit.BillCode ??= string.Empty;
                    kingdeeInputs.Add(kingdee);
                }
                foreach (var credit in credits)
                {
                    // 本次应收单总确认金额
                    var currentSureIncomes = creditSureIncomePos.Where(x => x.CreditId == credit.Id).ToList();
                    var currentSureIncomeValue = currentSureIncomes.Sum(x => x.Value);
                    // 历史确认金额 
                    var historySureIncomeValue = creditSureIncomes.Where(x => x.CreditId == credit.Id).Sum(x => x.Value);
                    if (currentSureIncomeValue + historySureIncomeValue > credit.Value)
                    {
                        //清楚异常数据 
                        var kingdeeInputTemps = kingdeeInputs.Where(p => p.billno == credit.BillCode).ToList();
                        if (kingdeeInputTemps != null)
                        {
                            foreach (var item in kingdeeInputTemps)
                            {
                                kingdeeInputs.Remove(item);
                            }
                        }
                        if (currentSureIncomes != null)
                        {
                            foreach (var item in currentSureIncomes)
                            {

                                creditSureIncomePos.Remove(item);
                            }
                        }
                        outputs.Add(new GetPartialIncomeOutput { OrderNo = credit.OrderNo });
                        // 金额超出
                        //return BaseResponseData<int>.Failed(500, $"本次确认收入超出应收单{credit.BillCode}总额");
                        continue;
                    }
                    if (currentSureIncomeValue + historySureIncomeValue == credit.Value)
                    {
                        // 更改状态
                        credit.IsSureIncome = 1;
                        updateCredits.Add(credit);
                    }
                }
                ret.Data = outputs;
                foreach (var credit in creditsTmp)
                {
                    credit.IsSureIncome = 1;
                    updateCredits.Add(credit);
                }
                if (kingdeeInputs.Any())
                {
                    // 调用金蝶接口
                    var kingRet = await _kingdeeApiClient.RevcfmbillBatchisConfirm(kingdeeInputs);
                    if (kingRet.Code != CodeStatusEnum.Success)
                    {
                        return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, kingRet.Message);
                    }

                }
                if (updateCredits.Any())
                {
                    _db.Credits.UpdateRange(updateCredits);
                }
                if (creditSureIncomePos.Any())
                {
                    // 成功写入数据
                    await _db.CreditSureIncome.AddRangeAsync(creditSureIncomePos);
                }
                await _unitOfWork.CommitAsync();
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<List<GetPartialIncomeOutput>>.Failed(500, "分批确认收入失败，原因：" + ex.Message);
            }
        }
        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportCreditTask(CreditQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DictionaryExtensions.DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_CreditExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("应收清单导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        /// <summary>
        /// 协调服务导出，销项发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoiceCreditTask(InvoiceCreditQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DictionaryExtensions.DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.UserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_InvoiceCreditExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.UserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("销项发票导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        /// <summary>
        /// 应收更换核算部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> CreditBusinessDeptChange(BusinessDeptInput input)
        {
            try
            {
                if (input == null || input.ProjectIds.Count == 0)
                {
                    return BaseResponseData<string>.Failed(500, "项目Id为空");
                }
                var newBusinessDepts = await _projectMgntApiClient.GetProjectListByIds(input.ProjectIds.Where(guid => guid.HasValue).Select(guid => guid.Value).ToList());//需要基础数据根据项目id获取核算部门的接口
                //根据项目获取所有未冲销完的应收(包含我们变更核算部门产生的新的已经冲销的应收)
                var projecrIdAndBusinessDeptId = newBusinessDepts.Select(p => p.Id.ToString() + p.BusinessDeptFullPath).ToList();//排除掉更换核算部门之后新产生的应收数据
                var allCreditList = await _creditQueryService.GetAllListAsync(p => (p.AbatedStatus == AbatedStatusEnum.NonAbate || (p.AbatedStatus == AbatedStatusEnum.Abated && !string.IsNullOrEmpty(p.AutoTypeName))) && input.ProjectIds.ToHashSet().Contains(p.ProjectId) && !projecrIdAndBusinessDeptId.Contains(p.ProjectId.ToString() + p.BusinessDeptFullPath));
                //排除已经初始化过的应收
                var allNeedChangeCreditList = allCreditList.Where(p => p.AbatedStatus == 0 && input.ProjectIds.ToHashSet().Contains(p.ProjectId) && string.IsNullOrEmpty(p.AutoTypeName)).ToList();
                if (allNeedChangeCreditList.Count <= 0)
                {
                    return BaseResponseData<string>.Success("没有要修改的应收");
                }
                var groupAllNeedChangeCreditList = allNeedChangeCreditList.GroupBy(z => z.CompanyId).ToList();
                foreach (var groupitem in groupAllNeedChangeCreditList)
                {
                    var allNeedChangeCreditListByCompanyId = allNeedChangeCreditList.Where(z => z.CompanyId == groupitem.Key).ToList();
                    //500一组分多次请求
                    int pageSize = 200;
                    int pageCount = (allNeedChangeCreditListByCompanyId.Count / pageSize) + 1;
                    for (int i = 0; i < pageCount; i++)
                    {
                        var needChangeCreditList = allNeedChangeCreditListByCompanyId.Skip(i * pageSize).Take(pageSize).ToList();
                        if (needChangeCreditList != null && needChangeCreditList.Count() > 0)
                        {
                            var creditCodes = needChangeCreditList.Select(t => t.BillCode).ToHashSet();
                            var needAddCredits = new List<CreditPo>();//需要添加的新应收
                            var needAddAbatements = new List<Abatement>();//需要添加的冲销记录
                            var needAddCreditChangeLog = new List<MigrationRecordPo>();//需要添加的变更记录表
                            var oldCreditIdAndNewCreditId = new Dictionary<Guid, Guid>();
                            //找所有未冲销完成的应收的冲销记录
                            var abatementsList = await _db.Abatements.Where(p => (creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode))).AsNoTracking().ToListAsync();

                            //获取已开票金额                                                                                                                                                                                                                                
                            var creditIds = needChangeCreditList.Select(p => p.Id).ToHashSet();
                            var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId != null && creditIds.Contains(p.CreditId.Value) && p.IsCancel != true).Select(p => new { p.CreditId, p.InvoiceAmount, p.CreditAmount, p.Type }).ToListAsync();
                            //获取所有应收关联的公司id
                            var allCompanyId = needChangeCreditList.Where(z => z.CompanyId.HasValue).Select(z => z.CompanyId.Value).Distinct().ToList();
                            //一次调用基础数据接口获取所有公司的系统月度
                            //List<CreditBillDateDTO> allCompanyBillDateList = new List<CreditBillDateDTO>();
                            //foreach (var item in allCompanyId)
                            //{
                            //    allCompanyBillDateList.Add(new CreditBillDateDTO
                            //    {
                            //        CompanyId = item,
                            //        BillDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(item.ToString()))
                            //    });
                            //}

                            //获取应付明细，这里是需要更新的，因为新的应收生成后，要告诉应付，我的新应收是什么，这样在初始化应付的时候，就可以把新应付和新应收关联起来
                            var debtDetailList = await _db.DebtDetails.Where(p => creditIds.ToHashSet().Contains(p.CreditId.Value)).ToListAsync();
                            //定义一个新增应付明细列表
                            //适配的场景是老的应收撤销应收，在应收和应付有关联的情况下，要讲应付的付款计划拆分并关联到新生成的应收上
                            var newInsertDebtDetailList = new List<DebtDetail>();
                            //找一下有没有产生新的应付明细，用来后面计算时候，防止循环访问
                            var newDebtDetailList = new List<DebtDetailPo>();
                            var newCreditIds = debtDetailList.Where(z => z.NewBussinessDeptCreditId.HasValue).Select(z => z.NewBussinessDeptCreditId).ToList();
                            //根据老应付里的NewBussinessDeptCreditId，顺藤摸瓜找到新应付的付款计划明细,以备不时之需
                            if (newCreditIds != null && newCreditIds.Count > 0)
                            {
                                newDebtDetailList = await _db.DebtDetails.Where(p => newCreditIds.ToHashSet().Contains(p.CreditId.Value)).ToListAsync();
                            }

                            var kingdeeCredits = new List<KingdeeCredit>();
                            //跳号
                            int skipNumber = 2;
                            foreach (var item in needChangeCreditList)
                            {
                                //生成原核算部门负数应收并冲销原应收
                                var newCredit = CopyNewCredit(item);
                                //剩余冲销金额
                                decimal noAbatedAmount = Math.Abs(item.Value);
                                //找出以前初始化的单号，在最大的那个单号基础上，序列+1
                                var currentMaxCode = allCreditList.Where(z => z.BillCode != item.BillCode && z.BillCode.Contains(item.BillCode)).OrderByDescending(z => z.BillCode).FirstOrDefault();//排除掉原单，找到前面生成的初始化单
                                string lastCodeString = "000";
                                if (currentMaxCode != null)
                                    lastCodeString = currentMaxCode.BillCode.Substring(currentMaxCode.BillCode.LastIndexOf('-') + 1);//拿到最后的标号
                                var newIndexCode = (int.Parse(lastCodeString) == 0 ? (int.Parse(lastCodeString) + 1) : (int.Parse(lastCodeString) + skipNumber)).ToString("000");//生成新标号
                                                                                                                                                                                 //方法内全局标号复制
                                lastCodeString = newIndexCode;
                                newCredit.BillCode = $"{item.BillCode}-{newIndexCode}";
                                newCredit.AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_MINUSTYPE;
                                newCredit.AbatedStatus = AbatedStatusEnum.Abated;//新应收已冲销
                                                                                 //newCredit.BillDate = allCompanyBillDateList.First(z => z.CompanyId == item.CompanyId.Value).BillDate;
                                item.AbatedStatus = AbatedStatusEnum.Abated;//原应收已冲销

                                //获取已冲销金额
                                var abatementsInfo = abatementsList.Where(p => p.DebtBillCode == item.BillCode || p.CreditBillCode == item.BillCode).ToList();
                                if (abatementsInfo != null && abatementsInfo.Count > 0)
                                {
                                    noAbatedAmount = Math.Abs(item.Value) - abatementsInfo.Sum(p => p.Value);
                                    newCredit.Value = item.Value > 0 ? -noAbatedAmount : noAbatedAmount;
                                }
                                else
                                {
                                    newCredit.Value = -item.Value;
                                }
                                needAddCredits.Add(newCredit);
                                kingdeeCredits.Add(await PushCreditToKingdee(newCredit, item.BillCode, item.BusinessDeptId, "B"));
                                //加入冲销表
                                needAddAbatements.Add(new Abatement()
                                {
                                    DebtBillCode = item.BillCode,
                                    DebtType = "credit",
                                    CreditBillCode = newCredit.BillCode,
                                    CreditType = "credit",
                                    Abtdate = DateTime.Now,
                                    Value = noAbatedAmount,
                                    CreatedTime = DateTime.Now,
                                    CreatedBy = "BusinessDept"
                                });

                                //生成一个新核算部门的应收（复制原始应收），金额使用未冲销金额
                                var newBusinessDeptCredit = CopyNewCredit(item);
                                newIndexCode = (int.Parse(lastCodeString) + 1).ToString("000");//生成新标号
                                newBusinessDeptCredit.BillCode = $"{item.BillCode}-{newIndexCode}";
                                newBusinessDeptCredit.AutoType = DomainConstants.CHANGE_BUSSINESS_DEPT_NEWTYPE;
                                newBusinessDeptCredit.Value = item.Value > 0 ? noAbatedAmount : -noAbatedAmount;
                                newBusinessDeptCredit.IsNoNeedInvoice = IsNoNeedInvoiceEnum.NoNeed; //新的核算部门应收无需开票
                                newBusinessDeptCredit.BusinessDeptFullPath = newBusinessDepts.Where(p => p.Id == item.ProjectId).FirstOrDefault().BusinessDeptFullPath;
                                newBusinessDeptCredit.BusinessDeptFullName = newBusinessDepts.Where(p => p.Id == item.ProjectId).FirstOrDefault().BusinessDeptFullName;
                                newBusinessDeptCredit.BusinessDeptId = newBusinessDepts.Where(p => p.Id == item.ProjectId).FirstOrDefault().BusinessDeptId;

                                newBusinessDeptCredit.AbatedStatus = AbatedStatusEnum.NonAbate; //新核算部门未冲销
                                                                                                //newBusinessDeptCredit.BillDate = allCompanyBillDateList.First(z => z.CompanyId == item.CompanyId.Value).BillDate;
                                needAddCredits.Add(newBusinessDeptCredit);
                                kingdeeCredits.Add(await PushCreditToKingdee(newBusinessDeptCredit, item.BillCode, newBusinessDeptCredit.BusinessDeptId, "C"));
                                needAddCreditChangeLog.Add(new()
                                {
                                    OriginBillCode = item.BillCode,
                                    MiddleBillCode = newCredit.BillCode,
                                    NewBillCode = newBusinessDeptCredit.BillCode,
                                    CreatedBy = "admin",
                                    CreatedTime = DateTime.Now,
                                    DataType = MigrationDataTypeEnum.Credit
                                });
                                //原来老应付的付款计划
                                var oldDebtDetails = debtDetailList.Where(p => p.CreditId == item.Id).ToList();
                                //应付明细中 用新核算部门的应收Id，替换 原始应收Id
                                //这里是原应付撤销冲销之后，新应收怎么关联到新应付的算法
                                if (oldDebtDetails != null && oldDebtDetails.Count > 0)
                                {
                                    //只要老的应付付款计划里，有任何记录新应收id字段有值，那么就认为这种情况就是老应收撤销了冲销，需要拆分新应付的付款计划
                                    if (oldDebtDetails.Any(z => z.NewBussinessDeptCreditId.HasValue))
                                    {
                                        var groupOldDebtDetails = oldDebtDetails.GroupBy(z => z.DebtId).ToList();
                                        foreach (var groupDebtItems in groupOldDebtDetails)
                                        {
                                            //直接天选老应付的付款计划明细
                                            var oldMatchDebtDetail = groupDebtItems.Where(z => z.OrderNo == newBusinessDeptCredit.OrderNo).ToList();
                                            //首先计算原应收和原应付的收付比
                                            var debtRate = Math.Round(oldMatchDebtDetail.Sum(x => x.Value) / item.Value, 2);
                                            //按照收付比，算出这次撤销的应收冲销，对应的应付付款计划应该是多少钱
                                            var debtDetailValue = Math.Round(noAbatedAmount * debtRate, 2);
                                            //找到新应付付款计划里，是未执行，并且金额最接近debtDetailValue 的付款计划，进行减法拆分
                                            var matchSplitDebtDetail = newDebtDetailList.Where(z => z.CreditId == oldMatchDebtDetail.First().NewBussinessDeptCreditId && z.Status == 0 && z.Value > debtDetailValue).OrderBy(z => Math.Abs(z.Value - debtDetailValue)).FirstOrDefault();
                                            if (matchSplitDebtDetail == null)
                                            {
                                                throw new ApplicationException($"拆分付款计划出现问题，拆分金额大于应付付款计划金额，应收单号{item.BillCode}");
                                            }
                                            //复制最准确的应付明细记录
                                            var newDebtDetal = new DebtDetail
                                            {
                                                Code = matchSplitDebtDetail.Code,
                                                AccountPeriodType = matchSplitDebtDetail.AccountPeriodType,
                                                AccountPeriodDays = matchSplitDebtDetail.AccountPeriodDays,
                                                Discount = matchSplitDebtDetail.Discount,
                                                DebtId = matchSplitDebtDetail.DebtId,
                                                CreditId = newBusinessDeptCredit.Id,
                                                Value = debtDetailValue,
                                                Status = 0,
                                                CreatedTime = DateTime.UtcNow,
                                                CreatedBy = matchSplitDebtDetail.CreatedBy,
                                                PurchaseCode = matchSplitDebtDetail.PurchaseCode,
                                                OrderNo = matchSplitDebtDetail.OrderNo,
                                                DistributionDiscount = matchSplitDebtDetail.DistributionDiscount,
                                                CostDiscount = matchSplitDebtDetail.CostDiscount,
                                                FinanceDiscount = matchSplitDebtDetail.FinanceDiscount,
                                                SpdDiscount = matchSplitDebtDetail.SpdDiscount,
                                                TaxDiscount = matchSplitDebtDetail.TaxDiscount
                                                //BackPayTime = matchSplitDebtDetail.BackPayTime,
                                            };
                                            //拆分最准确的应付明细
                                            matchSplitDebtDetail.Value = matchSplitDebtDetail.Value - newDebtDetal.Value;
                                            //给新的应付增加付款计划
                                            newInsertDebtDetailList.Add(newDebtDetal);
                                        }
                                    }
                                    else
                                    {
                                        oldDebtDetails.ForEach(p => p.NewBussinessDeptCreditId = newBusinessDeptCredit.Id);
                                    }

                                }
                            }
                            using (var transaction = await _db.Database.BeginTransactionAsync())
                            {
                                if (needAddCredits.Count > 0)
                                {
                                    await _db.Credits.AddRangeAsync(needAddCredits.Adapt<List<CreditPo>>());
                                }
                                if (needAddAbatements.Count > 0)
                                {
                                    await _db.Abatements.AddRangeAsync(needAddAbatements.Adapt<List<AbatementPo>>());
                                }
                                if (needAddCreditChangeLog.Count > 0)
                                {
                                    await _db.MigrationRecords.AddRangeAsync(needAddCreditChangeLog);
                                }
                                if (newInsertDebtDetailList.Count > 0)
                                {
                                    await _db.DebtDetails.AddRangeAsync(newInsertDebtDetailList.Adapt<List<DebtDetailPo>>());
                                }
                                _db.Credits.UpdateRange(needChangeCreditList);

                                if (kingdeeCredits.Count > 0)
                                {
                                    var kingdeeRes = await _kingdeeApiClient.DeptPushCreditsToKingdee(kingdeeCredits);
                                    if (kingdeeRes.Code != CodeStatusEnum.Success && kingdeeRes.ErrorCode != 800)
                                    {
                                        return BaseResponseData<string>.Failed(500, $"修改失败：{kingdeeRes.Message}");
                                    }


                                    //var groupKingdeeCredits = kingdeeCredits.GroupBy(z => z.org_number).ToList();//应金蝶要求，初始化的时候推送要按业务组织分批推送
                                    //foreach (var groupitem in groupKingdeeCredits)
                                    //{
                                    //    var currentPush = kingdeeCredits.Where(x => x.org_number == groupitem.Key).OrderBy(z => z.billno).ToList();
                                    //    //500一组分多次请求
                                    //    int pageSize = 500;
                                    //    int pageCount = (currentPush.Count / pageSize) + 1;
                                    //    for (int i = 0; i < pageCount; i++)
                                    //    {
                                    //        var pushData = currentPush.Skip(i * pageSize).Take(pageSize).ToList();
                                    //        var kingdeeRes = await _kingdeeApiClient.DeptPushCreditsToKingdee(pushData);
                                    //        if (kingdeeRes.Code != CodeStatusEnum.Success && kingdeeRes.ErrorCode != 800)
                                    //        {

                                    //            return BaseResponseData<string>.Failed(500, $"修改失败：{kingdeeRes.Message}");
                                    //        }
                                    //    }
                                    //}
                                }

                                await _db.SaveChangesAsync();
                                await transaction.CommitAsync();
                            }

                        }

                    }
                }




                return BaseResponseData<string>.Success("修改成功");



            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "修改失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 导出应收明细任务
        /// </summary>
        /// <param name="query">导出查询参数</param>
        /// <returns>导出任务响应数据列表</returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportCreditDetailTask(CreditQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DictionaryExtensions.DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_CreditDetailExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("应收明细导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        private CreditPo CopyNewCredit(CreditPo oldCredit)
        {
            if (oldCredit == null)
                return null;
            // 复制原 Credit 对象
            var newCredit = new CreditPo
            {
                AbatedStatus = oldCredit.AbatedStatus,
                BillCode = oldCredit.BillCode,
                BillDate = oldCredit.BillDate,
                CompanyId = oldCredit.CompanyId,
                CompanyName = oldCredit.CompanyName,
                NameCode = oldCredit.NameCode,
                Mark = oldCredit.Mark,
                CreditType = oldCredit.CreditType,
                CustomerId = oldCredit.CustomerId,
                CustomerName = oldCredit.CustomerName,
                LatestUniCode = oldCredit.LatestUniCode,
                FinishDate = oldCredit.FinishDate,
                InvoiceStatus = oldCredit.InvoiceStatus,
                Note = oldCredit.Note,
                ServiceId = oldCredit.ServiceId,
                ServiceName = oldCredit.ServiceName,
                RelateCode = oldCredit.RelateCode,
                BusinessDeptFullPath = oldCredit.BusinessDeptFullPath,
                BusinessDeptFullName = oldCredit.BusinessDeptFullName,
                BusinessDeptId = oldCredit.BusinessDeptId,
                IsLongTerm = oldCredit.IsLongTerm,
                IsSureIncome = oldCredit.IsSureIncome,
                IsSureIncomeDate = oldCredit.IsSureIncomeDate,
                OrderNo = oldCredit.OrderNo,
                OriginOrderNo = oldCredit.OriginOrderNo,
                GroupId = oldCredit.GroupId,
                SaleSystemId = oldCredit.SaleSystemId,
                SaleSystemName = oldCredit.SaleSystemName,
                SaleSource = oldCredit.SaleSource,
                HospitalId = oldCredit.HospitalId,
                HospitalName = oldCredit.HospitalName,
                SaleType = oldCredit.SaleType,
                ShipmentCode = oldCredit.ShipmentCode,
                DeptName = oldCredit.DeptName,
                ProjectCode = oldCredit.ProjectCode,
                ProjectName = oldCredit.ProjectName,
                ProjectId = oldCredit.ProjectId,
                CustomerOrderCode = oldCredit.CustomerOrderCode,
                SunPurchaseRelatecode = oldCredit.SunPurchaseRelatecode,
                ServiceConfirmRevenuePlanModeEnum = oldCredit.ServiceConfirmRevenuePlanModeEnum,
                AgentName = oldCredit.AgentName,
                ProducerName = oldCredit.ProducerName,
                CreditSaleSubType = oldCredit.CreditSaleSubType,
                ProbablyBackTime = oldCredit.ProbablyBackTime,
                Id = Guid.NewGuid(),
                IsInvoiced = oldCredit.IsInvoiced,
                CreatedBy = oldCredit.CreatedBy,
                CreatedTime = DateTime.Now,
                Auto = DomainConstants.CHANGE_BUSSINESS_DEPT_AUTO,

                AutoTypeName = DomainConstants.CHANGE_BUSSINESS_DEPT_TYPE_NAME,
                IsNoNeedInvoice = IsNoNeedInvoiceEnum.NoNeed//新应收无需开票
            };
            return newCredit;
        }
        /// <summary>
        /// 推送金蝶数据构建
        /// </summary>
        /// <param name="credit"></param>
        /// <param name="associatedNumber">关联单号，初始化的时候传的就是老应收的单号</param>
        /// <param name="newBusinessNumber"></param>
        /// <param name="adjustmentFlag"></param>
        /// <returns></returns>
        private async Task<KingdeeCredit> PushCreditToKingdee(CreditPo credit, string associatedNumber, string newBusinessNumber, string adjustmentFlag)
        {
            RebateTypeEnum? rebateType = null;

            var kingdeeCredit = new KingdeeCredit()
            {
                asstact_number1 = credit.CustomerId.Value,
                billno = credit.BillCode,
                billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType.ToString()),
                bizdate = credit.CreditType == CreditTypeEnum.origin ? DateTime.UtcNow.Date : credit.BillDate.Value,
                org_number = credit.NameCode,
                jfzx_businessnumber = credit.BusinessDeptId,
                jfzx_ordernumber = credit.OrderNo,
                jfzx_iscofirm = true,
                jfzx_creator = credit.CreatedBy ?? "none",
                jfzx_serviceid = credit.ServiceName,
                associatedNumber = associatedNumber,
                adjustmentFlag = adjustmentFlag,
                newBusinessNumber = newBusinessNumber
            };
            //var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(credit.OrderNo);
            //if (saleOut != null)
            //{
            //    rebateType = saleOut.RebateType;
            //}
            //kingdeeCredit.jfzx_rebate = rebateType.HasValue;
            //应收不含税总额
            kingdeeCredit.recamount = Math.Round(credit.Value, 2);
            return kingdeeCredit;
        }
    }
}
