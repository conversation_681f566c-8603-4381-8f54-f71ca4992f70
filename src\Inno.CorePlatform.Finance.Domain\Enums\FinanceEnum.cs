﻿using System.ComponentModel;
using System.Reflection;

namespace Inno.CorePlatform.Finance.Domain
{

    /// <summary>
    /// 订单类型
    /// </summary>
    public enum SaleTypeEnum
    {
        /// <summary>
        /// 销售出库订单
        /// </summary>
        [Description("销售出库订单")]
        SaleOut = 1,

        /// <summary>
        /// B类订单
        /// </summary>
        [Description("B类订单")]
        SaleForB = 2,

        /// <summary>
        /// 经销B类订单
        /// </summary>
        [Description("经销B类订单")]
        SaleForBNormal = 3,

        /// <summary>
        /// 暂存核销订单
        /// </summary>
        [Description("暂存核销订单")]
        Temp = 4,

        /// <summary>
        /// 订单修订
        /// </summary>
        [Description("订单修订")]
        SaleRevise = 5,

        /// <summary>
        /// 跟台核销
        /// </summary>
        [Description("跟台核销")]
        StageSurgeryWriteOff = 6,

        /// <summary>
        /// 服务订单
        /// </summary>
        [Description("服务订单")]
        ServiceFee = 7,
    }
    /// <summary>
    /// 订单来源枚举
    /// </summary>
    public enum SaleSourceEnum
    {
        /// <summary>
        /// 平台
        /// </summary>
        [Description("平台")]
        Platform = 10,

        /// <summary>
        /// 订货系统
        /// </summary>
        [Description("订货系统")]
        OrderingSystem = 20,


        /// <summary>
        /// 集成平台
        /// </summary>
        [Description("集成平台")]
        Integration = 25,

        /// <summary>
        /// 项目通道
        /// </summary>
        [Description("项目通道")]
        Project = 30,

        /// <summary>
        /// 数据初始化
        /// </summary>
        [Description("数据初始化")]
        DataInit = 40,

        /// <summary>
        /// SPD
        /// </summary>
        [Description("SPD")]
        Spd = 60,

        /// <summary>
        /// 爱尔眼科/三方
        /// </summary>
        [Description("爱尔眼科/三方")]
        Eye = 70,

        /// <summary>
        /// 服务商客户端
        /// </summary>
        [Description("服务商客户端")]
        PucClient = 80,

        /// <summary>
        /// 暂存实盘
        /// </summary>
        [Description("暂存实盘")]
        TempCheck = 90,

        /// <summary>
        /// 爱尔眼科/自采
        /// </summary>
        [Description("爱尔眼科/自采")]
        EyeMark = 100,

        /// <summary>
        /// 阳光采购
        /// </summary>
        [Description("阳光采购")]
        SunPurchase = 110,

        /// <summary>
        /// 集团业务
        /// </summary>
        [Description("集团业务")]
        GroupBusiness = 120,

        /// <summary>
        /// 辉图
        /// </summary>
        [Description("辉图")]
        HT = 130,

        /// <summary>
        /// 金域
        /// </summary>
        [Description("金域")]
        Kingmed = 140,

        /// <summary>
        /// 爱朋
        /// </summary>
        [Description("爱朋")]
        AP = 150,

        /// <summary>
        /// 旺店通
        /// </summary>
        [Description("旺店通")]
        Wangdian = 160,

        /// <summary>
        /// 返利
        /// </summary>
        [Description("返利")]
        Rebate = 170,

        /// <summary>
        /// 集团经销
        /// </summary>
        [Description("集团经销")]
        GroupDistribution = 180,
    }
    public enum RecognizeReceiveItemStatusEnum
    {
        [Description("已撤销")]
        Canceled = -1,

        [Description("草稿")]
        WaitSubmit = 0,
        /// <summary>
        /// 审批中
        /// </summary>
        [Description("审批中")]
        Auditing = 1,

        //已完成
        [Description("已完成")]
        Completed = 99,

        [Description("部分撤销")]
        PartCanceled = -2,

        /// <summary>
        /// 待执行
        /// </summary>
        [Description("待执行")]
        waitExecuteCount = 2
    }

    public enum RecognizeReceiveClassifyEnum
    {
        [Description("货款")]
        Goods = 1,

        [Description("暂收款")]
        Temp = 2,

    }
    public enum RecognizeReceiveDetailClassifyEnum
    {
        [Description("租金")]
        Rent = 1,

        [Description("商品款")]
        Goods = 2,

        [Description("服务费")]
        ServiceFee = 3,

    }
    public enum IsNoNeedInvoiceEnum
    {
        //不需要
        [Description("不需要")]
        NoNeed = 1,

        //需要
        [Description("需要")]
        Need = 0
    }
    /// <summary>
    /// 认款类型
    /// </summary>
    public enum RecognizeReceiveTypeEnum
    {
        [Description("销售回款")]
        SaleReturn = 100,

        [Description("预收款")]
        Prepayment = 101,

        [Description("退采购付款")]
        RefundPurchasePayment = 102,

        [Description("赔款")]
        Reparation = 108,

        [Description("押金")]
        Deposit = 109,

        [Description("未知")]
        Unknown = 998,

        [Description("其他")]
        Other = 999,
    }

    /// <summary>
    /// 付款单类型
    /// </summary>
    public enum PaymentTypeEnum
    {
        /// <summary>
        /// 正常付款
        /// </summary>
        [Description("正常付款")]
        Normal = 0,

        [Description("预付")]
        Prepay = 1,
        /// <summary>
        /// 手续费
        /// </summary>
        [Description("手续费")]
        Charge = 2,

        /// <summary>
        /// 负数付款
        /// </summary>
        [Description("负数付款")]
        Minus = 3,

        /// <summary>
        /// 应收票据转付款
        /// </summary>
        [Description("应收票据转付款")]
        Accepttopay = 4,

        /// <summary>
        /// 应付票据
        /// </summary>
        [Description("应付票据")]
        Billpayable = 5,

        /// <summary>
        /// 关税
        /// </summary>
        [Description("关税")]
        Tariff = 6,

        /// <summary>
        /// 进口增值税
        /// </summary>
        [Description("进口增值税")]
        ImportAddedTax = 7,

    }

    /// <summary>
    /// 应付执行计划明细状态
    /// </summary>
    public enum DebtDetailStatusEnum
    {
        /// <summary>
        /// 待执行
        /// </summary>
        [Description("待执行")]
        WaitExecute = 0,

        /// <summary>
        /// 已完成
        /// </summary>
        [Description("已完成")]
        Completed = 99
    }
    /// <summary>
    /// 批量付款单状态
    /// </summary>
    public enum CustomizeInvoiceStatusEnum
    {
        [Description("待提交")]
        WaitSubmit = 0,
        /// <summary>
        /// 审批中
        /// </summary>
        [Description("审批中")]
        Auditing = 1,
        //待开票
        [Description("待开票")]
        WaitInvoice = 2,
        //已作废
        [Description("已作废")]
        Cancel = 9,
        //已开票
        [Description("已开票")]
        Completed = 99
    }
    /// <summary>
    /// 批量付款单状态
    /// </summary>
    public enum PaymentAutoItemStatusEnum
    {
        [Description("待提交")]
        WaitSubmit = 0,
        /// <summary>
        /// 审批中
        /// </summary>
        [Description("审批中")]
        Auditing = 1,
        //待付款
        [Description("待付款")]
        WaitExecute = 2,

        //已完成
        [Description("已完成")]
        Completed = 99
    }
    /// <summary>
    /// 应付类型
    /// </summary>
    public enum DebtTypeEnum
    {
        [Description("修订应付")]
        revise = 1,

        [Description("初始应付")]
        origin = 2,

        [Description("退回应付")]
        _return = 3,

        [Description("购货应付(寄售)")]
        order = 4,

        [Description("购货应付(经销)")]
        selforder = 5,

        [Description("经销退回应付")]
        selfreturn = 6,

        [Description("购买应付")]
        purchase = 7,

        [Description("经销修订应付")]
        selfrevise = 8,

        [Description("购货应付(定价寄售)")]
        orderbulk = 9,

        [Description("返利应付")]
        rebate = 10,

        [Description("购货应付(集团寄售)")]
        grouporder = 11,

        [Description("垫资应付")]
        funded = 12,

        [Description("设备应付")]
        equipment = 13,

        [Description("寄售报损应付")]
        consignmentloss = 14,

        [Description("费用类应付")]
        expenses = 15,

        [Description("服务费应付")]
        servicefee = 16,

        [Description("寄售退回应付")]
        consignmentreturn = 17,

        [Description("SPD服务费")]
        spdservicefee = 18,

        [Description("纯代服务费")]
        pureservicefee = 19,

        [Description("仓储费")]
        storagenservicefee = 20,

        [Description("核心平台软件费")]
        coreplatformservicefee = 21,

        [Description("价外服务费")]
        outservicefee = 22,

        [Description("换货出库转退货应付")]
        exchangeback = 23,

        [Description("损失确认应付")]
        lossrecognition = 24,

        [Description("服务费修订应付")]
        servicefeeRevise = 25,
    }

    /// <summary>
    /// 应收类型
    /// </summary>
    public enum CreditTypeEnum
    {
        [Description("修订应收")]
        revise = 1,

        [Description("初始应收")]
        origin = 2,

        [Description("退回应收")]
        _return = 3,

        [Description("销售应收")]
        sale = 4,

        [Description("出库应收")]
        outstore = 5,

        [Description("经销销售应收")]
        selforder = 6,

        [Description("经销退回应收")]
        selfreturn = 7,

        [Obsolete]
        [Description("购买应收")]
        purchase = 8,

        [Description("经销修订应收")]
        selfrevise = 9,

        [Description("返利应收")]
        rebate = 10,

        [Description("设备应收")]
        equipment = 11,

        [Description("服务费应收")]
        servicefee = 12,

        [Description("服务费修订应收")]
        servicefeerevise = 13,

        [Description("损失确认应收")]
        lossrecognition = 14,
    }

    /// <summary>
    /// 发票应收关系表标记枚举
    /// </summary>
    public enum InvoiceCreditMarkEnum
    {
        [Description("手动调整应收")]
        Adjust = 1,
    }

    /// <summary>
    /// 销售应收子类型：个人消费者；平台
    /// </summary>
    public enum CreditSaleSubTypeEnum
    {
        [Description("个人消费者")]
        personal = 1,
        [Description("平台")]
        platform = 2,
    }


    /// <summary>
    /// 冲销状态
    /// </summary>
    public enum AbatedStatusEnum
    {
        /// <summary>
        /// 未冲销
        /// </summary>
        [Description("未冲销")]
        NonAbate = 0,
        /// <summary>
        /// 已冲销
        /// </summary>
        [Description("已冲销")]
        Abated = 1
    }

    /// <summary>
    /// 开票状态
    /// </summary>
    public enum InvoiceStatusEnum
    {
        [Description("未开票")]
        noninvoice = 0,
        [Description("已开票")]
        invoiced = 1
    }
    /// <summary>
    /// 产品与业务结合后相关的状态枚举
    /// </summary>
    public enum ProductBusinessEnum
    {
        [Description("可添加")]
        CanSubmit = 0,
        [Description("需首营")]
        NeedFirstProduct = 1,
        [Description("需经营关系")]
        NeedBusinessRelationship = 2
    }
    public enum PurchaseStatusEnums
    {
        /// <summary>
        /// 草稿
        /// </summary>
        [Description("临时草稿")]
        Draft = 1,
        /// <summary>
        /// 待审核
        /// </summary>
        [Description("待审核")]
        AwaitAudit = 2,
        /// <summary>
        /// 待入库
        /// </summary>
        [Description("待入库")]
        WaitStoreIn = 3,
        ///// <summary>
        ///// 已审核
        ///// </summary>
        //[Description("已审核")]
        //Audited = 4,
        /// <summary>
        /// 已完成
        /// </summary>
        [Description("已完成")]
        Finished = 5
    }
    /// <summary>
    /// 认款类型
    /// </summary>
    public enum RecognizeTypeEnums
    {
        /// <summary>
        /// 发票
        /// </summary>
        [Description("发票")]
        Invoice = 1,
        /// <summary>
        /// 订单
        /// </summary>
        [Description("订单")]
        Orderno = 2,
        /// <summary>
        /// 初始应收
        /// </summary>
        [Description("初始应收")]
        Credit = 3,
    }

    public static class EnumExtension
    {
        /// <summary>
        /// 获取枚举的Description特性的描述
        /// </summary>
        /// <param name="enumObj"></param>
        /// <returns></returns>

        public static string GetDescription(this Enum enumObj)
        {
            string description = string.Empty;
            try
            {
                if (enumObj != null)
                {
                    Type enumType = enumObj.GetType();
                    DescriptionAttribute desAttr = null;

                    FieldInfo field = enumType.GetField(Enum.GetName(enumType, enumObj));
                    desAttr = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));

                    if (desAttr != null)
                    {
                        description = desAttr.Description;
                    }
                }
            }
            catch
            {
                return string.Empty;
            }

            return description;

        }

        /// <summary>
        /// 将枚举转换为Dictionary Key为枚举值 Value为枚举名称
        /// </summary>
        /// <param name="obj">枚举</param>
        /// <returns></returns>
        public static Dictionary<int, string> EnumAsDictionary(this Enum obj)
        {
            Dictionary<int, string> dic = new Dictionary<int, string>();

            foreach (int item in Enum.GetValues(obj.GetType()))
            {
                dic.Add(item, Enum.GetName(obj.GetType(), item));
            }

            return dic;
        }

        /// <summary>
        /// 将枚举转换为Dictionary Key为枚举值 Value为枚举描述或名称
        /// </summary>
        /// <param name="obj">枚举</param>
        /// <param name="isGetDescription">是否获取枚举描述</param>
        /// <returns>Dictionary&lt;int, string&gt;</returns>
        public static Dictionary<int, string> EnumAsDictionary(this Enum obj, bool isGetDescription)
        {
            Type type = obj.GetType();

            if (isGetDescription)
            {
                Dictionary<int, string> dic = new Dictionary<int, string>();

                foreach (int item in Enum.GetValues(type))
                {
                    object[] atts = type.GetField(Enum.GetName(type, item)).GetCustomAttributes(typeof(DescriptionAttribute), false);

                    if (atts != null && atts.Any())
                    {
                        if (!string.IsNullOrEmpty(((DescriptionAttribute)atts[0]).Description))
                        {
                            dic.Add(item, ((DescriptionAttribute)atts[0]).Description);
                        }
                        else
                        {
                            dic.Add(item, Enum.GetName(type, item));
                        }
                    }
                    else
                    {
                        dic.Add(item, Enum.GetName(type, item));
                    }
                }

                return dic;
            }
            else
            {
                return EnumAsDictionary(obj);
            }
        }

        /// <summary>
        /// 根据值得到中文备注
        /// </summary>
        /// <param name="e"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string GetEnumDesc(this Type e, int? value)
        {
            FieldInfo[] fields = e.GetFields();
            for (int i = 1, count = fields.Length; i < count; i++)
            {
                if ((int)System.Enum.Parse(e, fields[i].Name) == value)
                {
                    DescriptionAttribute[] EnumAttributes =
                                (DescriptionAttribute[])fields[i].GetCustomAttributes(typeof(DescriptionAttribute), false);
                    if (EnumAttributes.Length > 0)
                    {
                        return EnumAttributes[0].Description;
                    }
                }
            }
            return "";
        }
    }

    public enum AdvancePayModeEnum
    {
        /// <summary>
        /// 不使用额度
        /// </summary>
        [Description("不使用额度")]
        NotUseQuota = 0,
        /// <summary>
        /// 使用额度
        /// </summary>
        [Description("使用额度")]
        UseQuota = 1

    }
    public enum UnitTypeEnum
    {
        /// <summary>
        /// 平台公司
        /// </summary>
        [Description("平台公司")]
        Company = 1,

        /// <summary>
        /// 供应商
        /// </summary>
        [Description("供应商")]
        Agent = 2,

        /// <summary>
        /// 业务单元
        /// </summary>
        [Description("业务单元")]
        Service = 3,


        /// <summary>
        /// 医院
        /// </summary>
        [Description("医院")]
        Hospital = 4,

        /// <summary>
        /// 生产厂家
        /// </summary>
        [Description("生产厂家")]
        Produce = 5
    }

    public enum StatusEnum
    {
        [Description("全部")]
        all = -1,
        [Description("待提交")]
        waitSubmit = 0,
        [Description("待审核")]
        waitAudit = 1,
        [Description("已拒绝")]
        Refuse = 66,
        [Description("已完成")]
        Complate = 99,
        [Description("我的审批")]
        My = 5000,
    }
    public enum PreCustomizeInvoiceItemStatusEnum
    {
        [Description("全部")]
        all = -1,
        [Description("待提交")]
        waitSubmit = 0,
        [Description("已提交")]
        waitAudit = 1,
        [Description("已开票待关联")]
        InvoicedNonUnion = 98,
        [Description("已关联应收单")]
        Complate = 99,
    }

    public enum SPDStatusEnum
    {
        [Description("全部")]
        all = -1,
        [Description("待提交")]
        waitSubmit = 0,
        [Description("待审核")]
        waitAudit = 1,
        [Description("已驳回")]
        Reject = 88,
        [Description("已完成")]
        Complate = 99,
    }

    public enum SunPurchaseStatusEnum
    {
        [Description("全部")]
        all = -1,
        [Description("待填报")]
        waitSubmit = 0,
        [Description("已忽略")]
        Ignore = 88,
        [Description("已完成")]
        Complate = 99,
    }

    /// <summary>
    /// 发票类型
    /// </summary>
    public enum InvoiceTypeEnum
    {
        /// <summary>
        /// 电子普通发票
        /// </summary>
        [Description("电子普通发票")]
        DZUniversal = 1,

        /// <summary>
        /// 电子专用发票
        /// </summary>
        [Description("电子专用发票")]
        DZSpecial = 2,

        /// <summary>
        /// 纸质普通发票
        /// </summary>
        [Description("纸质普通发票")]
        ZZUniversal = 3,

        /// <summary>
        /// 纸质专用发票
        /// </summary>
        [Description("纸质专用发票")]
        ZZSpecial = 4,

        /// <summary>
        /// 增值税普通发票(卷票)
        /// </summary>
        [Description("增值税普通发票(卷票)")]
        RollTicket = 5,

        /// <summary>
        /// 数电票(增值税专用发票)
        /// </summary>
        [Description("数电票(增值税专用发票)")]
        DigitalCircuitTiket = 6,

        /// <summary>
        /// 数电票(普通发票)
        /// </summary>
        [Description("数电票(普通发票)")]
        DigitalCircuitUniversal = 7,
    }

    /// <summary>
    /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期  4=验收账期  5=质保账期
    /// </summary>
    public enum AccountPeriodTypeEnum
    {
        /// <summary>
        /// 回款
        /// </summary>
        [Description("回款")]
        Repayment = 0,
        /// <summary>
        /// 入库
        /// </summary>
        [Description("入库")]
        StoreIn = 1,
        /// <summary>
        /// 销售
        /// </summary>
        [Description("销售")]
        Sale = 2,
        /// <summary>
        /// 预付
        /// </summary>
        [Description("预付")]
        ProbablyPay = 3,

        /// <summary>
        /// 验收账期
        /// </summary>
        [Description("验收")]
        AcceptancePeriod = 4,

        /// <summary>
        /// 质保账期
        /// </summary>
        [Description("质保")]
        WarrantyPeriod = 5,

    }

    /// <summary>
    /// 订阅日志来源枚举
    /// </summary>
    public enum SubLogSourceEnum
    {
        /// <summary>
        /// 出库
        /// </summary>
        [Description("出库")]
        StoreOut = 1,
        /// <summary>
        /// 入库
        /// </summary>
        [Description("入库")]
        StoreIn = 2,
        /// <summary>
        /// 采购
        /// </summary>
        [Description("采购")]
        Purchase = 3,
        /// <summary>
        /// 销售
        /// </summary>
        [Description("销售")]
        Sale = 4,
        /// <summary>
        /// 销售
        /// </summary>
        [Description("出库签收")]
        Sign = 5,
        /// <summary>
        /// 金蝶回调
        /// </summary>
        [Description("金蝶回调")]
        KingdeeCallBack = 6,
        /// <summary>
        /// 推送金蝶
        /// </summary>
        [Description("推送金蝶")]
        PushKingdee = 7,
        /// <summary>
        /// 推送SPD
        /// </summary>
        [Description("推送SPD")]
        SPD = 8,
        /// <summary>
        /// 暂存
        /// </summary>
        [Description("暂存")]
        Stage = 9,


        /// <summary>
        /// 垫资应收
        /// </summary>
        [Description("垫资应收")]
        AdvanceCredit = 10,
        /// <summary>
        /// 红字确认单编号查询
        /// </summary>
        [Description("红字确认单编号查询")]
        RedConfirmationFormNumber = 11,
        /// <summary>
        /// 项目
        /// </summary>
        [Description("项目")]
        Project = 12,
        /// <summary>
        /// SPDApprove
        /// </summary>
        [Description("SPD发票审核")]
        SPDApprove = 13,
        /// <summary>
        /// 发票更改应收
        /// </summary>
        [Description("发票更改应收")]
        ChangeInvoiceCreditRelationship = 14,
        /// <summary>
        /// 撤销认款
        /// </summary>
        [Description("撤销认款")]
        CancelReceiveInvoice = 15,
        /// <summary>
        /// 分批确认收入
        /// </summary>
        [Description("分批确认收入")]
        PartialIncome = 16,
    }

    /// <summary>
    ///  购销身份
    /// </summary>
    public enum TradeIdentityEnum
    {
        [Description("我是销方")]
        Seller = 0,
        [Description("我是购方")]
        Buyer = 1
    }

    /// <summary>
    /// 红字确认单状态枚举
    /// </summary>
    public enum RedConfirmBillStatusEnum
    {
        [Description("无需确认")]
        无需确认 = 01,
        [Description("销方录入待购方确认")]
        销方录入待购方确认 = 02,
        [Description("购方录入待销方确认")]
        购方录入待销方确认 = 03,
        [Description("购销双方已确认")]
        购销双方已确认 = 04,
        [Description("作废(销方录入购方否认)")]
        销方录入购方否认 = 05,
        [Description("作废(购方录入销方否认)")]
        购方录入销方否认 = 06,
        [Description("作废(超72小时未确认)")]
        超72小时未确认 = 07,
        [Description("发起方撤销")]
        发起方撤销 = 08,
        [Description("作废(确认后撤销)")]
        确认后撤销 = 09,
        [Description("作废(异常凭证)")]
        异常凭证 = 10
    }

    /// <summary>
    /// 红字确认单开具发票枚举
    /// </summary>
    public enum InvoiceOpenStateEnum
    {
        [Description("已开具")]
        Y,
        [Description("未开具")]
        N
    }

    /// <summary>
    /// 红字确认单红冲原因枚举
    /// </summary>
    public enum RedReasonEnum
    {
        [Description("开票有误")]
        开票有误 = 01,
        [Description("销货退回")]
        销货退回 = 02,
        [Description("服务中止")]
        服务中止 = 03,
        [Description("销售折让")]
        销售折让 = 04,
    }

    /// <summary>
    /// 红字确认单录入方身份
    /// </summary>
    public enum EnterIdentityEnum
    {
        [Description("销方录入")]
        销方录入 = 0,
        [Description("购方录入")]
        购方录入 = 1,
    }

    /// <summary>
    /// 蓝字发票类型
    /// </summary>
    public enum OriginalInvoiceTypeEnum
    {
        [Description("数电票（普通发票）")]
        数电票 = 26,
        [Description("全电类型专用发票")]
        全电类型专用发票 = 27,
    }

    public enum RebateTypeOfKdEnum
    {

        [Description("平移返利")]
        A,
        [Description("指标返利")]
        B,
        [Description("补偿返利")]
        C
    }
    public enum NextRebateMethodEnum
    {
        [Description("发票")]
        A,
        [Description("优惠劵")]
        B,
    }
    public enum ProvisionTypeEnum
    {

        [Description("月末计提")]
        EndMonth = 1,
        [Description("月初冲回")]
        StartMonth = 2,
    }
    /// <summary>
    /// 开票类型
    /// </summary>
    public enum CustomizeInvoiceClassifyEnum
    {
        [Description("应收")]
        Credit = 1,

        [Description("预开票")]
        Pre = 2,

        [Description("初始应收")]
        InitCredit = 3,
    }

    public enum ReconciliationLetterEnum
    {
        [Description("带发票明细")]
        Invoce = 1,

        [Description("不带发票明细")]
        Credit = 2,
        [Description("货号明细")]
        Product = 3
    }
    public enum ServiceConfirmRevenuePlanModeEnum
    {
        /// <summary>
        /// 一次性生成
        /// </summary>
        [Description("一次性生成")]
        OneTimeGeneration = 0,

        /// <summary>
        /// 分期生成
        /// </summary>
        [Description("分期生成")]
        InstallmentGeneration = 1
    }

    /// <summary>
    /// 进项票状态枚举
    /// </summary>
    public enum InputBillStatusEnum
    {
        [Description("临时发票")]
        Temporary = 1,
        [Description("已提交")]
        Submitted = 2,
        [Description("正在匹配")]
        InMergeReconciliation = 3,
        [Description("匹配完成")]
        CompletedReconciliation = 4,
        [Description("已忽略")]
        Ignore = 9,
    }

    /// <summary>
    /// 进项票类型枚举
    /// </summary>
    public enum InputBillTypeEnum
    {
        //全部
        [Description("全部")]
        All = 0,
        [Description("普票")]
        Universal = 1,
        [Description("专票")]
        Special = 2
    }

    public enum MigrationDataTypeEnum
    {
        [Description("应收")]
        Credit = 1,
        [Description("应付")]
        Debt = 2,
    }
    public enum PriceSourceEnum
    {
        [Description("非集采")]
        CHANGE_APPLY = 0,
        [Description("集采")]
        TEMP_APPLY = 1,
        [Description("其他")]
        OPERATION_APPLY = 2,
    }

    public enum RefundStatusEnum
    {
        [Description("全部")]
        all = -1,
        [Description("待提交")]
        waitSubmit = 0,
        [Description("待审核")]
        waitAudit = 1,
        [Description("已拒绝")]
        Refuse = 66,
        [Description("金蝶审核通过")]
        KindeeComplate = 98,
        [Description("已完成")]
        Complate = 99
    }
    /// <summary>
    /// 盘点状态
    /// </summary>
    public enum InventoryStatus
    {
        /// <summary>
        /// 未开始总盘点
        /// </summary>
        [Description("未开始总盘点")]
        None = -1,
        /// <summary>
        /// 待库存盘点
        /// </summary>
        [Description("待库存盘点")]
        WaintInventoryStocktaking = 0,
        /// <summary>
        /// 库存盘点中
        /// </summary>
        [Description("库存盘点中")]
        InventoryStocktaking = 1,
        /// <summary>
        /// 库存盘点完成
        /// </summary>
        [Description("库存盘点完成")]
        InventoryStocktakingFinished = 2,
        /// <summary>
        /// 全部完成
        /// </summary>
        [Description("全部完成")]
        AllFinished = 99
    }

    public enum LossRecognitionDetailTypeEnum
    {
        [Description("应收")]
        Credit = 1,
        [Description("应付")]
        Debt = 2,
    }
    public enum ConfirmPaymentDateModeEnum
    {
        [Description("按固定天数")]
        Days = 1,
        [Description("按固定日期")]
        Date = 2
    }
    public enum RecognizeReceiveDetailEnum
    {
        //正常
        [Description("正常")]
        Normal = 1,
        //撤销
        [Description("撤销")]
        Cancel = 2,
        //部分撤销
        [Description("部分撤销")]
        PartCancel = 3,
    }

    public enum CreditRecordItemClassifyEnum
    {
        /// <summary>
        /// 应收盘点
        /// </summary>
        [Description("应收盘点")]
        Credit = 0,
        /// <summary>
        /// 已签收未开票
        /// </summary>
        [Description("已签收未开票")]
        SignedNonInvoiced = 1
    }

    /// <summary>
    /// 合并进项发票状态枚举
    /// </summary>
    public enum MergeInputBillStatusEnum
    {
        /// <summary>
        /// 临时
        /// </summary>
        [Description("临时")]
        Temporary = 1,

        /// <summary>
        /// 正在匹配
        /// </summary>
        [Description("正在匹配")]
        InMergeReconciliation = 3,

        /// <summary>
        /// 匹配完成
        /// </summary>
        [Description("匹配完成")]
        CompletedReconciliation = 4,

        /// <summary>
        /// 已提交
        /// </summary>
        [Description("已提交")]
        Submitted = 99,

        /// <summary>
        /// 忽略
        /// </summary>
        [Description("忽略")]
        Ignore = 9,
    }

    public enum AdvancePaymentStatusEnum
    {
       [Description("全部")]
        All = -1,
        [Description("待提交")]
        WaitSubmit = 0,
        [Description("待审核")]
        WaitAudit = 1,
        [Description("已拒绝")]
        Refuse = 66,
        [Description("已完成")]
        Complate = 99,
        [Description("我的审批")]
        My = 5000,
    }

    /// <summary>
    /// 修订范围
    /// </summary>
    public enum ReviseRangeEnum
    {
        /// <summary>
        /// 只修成本
        /// </summary>
        [Description("只修成本")]
        Cost = 1,

        /// <summary>
        /// 成本售价同修
        /// </summary>
        [Description("成本售价同修")]
        CostAndPrice = 2,

        /// <summary>
        /// 只修价格
        /// </summary>
        [Description("只修价格")]
        Price = 3
    }


}