using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Application.DTOs.Credits
{
    /// <summary>
    /// 应收冲销状态检查输入参数
    /// </summary>
    public class CreditAbatementCheckInput
    {
        /// <summary>
        /// 销售订单号数组（支持批量查询）
        /// </summary>
        public List<string> SaleOrderNos { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; } 
    }
}
