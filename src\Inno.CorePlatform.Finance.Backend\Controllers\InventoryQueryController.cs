﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 盘点查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InventoryQueryController : BaseController
    {
        private readonly IInventoryQueryService _inventoryQueryService;
        private readonly ILogger<InventoryQueryController> _logger;
        

        public InventoryQueryController(IInventoryQueryService inventoryQueryService, ILogger<InventoryQueryController> logger, ISubLogService subLog) : base(subLog)
        {
            _inventoryQueryService = inventoryQueryService;
            _logger = logger;
        }

        /// <summary>
        ///   获取应收盘点列表
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetCreditRecordList")]
        public async Task<BaseResponseData<PageResponse<CreditRecordOutputDto>>> GetCreditRecordListAsync([FromBody] RecordBaseQueryDto queryDto)
        {
            try
            {
                queryDto.UserId = CurrentUser.Id.Value;
                var (list, count) = await _inventoryQueryService.GetCreditRecordListAsync(queryDto);
                return new BaseResponseData<PageResponse<CreditRecordOutputDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<CreditRecordOutputDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        ///  获取应收盘点详情列表
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetCreditRecordDetailList")]
        public async Task<BaseResponseData<PageResponse<CreditRecordDetailDto>>> GetCreditRecordDetailListAsync([FromBody] DetailRecordBaseQueryDto queryDto)
        {
            try
            {
                queryDto.UserId = CurrentUser.Id.Value;
                var (list, count) = await _inventoryQueryService.GetCreditRecordDetailListAsync(queryDto);
                return new BaseResponseData<PageResponse<CreditRecordDetailDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<CreditRecordDetailDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///   获取应付盘点列表
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetDebtRecordList")]
        public async Task<BaseResponseData<PageResponse<DebtRecordOutputDto>>> GetDebtRecordListAsync([FromBody] RecordBaseQueryDto queryDto)
        {
            try
            {
                queryDto.UserId = CurrentUser.Id.Value;
                var (list, count) = await _inventoryQueryService.GetDebtRecordListAsync(queryDto);
                return new BaseResponseData<PageResponse<DebtRecordOutputDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<DebtRecordOutputDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///   获取应付盘点详情
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetDebtRecordDetailList")]
        public async Task<BaseResponseData<PageResponse<DebtRecordDetailDto>>> GetDebtRecordDetailListAsync([FromBody] DetailRecordBaseQueryDto queryDto)
        {
            try
            {
                var (list, count) = await _inventoryQueryService.GetDebtRecordDetailListAsync(queryDto);
                return new BaseResponseData<PageResponse<DebtRecordDetailDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<DebtRecordDetailDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///  获取收款盘点列表
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetPaymentRecordList")]
        public async Task<BaseResponseData<PageResponse<PaymentRecordOutputDto>>> GetPaymentRecordListAsync([FromBody] RecordBaseQueryDto queryDto)
        {
            try
            {
                queryDto.UserId = CurrentUser.Id.Value;
                var (list, count) = await _inventoryQueryService.GetPaymentRecordListAsync(queryDto);
                return new BaseResponseData<PageResponse<PaymentRecordOutputDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<PaymentRecordOutputDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///  获取收款盘点详情
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetPaymentRecordDetailList")]
        public async Task<BaseResponseData<PageResponse<PaymentRecordDetailDto>>> GetPaymentRecordDetailListAsync([FromBody] DetailRecordBaseQueryDto queryDto)
        {
            try
            {
                var (list, count) = await _inventoryQueryService.GetPaymentRecordDetailListAsync(queryDto);
                return new BaseResponseData<PageResponse<PaymentRecordDetailDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<PaymentRecordDetailDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        #region
        /// <summary>
        ///  获取垫资盘点列表
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetAdvanceRecordList")]
        public async Task<BaseResponseData<PageResponse<AdvanceRecordOutputDto>>> GetAdvanceRecordListAsync([FromBody] RecordBaseQueryDto queryDto)
        {
            try
            {
                queryDto.UserId = CurrentUser.Id.Value;
                var (list, count) = await _inventoryQueryService.GetAdvanceRecordListAsync(queryDto);
                return new BaseResponseData<PageResponse<AdvanceRecordOutputDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<AdvanceRecordOutputDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///  获取垫资盘点详情
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetAdvanceRecordDetailList")]
        public async Task<BaseResponseData<PageResponse<AdvanceRecordDetailDto>>> GetAdvanceRecordDetailListAsync([FromBody] DetailRecordBaseQueryDto queryDto)
        {
            try
            {
                var (list, count) = await _inventoryQueryService.GetAdvanceRecordDetailListAsync(queryDto);
                return new BaseResponseData<PageResponse<AdvanceRecordDetailDto>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<AdvanceRecordDetailDto>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///垫资盘点导出（按盘点单Ids）
        /// </summary>
        /// <returns></returns>
        [HttpPost("AdvanceRecord/Export")]
        public async Task<IActionResult> AdvanceRecordExport([FromBody] AdvanceRecordExportQueryDto input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                input.CurrentUserName = CurrentUser.UserName;
                var dataList = await _inventoryQueryService.AdvanceDownLoadByIds(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "垫资申请单号");
                    //worksheet.SetValue(1, columnIndex++, "单据日期");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "业务类型");
                    worksheet.SetValue(1, columnIndex++, "医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于核准医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于全流程接管医院");
                    worksheet.SetValue(1, columnIndex++, "是否发票入账");
                    worksheet.SetValue(1, columnIndex++, "销售账期天数");
                    worksheet.SetValue(1, columnIndex++, "回款账期天数");
                    worksheet.SetValue(1, columnIndex++, "垫资天数");
                    worksheet.SetValue(1, columnIndex++, "年化垫资利率(%)");
                    worksheet.SetValue(1, columnIndex++, "折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "基础折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "SPD折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "实际供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "垫资比例(%)");
                    worksheet.SetValue(1, columnIndex++, "供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "合计折扣(%)");
                    //--
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "应收日期");
                    worksheet.SetValue(1, columnIndex++, "开票日期");
                    worksheet.SetValue(1, columnIndex++, "应收金额");

                    worksheet.SetValue(1, columnIndex++, "应付单号");
                    worksheet.SetValue(1, columnIndex++, "应付日期");
                    worksheet.SetValue(1, columnIndex++, "应付金额");

                    worksheet.SetValue(1, columnIndex++, "收款单号");
                    worksheet.SetValue(1, columnIndex++, "收款日期");
                    worksheet.SetValue(1, columnIndex++, "收款金额");

                    worksheet.SetValue(1, columnIndex++, "付款单号");
                    worksheet.SetValue(1, columnIndex++, "付款日期");
                    worksheet.SetValue(1, columnIndex++, "预计付款日期");
                    worksheet.SetValue(1, columnIndex++, "付款金额");

                    worksheet.SetValue(1, columnIndex++, "计划回款日期");
                    worksheet.SetValue(1, columnIndex++, "垫资应收到期时间");
                    worksheet.SetValue(1, columnIndex++, "逾期天数");
                    worksheet.SetValue(1, columnIndex++, "是否逾期");
                    worksheet.SetValue(1, columnIndex++, "逾期利息");
                    worksheet.SetValue(1, columnIndex++, "提前回款利息");
                    worksheet.SetValue(1, columnIndex++, "资金占用余额");
                    worksheet.SetValue(1, columnIndex++, "基础毛利");
                    worksheet.SetValue(1, columnIndex++, "垫资利息收入");
                    worksheet.SetValue(1, columnIndex++, "合计毛利");
                    worksheet.SetValue(1, columnIndex++, "校验");
                    worksheet.SetValue(1, columnIndex++, "开票后实际支付(天数)");
                    worksheet.SetValue(1, columnIndex++, "提示(放款风险)");
                    worksheet.SetValue(1, columnIndex++, "供应商名称");
                    worksheet.SetValue(1, columnIndex++, "批量付款单号");
                    //worksheet.SetValue(1, columnIndex++, "收款单类型");
                    //worksheet.SetValue(1, columnIndex++, "税率折扣");

                    var index = 2;
                    dataList.Data.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.AdvanceBusinessApplyCode);
                        //worksheet.SetValue(index, columnIndex++, p.BillDate.ToString("yyyy-MM-dd"));
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, "其它");
                        worksheet.SetValue(index, columnIndex++, p.HospitalName);
                        worksheet.SetValue(index, columnIndex++, p.IsCheckedHospital >= 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsProcessAllMage >= 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsInvoice == 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.AccountPeriod);
                        worksheet.SetValue(index, columnIndex++, p.ReceivePeriod);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceDays);
                        worksheet.SetValue(index, columnIndex++, p.RateOfYear);
                        worksheet.SetValue(index, columnIndex++, p.Discount);
                        worksheet.SetValue(index, columnIndex++, p.BaseDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SPDDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ActualFinanceDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.TotalDiscounts);


                        worksheet.SetValue(index, columnIndex++, p.CreditCode);
                        worksheet.SetValue(index, columnIndex++, p.CreditDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.CreditValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.DebtCode);
                        worksheet.SetValue(index, columnIndex++, p.DebtDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.DebtValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ReceiveCode);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.PaymentCode);
                        worksheet.SetValue(index, columnIndex++, p.PaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ExpectPaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.PaymentAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ExpectReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceExpireTime);
                        worksheet.SetValue(index, columnIndex++, p.OverdueDays);
                        worksheet.SetValue(index, columnIndex++, p.OverdueStatus);
                        worksheet.SetValue(index, columnIndex++, p.OverdueInterest);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceReceiveInterest);
                        worksheet.SetValue(index, columnIndex++, p.FundUsedValue);
                        worksheet.SetValue(index, columnIndex++, p.BasicGrossProfit);
                        worksheet.SetValue(index, columnIndex++, p.IntrestIncome);
                        worksheet.SetValue(index, columnIndex++, p.TotalGrossProfit);
                        worksheet.SetValue(index, columnIndex++, p.Verify);
                        worksheet.SetValue(index, columnIndex++, p.ActualPaydays);
                        worksheet.SetValue(index, columnIndex++, p.HintRisk);
                        worksheet.SetValue(index, columnIndex++, p.AgentName);
                        worksheet.SetValue(index, columnIndex++, p.BatchpaymentCode);
                        //worksheet.SetValue(index, columnIndex++, p.SalesTaxRate);

                        index++;
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, $"导出错误: {ex.Message}");
            }
        }

        /// <summary>
        ///垫资盘点导出（旧版本，按公司条件查询）
        /// </summary>
        /// <returns></returns>
        [HttpPost("AdvanceRecord/ExportByCompany")]
        public async Task<IActionResult> AdvanceRecordExportByCompany([FromBody] RecordBaseQueryDto input)
        {
            try
            {
                input.page = 1;
                input.limit = int.MaxValue;
                input.UserId = CurrentUser.Id.Value;
                var dataList = await _inventoryQueryService.AdvanceDownLoad(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "垫资申请单号");
                    //worksheet.SetValue(1, columnIndex++, "单据日期");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "业务类型");
                    worksheet.SetValue(1, columnIndex++, "医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于核准医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于全流程接管医院");
                    worksheet.SetValue(1, columnIndex++, "是否发票入账");
                    worksheet.SetValue(1, columnIndex++, "销售账期天数");
                    worksheet.SetValue(1, columnIndex++, "回款账期天数");
                    worksheet.SetValue(1, columnIndex++, "垫资天数");
                    worksheet.SetValue(1, columnIndex++, "年化垫资利率(%)");
                    worksheet.SetValue(1, columnIndex++, "折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "基础折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "SPD折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "实际供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "垫资比例(%)");
                    worksheet.SetValue(1, columnIndex++, "供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "合计折扣(%)");
                    //--
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "应收日期");
                    worksheet.SetValue(1, columnIndex++, "开票日期");
                    worksheet.SetValue(1, columnIndex++, "应收金额");

                    worksheet.SetValue(1, columnIndex++, "应付单号");
                    worksheet.SetValue(1, columnIndex++, "应付日期");
                    worksheet.SetValue(1, columnIndex++, "应付金额");

                    worksheet.SetValue(1, columnIndex++, "收款单号");
                    worksheet.SetValue(1, columnIndex++, "收款日期");
                    worksheet.SetValue(1, columnIndex++, "收款金额");

                    worksheet.SetValue(1, columnIndex++, "付款单号");
                    worksheet.SetValue(1, columnIndex++, "付款日期");
                    worksheet.SetValue(1, columnIndex++, "预计付款日期");
                    worksheet.SetValue(1, columnIndex++, "付款金额");

                    worksheet.SetValue(1, columnIndex++, "计划回款日期");
                    worksheet.SetValue(1, columnIndex++, "垫资应收到期时间");
                    worksheet.SetValue(1, columnIndex++, "逾期天数");
                    worksheet.SetValue(1, columnIndex++, "是否逾期");
                    worksheet.SetValue(1, columnIndex++, "逾期利息");
                    worksheet.SetValue(1, columnIndex++, "提前回款利息");
                    worksheet.SetValue(1, columnIndex++, "资金占用余额");
                    worksheet.SetValue(1, columnIndex++, "基础毛利");
                    worksheet.SetValue(1, columnIndex++, "垫资利息收入");
                    worksheet.SetValue(1, columnIndex++, "合计毛利");
                    worksheet.SetValue(1, columnIndex++, "校验");
                    worksheet.SetValue(1, columnIndex++, "开票后实际支付(天数)");
                    worksheet.SetValue(1, columnIndex++, "提示(放款风险)");
                    worksheet.SetValue(1, columnIndex++, "供应商名称");
                    worksheet.SetValue(1, columnIndex++, "批量付款单号");
                    //worksheet.SetValue(1, columnIndex++, "收款单类型");
                    //worksheet.SetValue(1, columnIndex++, "税率折扣");

                    var index = 2;
                    dataList.Data.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.AdvanceBusinessApplyCode);
                        //worksheet.SetValue(index, columnIndex++, p.BillDate.ToString("yyyy-MM-dd"));
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, "其它");
                        worksheet.SetValue(index, columnIndex++, p.HospitalName);
                        worksheet.SetValue(index, columnIndex++, p.IsCheckedHospital >= 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsProcessAllMage >= 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsInvoice == 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.AccountPeriod);
                        worksheet.SetValue(index, columnIndex++, p.ReceivePeriod);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceDays);
                        worksheet.SetValue(index, columnIndex++, p.RateOfYear);
                        worksheet.SetValue(index, columnIndex++, p.Discount);
                        worksheet.SetValue(index, columnIndex++, p.BaseDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SPDDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ActualFinanceDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.TotalDiscounts);


                        worksheet.SetValue(index, columnIndex++, p.CreditCode);
                        worksheet.SetValue(index, columnIndex++, p.CreditDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.CreditValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.DebtCode);
                        worksheet.SetValue(index, columnIndex++, p.DebtDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.DebtValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ReceiveCode);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.PaymentCode);
                        worksheet.SetValue(index, columnIndex++, p.PaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ExpectPaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.PaymentAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ExpectReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceExpireTime);
                        worksheet.SetValue(index, columnIndex++, p.OverdueDays);
                        worksheet.SetValue(index, columnIndex++, p.OverdueStatus);
                        worksheet.SetValue(index, columnIndex++, p.OverdueInterest);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceReceiveInterest);
                        worksheet.SetValue(index, columnIndex++, p.FundUsedValue);
                        worksheet.SetValue(index, columnIndex++, p.BasicGrossProfit);
                        worksheet.SetValue(index, columnIndex++, p.IntrestIncome);
                        worksheet.SetValue(index, columnIndex++, p.TotalGrossProfit);
                        worksheet.SetValue(index, columnIndex++, p.Verify);
                        worksheet.SetValue(index, columnIndex++, p.ActualPaydays);
                        worksheet.SetValue(index, columnIndex++, p.HintRisk);
                        worksheet.SetValue(index, columnIndex++, p.AgentName);
                        worksheet.SetValue(index, columnIndex++, p.BatchpaymentCode);
                        //worksheet.SetValue(index, columnIndex++, p.SalesTaxRate);

                        index++;
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, $"导出错误: {ex.Message}");
            }
        }

        #endregion
        /// <summary>
        ///  获取付款盘点列表下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("PaymentDownLoad")]
        public async Task<IActionResult> PaymentDownLoadAsync([FromBody] RecordBaseQueryDto input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                var ret = await _inventoryQueryService.PaymentDownLoad(input);

                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "盘点单号";
                    worksheet.Cells[1, 2].Value = "盘点日期";
                    worksheet.Cells[1, 3].Value = "付款单号";
                    worksheet.Cells[1, 4].Value = "付款单日期";
                    worksheet.Cells[1, 5].Value = "公司";
                    worksheet.Cells[1, 6].Value = "供应商";
                    worksheet.Cells[1, 7].Value = "付款金额";
                    worksheet.Cells[1, 8].Value = "冲销金额";
                    worksheet.Cells[1, 9].Value = "余额";
                    #endregion

                    #region 数据
                    int row = 2;
                    if (ret != null && ret.Data != null && ret.Data.Any())
                    {
                        foreach (var item in ret.Data)
                        {
                            worksheet.Cells[row, 1].Value = item.Code;
                            worksheet.Cells[row, 2].Value = item.BillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 3].Value = item.PayCode;
                            worksheet.Cells[row, 4].Value = item.PaymentBillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 5].Value = item.CompanyName;
                            worksheet.Cells[row, 6].Value = item.AgentName;
                            worksheet.Cells[row, 7].Value = item.Vaule;
                            worksheet.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 8].Value = item.AbatedVaule;
                            worksheet.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 9].Value = item.Balance;
                            worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                            row++;
                        }
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        ///  获取应付款盘点列表下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DebtDownLoad")]
        public async Task<IActionResult> DebtDownLoadAsync([FromBody] RecordBaseQueryDto input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                var ret = await _inventoryQueryService.DebtDownLoad(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "盘点单号";
                    worksheet.Cells[1, 2].Value = "盘点日期";
                    worksheet.Cells[1, 3].Value = "应付单号";
                    worksheet.Cells[1, 4].Value = "应付单日期";
                    worksheet.Cells[1, 5].Value = "公司";
                    worksheet.Cells[1, 6].Value = "供应商";
                    worksheet.Cells[1, 7].Value = "客户";
                    worksheet.Cells[1, 8].Value = "应付金额";
                    worksheet.Cells[1, 9].Value = "冲销金额";
                    worksheet.Cells[1, 10].Value = "余额";
                    worksheet.Cells[1, 11].Value = "业务单元";
                    worksheet.Cells[1, 12].Value = "项目名称";
                    worksheet.Cells[1, 13].Value = "项目编号";
                    #endregion

                    #region 数据
                    int row = 2;
                    if (ret != null && ret.Data != null && ret.Data.Any())
                    {
                        foreach (var item in ret.Data)
                        {
                            worksheet.Cells[row, 1].Value = item.Code;
                            worksheet.Cells[row, 2].Value = item.BillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 3].Value = item.DebtCode;
                            worksheet.Cells[row, 4].Value = item.DebtBillDate.Value.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 5].Value = item.CompanyName;
                            worksheet.Cells[row, 6].Value = item.AgentName;
                            worksheet.Cells[row, 7].Value = item.CustomerName;
                            worksheet.Cells[row, 8].Value = Convert.ToDouble(item.Vaule);
                            worksheet.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 9].Value = item.AbatedVaule;
                            worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 10].Value = item.Balance;
                            worksheet.Cells[row, 10].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 11].Value = item.ServiceName;
                            worksheet.Cells[row, 12].Value = item.ProjectName;
                            worksheet.Cells[row, 13].Value = item.ProjectCode;
                            row++;
                        }
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        ///  获取应收款盘点列表下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreditDownLoad")]
        public async Task<IActionResult> CreditDownLoadAsync([FromBody] RecordBaseQueryDto input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                var ret = await _inventoryQueryService.CreditDownLoad(input);

                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "盘点单号";
                    worksheet.Cells[1, 2].Value = "盘点日期";
                    worksheet.Cells[1, 3].Value = "应收单号";
                    worksheet.Cells[1, 4].Value = "应收单日期";
                    worksheet.Cells[1, 5].Value = "公司";
                    worksheet.Cells[1, 6].Value = "客户";
                    worksheet.Cells[1, 7].Value = "应收金额";
                    worksheet.Cells[1, 8].Value = "冲销金额";
                    worksheet.Cells[1, 9].Value = "确认收入金额";
                    worksheet.Cells[1, 10].Value = "开票金额";
                    worksheet.Cells[1, 11].Value = "余额";
                    worksheet.Cells[1, 12].Value = "业务单元";
                    worksheet.Cells[1, 13].Value = "订单号";
                    worksheet.Cells[1, 14].Value = "核算部门名称";
                    worksheet.Cells[1, 15].Value = "核算部门id";
                    worksheet.Cells[1, 16].Value = "项目名称";
                    worksheet.Cells[1, 17].Value = "项目单号";
                    worksheet.Cells[1, 18].Value = "损失确认金额";
                    worksheet.Cells[1, 19].Value = "未冲销金额";
                    worksheet.Cells[1, 20].Value = "未确认收入金额";
                    worksheet.Cells[1, 21].Value = "未开票金额";
                    #endregion

                    #region 数据
                    int row = 2;
                    if (ret != null && ret.Data != null && ret.Data.Any())
                    {
                        foreach (var item in ret.Data)
                        {
                            worksheet.Cells[row, 1].Value = item.Code;
                            worksheet.Cells[row, 2].Value = item.BillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 3].Value = item.CreditCode;
                            worksheet.Cells[row, 4].Value = item.CreditBillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 5].Value = item.CompanyName;
                            worksheet.Cells[row, 6].Value = item.CustomerName;
                            worksheet.Cells[row, 7].Value = item.Vaule;
                            worksheet.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 8].Value = item.AbatedVaule;
                            worksheet.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 9].Value = item.SureIncomeAmount;
                            worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 10].Value = item.InvoiceAmount;
                            worksheet.Cells[row, 10].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 11].Value = item.Balance;
                            worksheet.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 12].Value = item.ServiceName;
                            worksheet.Cells[row, 13].Value = item.OrderNo;
                            worksheet.Cells[row, 14].Value = item.BusinessDeptFullName;
                            worksheet.Cells[row, 15].Value = item.BusinessDeptId;
                            worksheet.Cells[row, 16].Value = item.ProjectName;
                            worksheet.Cells[row, 17].Value = item.ProjectCode;
                            worksheet.Cells[row, 18].Value = item.LossValue;
                            worksheet.Cells[row, 18].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 19].Value = item.UnAbatedAmount;
                            worksheet.Cells[row, 19].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 20].Value = item.UnSureIncomeAmount;
                            worksheet.Cells[row, 20].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 21].Value = item.UnInvoiceAmount;
                            worksheet.Cells[row, 21].Style.Numberformat.Format = "#,##0.00";

                            row++;
                        }
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("CreditDownLoadByCoordinate")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync([FromBody] RecordBaseQueryDto query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _inventoryQueryService.ExportAsync(query);
        }

        /// <summary>
        /// 确认应收盘点
        /// </summary>
        /// <returns></returns>
        [HttpGet("ConfirmCreditRecordItem")]
        public async Task<BaseResponseData<string>> ConfirmCreditRecordItem(string id)
        {
            var userName = CurrentUser.UserName;
            return await _inventoryQueryService.ConfirmCreditRecordItem(id, userName);
        }

    }
}

