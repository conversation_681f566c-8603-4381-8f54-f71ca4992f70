using Inno.CorePlatform.Finance.Application.DTOs.UserWorkExchange;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.UserWorkExchange
{
    /// <summary>
    /// 用户工作交换服务接口
    /// </summary>
    public interface IUserWorkExchangeService
    {
        /// <summary>
        /// 处理用户工作交换事件
        /// </summary>
        /// <param name="eventDto">工作交换事件</param>
        /// <returns>转移结果</returns>
        Task<List<TransferResultDto>> ProcessUserWorkExchangeAsync(UserWorkExchangeEventDto eventDto);
        
        /// <summary>
        /// 更新批量付款单创建人
        /// </summary>
        Task<TransferResultDto> UpdatePaymentAutoItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新收款认领创建人
        /// </summary>
        Task<TransferResultDto> UpdateRecognizeReceiveItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新退款处理创建人
        /// </summary>
        Task<TransferResultDto> UpdateRefundItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新损失确认创建人
        /// </summary>
        Task<TransferResultDto> UpdateLossRecognitionItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新进项票创建人
        /// </summary>
        Task<TransferResultDto> UpdateInputBillCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新开票明细创建人
        /// </summary>
        Task<TransferResultDto> UpdateCustomizeInvoiceItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新预开票申请创建人
        /// </summary>
        Task<TransferResultDto> UpdatePreCustomizeInvoiceItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新提前付款垫资申请创建人
        /// </summary>
        Task<TransferResultDto> UpdateAdvancePaymentItemCreatorAsync(string oldCreator, string newCreator);
        
        /// <summary>
        /// 更新垫资申请单创建人
        /// </summary>
        Task<TransferResultDto> UpdateAdvanceBusinessApplyCreatorAsync(string oldCreator, string newCreator);
    }
}
