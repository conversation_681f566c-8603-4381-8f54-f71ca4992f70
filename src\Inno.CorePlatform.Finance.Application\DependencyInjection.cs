﻿using Inno.CorePlatform.Finance.Application.Helpers;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation;
using Inno.CorePlatform.Finance.Application.Services.MatchCache;
using Inno.CorePlatform.Finance.Application.Services.MatchLogic;
using Inno.CorePlatform.Finance.Application.Services.SubmitHandling;
using Inno.CorePlatform.Finance.Data;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application
{
    /// <summary>
    /// 依赖注入扩展
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// 添加应用服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // 注册缓存管理器
            services.AddSingleton<IMatchCacheManager, RedisMatchCacheManager>();

            // 注册匹配逻辑服务
            services.AddScoped<MatchLogicService>();

            // 注册数据库上下文接口
            services.AddScoped<IFinanceDbContext>(provider => provider.GetRequiredService<FinanceDbContext>());

            // 注册接口调用服务
            services.AddScoped<IInterfaceInvocationService, InterfaceInvocationService>();

            // 注册提交处理服务
            services.AddScoped<ISubmitHandlingService, SubmitHandlingService>();

            // 注册合并进项发票应用服务
            services.AddScoped<IMergeInputBillAppService, MergeInputBillAppService>();

            // 注册合并进项票辅助类
            services.AddScoped<MergeInputBillHelper>();




            return services;
        }
    }
}
